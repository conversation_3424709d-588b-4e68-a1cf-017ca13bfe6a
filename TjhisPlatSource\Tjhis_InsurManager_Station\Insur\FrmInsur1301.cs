﻿using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NM_Service.NMService;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Tjhis.InsurManager.Station.Comm;
using TjhisInterfaceInsurance;
using Utility.OracleODP;

namespace Tjhis.InsurManager.Station.Insur
{
    public partial class FrmInsur1301 : PlatCommon.SysBase.ParentForm
    {
        /// <summary>
        /// 是否是测试
        /// 测试使用 xxx_test的表
        /// </summary>
        private bool mIsTest = false;

        /// <summary>
        /// 接口代码
        /// </summary>
        private string is_interface_code = "";

        /// <summary>
        /// 目录类型数据
        /// </summary>
        private DataTable dt_type = new DataTable();

        /// <summary>
        /// 目录数据
        /// </summary>
        private DataTable dtDownLoadedData = new DataTable();

        /// <summary>
        /// 错误消息
        /// </summary>
        private string errorMessage = "";

        /// <summary>
        /// 拼音输入码字段名
        /// </summary>
        private const string INPUT_CODE_FIELDNAME = "INPUT_CODE";

        ///// <summary>
        ///// 下载后待插入的新数据（即原表中没有的数据）
        ///// </summary>
        //private DataTable dtInsertNewData = new DataTable();

        /// <summary>
        /// 更新原表数据的update语句字典
        /// </summary>
       // private Dictionary<string, string> sqlDict = new Dictionary<string, string>();

        /// <summary>
        /// 待更新的原表数据的数据行数量
        /// </summary>
        private int iOldDataModifycnt = 0;

        /// <summary>
        /// 待插入的下载新数据的数据行数量
        /// </summary>
        private int iNewDataInsertCnt = 0;

        /// <summary>
        /// 最大版本
        /// </summary>
        private string lastestVersion = "0";

        /// <summary>
        /// 清空列表
        /// </summary>
        private void clearData()
        {
            gcData.DataSource = null;
            gvData.BeginInit();
            gvData.EndInit();
            gvData.Columns.Clear();
            //gridControl1.ViewCollection.Clear();
            //DevExpress.XtraGrid.Views.Grid.GridView gv = new DevExpress.XtraGrid.Views.Grid.GridView();
            //gridControl1.ViewCollection.Add(gv);
            //gridControl1.MainView = gv;
            gvData.ShowLoadingPanel();
            if (dtDownLoadedData != null)
            {
                dtDownLoadedData.Clear();
            }
            gvData.HideLoadingPanel();
        }

        /// <summary>
        /// 目录配置信息
        /// </summary>
        private DataTable dtDirConfigInfo = new DataTable();


        public FrmInsur1301()
        {
            InitializeComponent();
        }

        private void btnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Close();
        }

        private void frmInsurDownload_Load(object sender, EventArgs e)
        {
            labDownloadFileName.Caption = "";//下载路径
            labDataCountInDB.Caption = ""; //本地数据库中的目录数据量
            labInfo.Caption = "";
            btnSave.Enabled = false;
            GetInterFace();
            // barEditItem3.EditValue = new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString("yyyy-MM-dd HH:mm:ss");
            searchLookup_Interface.EditValue = "ptyb";
        }

        #region 方法

        //提取接口
        private void GetInterFace()
        {
            string sql = "SELECT INTERFACECODE,INTERFACENAME  FROM INSURANCE.TJ_INTERFACE_DICT ORDER BY INTERFACEINDEX";
            DataTable dt = new ServerPublicClient().GetDataBySql(sql).Tables[0];
            repositoryItemSearchLookUpEdit_Interface.DataSource = dt;
            repositoryItemSearchLookUpEdit_Interface.ValueMember = "INTERFACECODE";
            repositoryItemSearchLookUpEdit_Interface.DisplayMember = "INTERFACENAME";
            if (dt.Rows.Count > 0)
            {
                searchLookup_Interface.EditValue = dt.Rows[0][0].ToString();
            }
        }

        /// <summary>
        ///  功能: //定义返回数组长度
        /// </summary> 
        //public void of_getout_array(string as_data, ref int li_row, ref int lj_column)
        //{
        //    try
        //    {
        //        string[,] str_out = new string[5, 80];
        //        string[] as_splitstr, ls_rows, ls_col;
        //        as_splitstr = as_data.Split('^');
        //        int row, k;
        //        row = 0;
        //        str_out[row, 0] = as_splitstr[0].ToString();
        //        str_out[row, 1] = as_splitstr[1].ToString();
        //        str_out[row, 2] = as_splitstr[2].ToString();
        //        ls_rows = str_out[row, 2].Split('$');
        //        int li_ret = ls_rows.Length;
        //        if (li_ret > 1)
        //        {
        //            string pdnull = ls_rows[li_ret - 1];
        //            if (string.IsNullOrEmpty(pdnull))
        //            {
        //                li_ret = li_ret - 1;
        //            }
        //        }
        //        lj_column = 2;
        //        for (int i = 0; i < li_ret; i++)
        //        {
        //            str_out[row, 0] = as_splitstr[0];
        //            str_out[row, 1] = as_splitstr[1];
        //            k = 2;
        //            ls_col = ls_rows[i].Split('|');
        //            int ret = ls_col.Length;
        //            if (ret > 1)
        //            {
        //                string pdnull2 = ls_col[ret - 1];
        //                if (string.IsNullOrEmpty(pdnull2))
        //                {
        //                    ret = ret - 1;
        //                }

        //            }
        //            for (int j = 0; j < ret; j++)
        //            {
        //                str_out[row, k] = ls_col[j];
        //                k++;
        //                if (k > lj_column)
        //                {
        //                    lj_column = k;
        //                }
        //            }
        //            row++;
        //        }
        //        li_row = row;
        //    }
        //    catch (Exception e)
        //    {
        //        MessageBox.Show("定义返回数组长度of_getout_array方法异常" + e.Message);
        //    }
        //}
        /// <summary>
        ///  功能: //拆分返回字符串
        /// </summary> 
        //public void of_getout_split(string as_data, ref string[,] str_out)
        //{
        //    try
        //    {
        //        string[] as_splitstr, ls_rows, ls_col;
        //        as_splitstr = as_data.Split('^');
        //        int row, k;
        //        row = 0;
        //        str_out[row, 0] = as_splitstr[0].ToString();
        //        str_out[row, 1] = as_splitstr[1].ToString();
        //        str_out[row, 2] = as_splitstr[2].ToString();
        //        ls_rows = str_out[row, 2].Split('$');
        //        int li_ret = ls_rows.Length;
        //        if (li_ret > 1)
        //        {
        //            string pdnull = ls_rows[li_ret - 1];
        //            if (string.IsNullOrEmpty(pdnull))
        //            {
        //                li_ret = li_ret - 1;
        //            }
        //        }
        //        for (int i = 0; i < li_ret; i++)
        //        {
        //            str_out[row, 0] = as_splitstr[0];
        //            str_out[row, 1] = as_splitstr[1];
        //            k = 2;
        //            ls_col = ls_rows[i].Split('|');
        //            int ret = ls_col.Length;
        //            if (ret > 1)
        //            {
        //                string pdnull2 = ls_col[ret - 1];
        //                if (string.IsNullOrEmpty(pdnull2))
        //                {
        //                    ret = ret - 1;
        //                }

        //            }
        //            for (int j = 0; j < ret; j++)
        //            {
        //                str_out[row, k] = ls_col[j];
        //                k++;
        //            }
        //            row++;
        //        }
        //    }
        //    catch (Exception e)
        //    {
        //        MessageBox.Show("of_getout_split方法异常" + e.Message);
        //    }

        //}

        /// <summary>
        /// 得到字段的中文描述
        /// </summary>
        /// <param name="desc"></param>
        /// <returns></returns>
        private string getFieldChineseDesc(string desc)
        {
            string chineseDesc = desc;
            string[] array1 = desc.Split(':');//如果是带:的则拆开
            if (array1.Length > 1)
            {
                chineseDesc = array1[1];
            }

            string[] array2 = desc.Split('：');//如果是带：（中文全角分号）的则拆开
            if (array2.Length > 1)
            {
                chineseDesc = array2[1];
            }
            return chineseDesc;
        }


        /// <summary>
        /// 临时测试使用测试表，获得临时测试表的表名
        /// zzzz临时测试zzzz
        /// </summary>
        /// <param name="tableName"></param>
        /// <returns></returns>
        private string getTestTableName(string tableName)
        {
            if (mIsTest)
            {
                if (!tableName.ToUpper().EndsWith("_TEST"))
                {
                    tableName = tableName + "_TEST";
                }

                if (!tableName.ToUpper().StartsWith("INSURANCE."))
                {
                    tableName = "INSURANCE." + tableName;
                }
            }
            return tableName;
        }

        /// <summary>
        /// 判断是否是需要进行与HIS对照的表（并且是需要有INPUT_CODE输入码字段）
        /// </summary>
        /// <param name="tableName"></param>
        /// <returns></returns>
        private bool isVSwithInputCodeTable(string tableName)
        {
            return tableName.Contains("1301")//西药中成药目录
                        || tableName.Contains("1302")//中药饮片目录
                        || tableName.Contains("1303")//医疗机构制剂目录
                        || tableName.Contains("1305") //医疗服务项目目录
                        || tableName.Contains("1306") //医用耗材目录
                        || tableName.Contains("1321"); //医疗服务项目（新）目录                        
        }
        /// <summary>
        /// 设置下载数据表的列信息
        /// </summary>
        /// <param name="businessCode">业务代码</param>
        /// <param name="catalogName">目录名称（数据表描述）</param>
        /// <param name="tableName">数据表名称</param>
        /// <param name="version">版本</param>
        private bool setDownloadedTableColumns(string businessCode, string catalogName, string tableName, string version)
        {
            try
            {
                if (!hasInputCodeField(tableName))
                {
                    errorMessage = $"{tableName} 表无 {INPUT_CODE_FIELDNAME} 列，请先添加 {INPUT_CODE_FIELDNAME} 列！[VARCHAR2(200)]";
                    CommonHelper.WriteLog(errorMessage);
                    return false;
                }

                CommonHelper.WriteLog("设置下载数据表的列信息...");
                string sql = $"select a.*,a.rowid from insurance.tj_business_config a where a.interfacecode='{is_interface_code}' and a.businesscode='{businessCode}' and a.paramtype='out' order by a.dataorder asc";
                dtDirConfigInfo = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
                if (dtDirConfigInfo.Rows.Count > 0)
                {
                    var fieldNameList = dtDirConfigInfo.AsEnumerable().Select(x => x["savecolname"].ToString()).ToList();
                    string sColumns = string.Join(", ", fieldNameList);

                    if (isVSwithInputCodeTable(tableName))
                        sColumns += ", INPUT_CODE"; //如果是需要对照的表，还需加上INPUT_CODE输入码字段

                    //var fieldNameWithAsList = dtDirConfigInfo.AsEnumerable().Select(x =>
                    //{
                    //    string fieldName = x["SAVECOLNAME"].ToString();//字段名
                    //    string dataName = x["DATANAME"].ToString();//中文提示
                    //    string chineseDesc = getFieldChineseDesc(dataName);
                    //    return $"{fieldName} AS {chineseDesc}";
                    //});
                    //string sColumnWithAs = string.Join(", ", fieldNameWithAsList);                    

                    sql = "select " + sColumns + " from " + tableName + " where 1=0";                    
                    // sql = "select " + sColumnWithAs + " from " + ls_savetable + " where 1=0";
                    dtDownLoadedData = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql, tableName).Tables[0];
                }
                CommonHelper.WriteLog("设置下载数据表的列信息完成");
                return true;
            }
            catch (Exception ex)
            {
                errorMessage = $"下载目录后，设置数据表字段信息时出错！[{ex.Message}]";
                CommonHelper.WriteLog($"{errorMessage}：\r{ex.StackTrace}");
                //XtraMessageBox.Show(this, errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 设置GridView列标题和只读
        /// </summary>
        private void setGridColumnCaptionAndSetReadonly()
        {
            //设置列标题
            foreach (GridColumn column in gvData.Columns)
            {
                column.OptionsColumn.ReadOnly = true;
                var dr = dtDirConfigInfo.AsEnumerable().FirstOrDefault(x => x["SAVECOLNAME"].Equals(column.FieldName));
                if (dr != null)
                {
                    string desc = dr["DATANAME"].ToString();//字段描述
                    desc = getFieldChineseDesc(desc);
                    column.Caption = desc;
                }
                else
                {
                    if (column.Caption.Equals(INPUT_CODE_FIELDNAME))
                    {
                        column.Caption = "拼音码";
                    }
                }
            }
        }

        /// <summary>
        /// 比较新下载的数据与原目录表中的对应数据，
        /// 如果有对应数据再判断新数据是否与原数据完全一致，
        /// 如不一致则需要更新对应字段的值
        /// </summary>
        /// <param name="spc"></param>        
        /// <param name="dtInsertNew">待插入数据的dt，用于添加下载后的并且原数据表中没有的新数据</param>
        /// <param name="drDownloaded">新下载的数据行</param>
        /// <param name="tableName">表名</param>
        /// <param name="lstUpdateField">输出：判断原数据中是否含有新下载的数据行对应的数据，如果有则判断数据是否一致，如果不一致则给出更新字段的语句列表</param>
        /// <returns></returns>
        private bool compareData(ServerPublicClient spc, DataTable dtInsertNew, DataRow drDownloaded, string tableName, List<string> lstUpdateField)
        {
            try
            {
                //string sNoFieldName = dtDirConfigInfo.AsEnumerable().First()[0].ToString();//编号字段名称
                string sNoFieldName = drDownloaded.Table.Columns[0].ColumnName;//编号字段名称
                string sNo = drDownloaded[sNoFieldName].ToString(); //编号字段内容
                string sql = $@"select * from {tableName} where {sNoFieldName} = '{sNo}'";
                DataTable dt = spc.GetDataBySql(sql).Tables[0];
                if (dt.Rows.Count > 0) //有数据
                {
                    //直接更新
                    dtDirConfigInfo.AsEnumerable().Select(x => $"");

                    foreach (DataRow drFieldCfg in dtDirConfigInfo.Rows)
                    {
                        // 判断字段是否一致
                        string fieldName = drFieldCfg["PARAMNAME"].ToString();//字段名
                        DataRow drOldData = dt.Rows[0];//原表中的数据
                        string sOld = drOldData[fieldName].ToString();
                        string sDownload = drDownloaded[fieldName].ToString();
                        int lenOld = sOld.Length;
                        int lenDown = sDownload.Length;
                        if (!sOld.Equals(sDownload) //如果不等
                            &&
                            (fieldName.ToLower() != "wbzjm" && fieldName.ToLower() != "pyzjm") //排除掉五笔助记码和拼音助记码字段
                            && fieldName.ToLower() != "mcyl"
                            )
                        {
                            lstUpdateField.Add($"{fieldName}='{drDownloaded[fieldName]}'");
                        }
                    }
                }
                else
                {
                    dtInsertNew.ImportRow(drDownloaded);
                }
                return true;
            }
            catch (Exception ex)
            {
                this.errorMessage = $"保存目录数据，判断新旧数据时出错！[{ex.Message}]";
                CommonHelper.WriteLog($"{this.errorMessage}：\r{ex.StackTrace}");
                return false;
            }
        }

        //public int of_get_tablename(string as_interfacecode, string as_businesscode, ref string ls_savetable)
        //{
        //    try
        //    {
        //        string sqla = "select SaveTable from insurance.tj_Business_Dict  where InterfaceCode = '" + as_interfacecode + "'";
        //        sqla += " and lower(BusinessCode)  ='" + as_businesscode.ToLower() + "'";
        //        DataTable dt = new ServerPublicClient().GetDataBySql(sqla).Tables[0];
        //        if (dt.Rows.Count < 1)
        //        {
        //            errorMessage = "取业务表名失败！";
        //            CommonHelper.WriteLog(errorMessage);
        //            //MessageBox.Show("取业务表名失败！", "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
        //            return -1;
        //        }
        //        ls_savetable = "INSURANCE." + dt.Rows[0][0].ToString().ToUpper();
        //        return 0;
        //    }
        //    catch (Exception ex)
        //    {
        //        errorMessage = "of_get_tablename方法异常" + ex.Message;
        //        CommonHelper.WriteLog($"{this.errorMessage}：\r{ex.StackTrace}");
        //        //MessageBox.Show();
        //        return -1;
        //    }
        //}

        /// <summary>
        /// 下载目录
        /// </summary>        
        /// <param name="businessCode">业务代码</param>
        /// <param name="catalogName">目录名称（数据表描述）</param>
        /// <param name="tableName">数据表名称</param>
        /// <param name="version">版本</param>
        /// <param name="filePathName">输出：下载的文件路径及文件名</param>
        /// <returns>
        /// 0:下载成功
        /// 1：已经是最新的数据
        /// -1：发生错误
        /// -2：警告提示
        /// </returns>
        private int downLoadCatalogueData(string businessCode, string catalogName, string tableName, string version, out string filePathName)
        {
            filePathName = "";
            CommonHelper.WriteLog($"##########################################");
            CommonHelper.WriteLog($"开始下载目录：{businessCode}， 版本：{version}");
            gvData.ShowLoadingPanel();
            try
            {
                CommonHelper.WriteLog($"判断是否需要INPUT_CODE字段：{businessCode}， 版本：{version}");
                if (!hasInputCodeField(tableName))
                {
                    errorMessage = $"{tableName} 表无 {INPUT_CODE_FIELDNAME} 列，请先添加 {INPUT_CODE_FIELDNAME} 列！[VARCHAR2(200)]";
                    CommonHelper.WriteLog(errorMessage);
                    return -1;
                }

                CommonHelper.WriteLog($"判断费别对照：{businessCode}， 版本：{version}");
                string ls_charge_type = "";
                string sql = "select a.chargetype from insurance.tj_interface_vs_charge a where a.interfacecode='" + is_interface_code + "'";
                DataTable dt_fee = new ServerPublicClient().GetDataBySql(sql).Tables[0];
                if (dt_fee.Rows.Count > 0)
                {
                    ls_charge_type = dt_fee.Rows[0][0].ToString();
                }
                else
                {
                    errorMessage = "接口与费别没做对照！";
                    CommonHelper.WriteLog(errorMessage);
                    //XtraMessageBox.Show(errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return -1;
                }
                //if (is_interface_code.Equals("btyb"))
                //{
                //    ls_charge_type = "自费";
                //}
                //if (is_interface_code.Equals("ptyb"))
                //{
                //    ls_charge_type = "新农合";
                //}

                DataSet ds = new DataSet();
                DataTable dtnew = new DataTable();
                dtnew.Columns.Add("INTERFACECODE");
                dtnew.Columns.Add("BUSINESSCODE");
                //dtnew.Columns.Add("JBRQ");
                dtnew.Columns.Add("VER");
                DataRow dr = dtnew.NewRow();
                dr["INTERFACECODE"] = is_interface_code;
                dr["BUSINESSCODE"] = businessCode;
                //int ii;
                //if (!int.TryParse(barEditItem2.EditValue.ToString().Substring(barEditItem2.EditValue.ToString().Length - 4), out ii))
                //{
                //    gvData.HideLoadingPanel();
                //    XtraMessageBox.Show("BUSINESSCODE值存在问题，请检查命名规则！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                //    return;
                //}

                dr["VER"] = version;
                // dr["JBRQ"] =DateTime.Parse(barEditItem3.EditValue.ToString()).ToString("yyyyMMddHHmmss");
                dtnew.Rows.Add(dr);
                ds.Tables.Add(dtnew.Copy());
                string remark = "";

                CommonHelper.WriteLog($"执行医保下载：{businessCode}， 版本：{version}");
                //执行医保下载接口
                int ret = MedicalInterface.MedicalBusinessHandle("025", "", "", ls_charge_type, SystemParm.LoginUser.ID, "", remark, ds);
                //====下载成功===
                if (ret == 0)
                {
                    //设置gridview的列
                    CommonHelper.WriteLog($"设置数据列：{businessCode}， 版本：{version}");
                    if (!setDownloadedTableColumns(businessCode, catalogName, tableName, version))
                        return -1;

                    string output = MedicalInterface.gs_output;

                    if (!string.IsNullOrEmpty(output))
                    {
                        JObject jo = (JObject)JsonConvert.DeserializeObject(output);
                        string filepath = jo["output"].ToString();
                        filePathName = filepath;
                        //filepath = @"D:\client\YBDLOAD\202106278635282351698696023.txt\";


                        //加载数据
                        if (!setDownloadedData(tableName, filepath))
                            return -1;

                        gcData.DataSource = dtDownLoadedData;
                        setGridColumnCaptionAndSetReadonly();//设置列标题

                        labDownloadFileName.Caption = $"{filepath} 【{catalogName}（数量：{dtDownLoadedData.Rows.Count}）】";
                        string msg = $"下载完成，{businessCode}，版本：{version}，共{dtDownLoadedData.Rows.Count}条新数据！ 文件路径：{filepath}";
                        CommonHelper.WriteLog(msg);

                        return 0;
                    }
                    else
                    {
                        errorMessage = $"下载失败【{catalogName}，版本：{version}】！医保返回的出参为空！";
                        //XtraMessageBox.Show(errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        CommonHelper.WriteLog(errorMessage);
                        return -1;
                    }
                }
                else if (ret == 1)
                {
                    CommonHelper.WriteLog($"已经是最新数据!【{catalogName}，版本：{version}】");
                    return 1;
                }
                else
                {
                    errorMessage = $"医保返回下载失败！【{catalogName}，版本：{version}】";
                    //XtraMessageBox.Show("医保返回下载失败！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    CommonHelper.WriteLog(errorMessage);
                    return -1;
                }
                //else
                //{
                //    btnDownload.Enabled = true;
                //    return;
                //}
            }
            catch (Exception ex)
            {
                errorMessage = $"下载目录时出错【{catalogName}，版本：{version}】！[{ex.Message}]";
                CommonHelper.WriteLog($"{errorMessage}：\r{ex.StackTrace}");
                //XtraMessageBox.Show(this, errmsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return -1;
            }
            finally
            {
                gvData.HideLoadingPanel();

            }
        }

        /// <summary>
        /// 导入数据
        /// </summary>
        /// <param name="businessCode">业务代码</param>
        /// <param name="catalogName">目录名称（数据表描述）</param>
        /// <param name="tableName">数据表名称</param>
        /// <param name="version">版本</param>
        private void importCatalogueData(string businessCode, string catalogName, string tableName, string version)
        {

            string fileName = "";
            OpenFileDialog OpenFile = new OpenFileDialog();
            OpenFile.InitialDirectory = System.IO.Directory.GetCurrentDirectory();
            OpenFile.Filter = "文本文档|*.txt";
            if (OpenFile.ShowDialog() == DialogResult.OK)
            {
                gvData.ShowLoadingPanel();
                try
                {
                    fileName = OpenFile.FileName;

                    if (!setDownloadedTableColumns(businessCode, catalogName, tableName, version))
                        return;

                    if (!setDownloadedData(tableName, fileName))
                        return;

                    gcData.DataSource = dtDownLoadedData; //设置数据源
                    setGridColumnCaptionAndSetReadonly();//设置列标题

                    string msg = $"导入完成，{businessCode}共{dtDownLoadedData.Rows.Count}条新数据！ 文件路径：{fileName}";
                    CommonHelper.WriteLog(msg);
                    labDownloadFileName.Caption = $"{fileName} 【{catalogName}（数量：{dtDownLoadedData.Rows.Count}）】";
                    XtraMessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    string errmsg = $"导入数据时出错！[{ex.Message}]";
                    CommonHelper.WriteLog($"{errmsg}：\r{ex.StackTrace}");
                    XtraMessageBox.Show(this, errmsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                finally
                {
                    gvData.HideLoadingPanel();
                }
            }
        }

        /// <summary>
        /// 下载的数据去重（根据关键字段）
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="dtDownloaded">下载的数据表</param>
        /// <returns></returns>
        private bool RemoveDuplicationData(string tableName, DataTable dtDownloaded)
        {
            try
            {
                CommonHelper.WriteLog("下载的数据去重...");
                List<string> keyFieldNames = new List<string>();//关键字段列表
                if (!getKeyFieldNames(tableName, dtDownloaded, out keyFieldNames))//得到关键字段列表
                    return false;


                //按关键字段分组后，数量大于1的为有重复的
                var groupCountMoreThenOne = dtDownloaded.AsEnumerable().GroupBy(g =>
                {
                    string sFieldName = ""; //如果是联合主键（多个关键字段），就将几个字段的值拼接起来，使用groupby 判断重复（即分组后count>1的）
                    foreach (var fieldName in keyFieldNames)
                    {
                        sFieldName += g[fieldName].ToString();
                    }
                    return sFieldName;
                })
                .Select(p => new { groupKey = p.Key, groupCount = p.Count() })
                .Where(x => x.groupCount > 1); //取分组后大于1的

                ////////
                if (groupCountMoreThenOne.Count() > 1)//如果有分组后大于1的，说明是重复的，需要从下载数据表中去重
                {
                    CommonHelper.WriteLog($"有重复的数据，重复的数量为：{groupCountMoreThenOne.Count()}");
                    foreach (var item in groupCountMoreThenOne)
                    {
                        //取出重复的数据行
                        var rows = dtDownloaded.AsEnumerable().Where(x =>
                        {
                            string sFieldName = ""; //如果是联合主键（多个关键字段），就将几个字段的值拼接起来
                            foreach (var fieldName in keyFieldNames)
                            {
                                sFieldName += x[fieldName].ToString();
                            }
                            return sFieldName == item.groupKey;
                        }).ToList();//找到所有重复项

                        //只保留最后一行，其余的重复行删除
                        for (int i = 0; i < rows.Count - 1; i++)
                        {
                            DataRow dr = rows[i];

                            //如果有重复的话，则记录一下日志
                            var keyFieldNameList = new List<string>();//关键字段列表
                            if (!getKeyFieldNames(tableName, dtDownLoadedData, out keyFieldNameList))//得到关键字段列表
                                return false;

                            string mess = "";
                            foreach (var fieldname in keyFieldNameList)
                            {
                                mess += $"{fieldname}='{dr[fieldname]}',";
                            }
                            if (mess.EndsWith(","))
                            {
                                mess = mess.Remove(mess.Length - 1);//删除最后一个逗号
                            }
                            CommonHelper.WriteLog($"去除下载数据中的重复数据（{tableName}）， {mess}");


                            dtDownloaded.Rows.Remove(dr); //删除
                        }
                    }
                }
                else
                {
                    CommonHelper.WriteLog("无重复的数据");
                }
                CommonHelper.WriteLog("下载的数据去重完成");
                return true;
            }
            catch (Exception ex)
            {
                errorMessage = $"对下载的数据去重时出错！[{ex.Message}]";
                CommonHelper.WriteLog($"{errorMessage}：\r{ex.StackTrace}");
                // XtraMessageBox.Show(this, errmsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 判断要添加的数据是否重复
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="dataRow">要添加的数据行</param>
        /// <param name="dtDownloaded">要判断的数据表</param>
        /// <param name="isDuplication">输出项：是否重复</param>
        /// <param name="duplicatedRow">输出项：如果有重复则输出重复的行，如果无重复则输出null</param>
        /// <returns></returns>
        private bool DetermineisDuplicationData(string tableName, DataRow dataRow, DataTable dtDownloaded, out bool isDuplication, out DataRow duplicatedRow)
        {
            isDuplication = false;
            duplicatedRow = null;
            try
            {
                List<string> keyFieldNames = new List<string>();//关键字段列表
                if (!getKeyFieldNames(tableName, dtDownloaded, out keyFieldNames))//获取关键字段列表
                    return false;

                var sCompareValue = "";//待比较的值（为待添加的行，根据关键字段拼接起来的字段值）
                foreach (var fieldName in keyFieldNames)
                {
                    sCompareValue += dataRow[fieldName].ToString();
                }

                duplicatedRow = dtDownloaded.AsEnumerable().FirstOrDefault(x =>
                {
                    string sFieldName = ""; //如果是联合主键（多个关键字段），就将几个字段的值拼接起来
                    foreach (var fieldName in keyFieldNames)
                    {
                        sFieldName += x[fieldName].ToString();
                    }
                    return sFieldName == sCompareValue;
                });

                isDuplication = duplicatedRow != null; //是否有重复
                return true;
            }
            catch (Exception ex)
            {
                errorMessage = $"判断是否有重复数据时出错！[{ex.Message}]";
                CommonHelper.WriteLog($"{errorMessage}：\r{ex.StackTrace}");
                // XtraMessageBox.Show(this, errmsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 得到指定表的结构，结果为DataTable
        /// </summary>
        /// <param name="spc">数据库操作</param>
        /// <param name="tableName">表名</param>
        /// <returns></returns>
        private DataTable getTableStructDataTable(ServerPublicClient spc, string tableName)
        => spc.GetDataBySql($"select * from {tableName} where 1=0 ", tableName).Tables[0];



        /// <summary>
        /// 判断是否包含INPUT_CODE列
        /// </summary>
        /// <param name="dr"></param>
        /// <returns>true包含; false不包含</returns>
        private bool hasInputCodeField(string tableName)
        {
            if (isVSwithInputCodeTable(tableName))
            {
                DataTable dt = getTableStructDataTable(new ServerPublicClient(), tableName);
                return dt.Columns.Contains(INPUT_CODE_FIELDNAME);
            }
            return true;
        }

        /// <summary>
        /// 处理拼音输入码
        /// 1301/1302/1303/1305/1306/1321这几个目录因为要执行对照，需要使用拼音码检索，
        /// 因此需要有input_code字段用于存放拼音码，若没有则需添加 INPUT_CODE VARCHAR2(200)
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="dr">数据行</param>
        /// <returns>true成功，false失败</returns>
        private bool dealInputCode(string tableName, DataRow dr)
        {
            string nameFildName = "";//存放项目名称字段的字段名（用于生成该名称的拼音码）

            if (tableName.Contains("1301"))//西药中成药目录
            {
                nameFildName = "ZCMC";//第8列：注册名称
                return dealInputCode_Inner(tableName, nameFildName, dr);
            }

            if (tableName.Contains("1302"))//中药饮片目录
            {
                nameFildName = "DWYMC";//第2列：单味药名称
                return dealInputCode_Inner(tableName, nameFildName, dr);
            }

            if (tableName.Contains("1303"))//医疗机构制剂目录
            {
                nameFildName = "YPSPM";//第2列：药品商品名
                return dealInputCode_Inner(tableName, nameFildName, dr);
            }

            if (tableName.Contains("1305")) //医疗服务项目目录
            {
                nameFildName = "YLFWXMMC";//第10列：医疗服务项目名称
                return dealInputCode_Inner(tableName, nameFildName, dr);
            }

            if (tableName.Contains("1306")) //医用耗材目录
            {
                nameFildName = "HCMC";//第2列：耗材名称
                return dealInputCode_Inner(tableName, nameFildName, dr);
            }

            if (tableName.Contains("1321")) //医疗服务项目（新）目录
            {
                nameFildName = "YLFWXMMC";//第 7 列：医疗服务项目名称
                return dealInputCode_Inner(tableName, nameFildName, dr);
            }
            return true;
        }

        /// <summary>
        /// 处理拼音输入码
        /// 1301/1302/1303/1305/1306/1321这几个目录因为要执行对照，需要使用拼音码检索，
        /// 因此需要有input_code字段用于存放拼音码，若没有则需添加 INPUT_CODE VARCHAR2(200)
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="nameFieldName">“项目名称”字段的字段名</param>
        /// <param name="dr">数据行</param>
        /// <returns></returns>
        private bool dealInputCode_Inner(string tableName, string nameFieldName, DataRow dr)
        {
            try
            {
                if (!hasInputCodeField(tableName))
                {
                    errorMessage = $"{tableName} 表无 {INPUT_CODE_FIELDNAME} 列，请先添加 {INPUT_CODE_FIELDNAME} 列！[VARCHAR2(200)]";
                    CommonHelper.WriteLog(errorMessage);
                    return false;
                }
                string nameFieldValue = dr[nameFieldName].ToString();//名称字段值
                string pym = CommonHelper.GetPinyinCode(nameFieldValue, 200);//得到名称的拼音缩写
                //if (!dr.Table.Columns.Contains(INPUT_CODE_FIELDNAME))
                //{
                //    dr.Table.Columns.Add(INPUT_CODE_FIELDNAME);
                //}
                dr[INPUT_CODE_FIELDNAME] = pym;

                return true;
            }
            catch (Exception ex)
            {
                errorMessage = $"处理拼音输入码时出错！[{ex.Message}]";
                CommonHelper.WriteLog($"{errorMessage}：\r{ex.StackTrace}");
                // XtraMessageBox.Show(this, errmsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }


        /// <summary>
        /// 加载并绑定下载的数据
        /// </summary>
        /// <param name="fileName"></param>
        private bool setDownloadedData(string tableName, string fileName)
        {
            try
            {
                CommonHelper.WriteLog("加载并绑定下载的数据...");                
                //if (fileName.Equals(string.Empty))
                //{
                //    MessageBox.Show("没有找到选中文件", "提示");
                //    return;
                //}
                if (File.Exists(fileName))
                {

                }
                else if (Directory.Exists(fileName))
                {
                    fileName = fileName + @"\" + Path.GetFileName(fileName.TrimEnd('\\'));
                }

                DataTable dtTableInfo = new DataTable();//表字段信息
                if (!getTableInfo(tableName, out dtTableInfo)) //得到表的字段信息
                    return false;

                clearData();//清除数据

                int duplCount = 0;//重复数据的数量
                int totalCount = 0;//原有数据量（去重前的数据量）
                using (StreamReader sReader = new StreamReader(fileName, Encoding.UTF8))
                {
                    while (!sReader.EndOfStream)
                    {
                        ++totalCount;
                        string readFileOne = sReader.ReadLine();
                        readFileOne = readFileOne.Replace("\n", "");
                        string[] strs = readFileOne.Split(new char[] { '\t' }, StringSplitOptions.None);//, '"'
                        DataRow dr = dtDownLoadedData.NewRow();
                        int count = strs.Length >= dtDownLoadedData.Columns.Count ? dtDownLoadedData.Columns.Count : strs.Length;
                        for (int i = 0; i < count; i++)
                        {
                            int maxLen = 4000;
                            string fieldName = dtDownLoadedData.Columns[i].ColumnName; //取字段名
                            var rowFieldInfo = dtTableInfo.AsEnumerable().FirstOrDefault(x => x["COLUMN_NAME"].Equals(fieldName));//取出该字段的信息行
                            if (rowFieldInfo != null)
                            {
                                maxLen = Convert.ToInt32(rowFieldInfo["DATA_LENGTH"]);//取出设定的字段长度
                            }

                            string temp = strs[i] == "null" ? "" : strs[i].TrimEnd();                            
                            //1301的每次用量（MCYL）和功能主治（GNZZ）字段都已经改为CLOB，
                            //而非1301的需判断最大字符不能超过字段定义的长度
                            if (!(
                                    tableName.Contains("1301")
                                    && (fieldName.Equals("MCYL") || fieldName.Equals("GNZZ"))
                                  )
                                )
                            {
                                int len = System.Text.ASCIIEncoding.Default.GetByteCount(temp);
                                if (len > maxLen)
                                {
                                    byte[] tagByteArray = new byte[maxLen];//目标字节数组
                                    byte[] srcByteArray = System.Text.ASCIIEncoding.Default.GetBytes(temp);//源字节数组
                                    Array.Copy(srcByteArray, tagByteArray, tagByteArray.Length);//复制到目标数组
                                    string ttt = System.Text.ASCIIEncoding.Default.GetString(tagByteArray);//字节数组转字符串
                                    temp = ttt;
                                    CommonHelper.WriteLog($"加载数据时，字段：{fieldName}，下载的该字段数据长度({len})大于数据库中该字段的长度({maxLen})，将进行截取");
                                }
                            }
                            temp = temp == "null" ? "" : temp;
                            dr[i] = temp;
                        }
                        if (!dealInputCode(tableName, dr))//处理拼音码
                        {
                            return false;
                        }

                        /*
                        //判断是否已经存在
                        if (!DetermineisDuplicationData(tableName, dr, dtDownLoadedData, out bool isDuplication, out DataRow drDuplicated))
                            return false;//调用判断失败，则返回

                        if (isDuplication && drDuplicated != null) //如果有重复项，则先删除重复项后再添加，并将重复项信息记录到日志中                     
                        {
                            ++duplCount;
                            dtDownLoadedData.Rows.Remove(drDuplicated);

                            //如果有重复的话，则记录一下日志
                            var keyFieldNameList = new List<string>();//关键字段列表
                            if (!getKeyFieldNames(tableName, dtDownLoadedData, out keyFieldNameList))//得到关键字段列表
                                return false;

                            string mess = "";
                            foreach (var fieldname in keyFieldNameList)
                            {
                                mess += $"{fieldname}='{dr[fieldname]}',";
                            }
                            if (mess.EndsWith(","))
                            {
                                mess = mess.Remove(mess.Length - 1);//删除最后一个逗号
                            }
                            CommonHelper.WriteLog($"去除下载数据中的重复数据（{tableName}）， {mess}");
                        }//end 判断重复
                        */

                        dtDownLoadedData.Rows.Add(dr); //添加
                    }
                }
                //CommonHelper.WriteLog($"读取数据完成，其中重复数据数量为：{duplCount}，原有数据量为：{totalCount}");

                totalCount = dtDownLoadedData.Rows.Count;
                if (!RemoveDuplicationData(tableName, dtDownLoadedData)) //去除重复数据
                    return false;
                duplCount = totalCount - dtDownLoadedData.Rows.Count;//去重的数量
                if (duplCount > 0)
                    CommonHelper.WriteLog($"读取数据完成，其中重复数据数量为：{duplCount}，原有数据量为：{totalCount}，去重后的数据量为：{dtDownLoadedData.Rows.Count}");

                gcData.DataSource = null;
                gvData.BeginInit();
                gvData.EndInit();
                gvData.Columns.Clear();


                btnSave.Enabled = true;
                CommonHelper.WriteLog("加载并绑定下载的数据完成");
                return true;
            }
            catch (Exception ex)
            {
                errorMessage = $"加载下载数据时出错！[{ex.Message}]";
                CommonHelper.WriteLog($"{errorMessage}：\r{ex.StackTrace}");
                // XtraMessageBox.Show(this, errmsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        #endregion 方法
        #region 事件

        /// <summary>
        /// 接口更改事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void searchLookup_Interface_EditValueChanged(object sender, EventArgs e)
        {
            if (searchLookup_catalogueName.EditValue != null)
            {
                string businessCode = searchLookup_catalogueName.EditValue.ToString();//业务代码
                string catalogName = getTableDescByBusinessCode(businessCode);//目录名
                string tableName = getTableNameByBusinessCode(businessCode);//表名
                string version = txtVer.EditValue.ToString();//版本

                tableName = getTestTableName(tableName); //zzzz临时测试zzzz

                if (!await ReminderSave(dtDownLoadedData, businessCode, catalogName, tableName, version))
                    return;
            }
            is_interface_code = searchLookup_Interface.EditValue.ToString();
            string sql = "select a.interfacecode,a.businesscode,a.businessname,a.savetable, a.description from insurance.tj_business_dict a where a.interfacecode='" + is_interface_code + "' and a.businesstype='down' and length(a.businesscode)>13 order by a.description";
            dt_type = new ServerPublicClient().GetDataBySql(sql).Tables[0];
            repositoryItemSearchLookUpEdit_catalogueName.DataSource = dt_type;
            repositoryItemSearchLookUpEdit_catalogueName.ValueMember = "BUSINESSCODE";
            repositoryItemSearchLookUpEdit_catalogueName.DisplayMember = "BUSINESSNAME";
        }

        /// <summary>
        /// 循环下载至最新版
        /// </summary>
        /// <param name="businessCode">业务代码</param>
        /// <param name="catalogName">目录名称（数据表描述）</param>
        /// <param name="tableName">数据表名称</param>
        /// <param name="version">版本</param>
        /// <returns></returns>
        private async Task loopDownload2Lastest(string businessCode, string catalogName, string tableName, string version)
        {

            while (true)
            {
                bool isLoop = false; //是否允许循环下载

                this.Invoke(new Action(() => isLoop = checkboxLoopDownloadSave.Checked));

                if (!isLoop)
                {
                    this.Invoke(new Action(() => XtraMessageBox.Show("您取消了循环增量下载至最新版本，下载停止！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)));
                    return;
                }
                //1.下载                
                this.Invoke(new Action(() =>
                {
                    labInfo.Caption = $"正在下载【{catalogName}】，版本：{version}......";
                }));
                int iret = downLoadCatalogueData(businessCode, catalogName, tableName, version, out _);
                if (iret < 0)
                {
                    this.Invoke(new Action(() =>
                    {
                        labInfo.Caption = errorMessage;
                    }));
                    XtraMessageBox.Show(errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                if (iret == 1)//已经是最新数据
                {
                    string mess = "下载并保存完成，已经是最新数据！";
                    this.Invoke(new Action(() =>
                    {
                        labInfo.Caption = mess;
                    }));
                    XtraMessageBox.Show(mess, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (dtDownLoadedData.Rows.Count == 0)
                {
                    //XtraMessageBox.Show("无数据可保存！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //return;
                    continue;
                }

                System.Threading.Thread.Sleep(2000); //休息2秒
                //2. 保存
                this.Invoke(new Action(() =>
                {
                    labInfo.Caption = $"正在保存下载的数据【{catalogName}】，版本：{version}（数据量：{dtDownLoadedData.Rows.Count}）......";
                }));
                bool bret = await saveData(businessCode, catalogName, tableName, version);
                if (!bret)
                {
                    this.Invoke(new Action(() =>
                    {
                        labInfo.Caption = errorMessage;
                    }));
                    XtraMessageBox.Show(errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                CommonHelper.WriteLog($"下载完成后，重新获取和设置最大版本号，并查询本地数据库表中的数据量");
                //获取最大版本号
                getLastestVer(businessCode, catalogName, tableName, out string sLastestVer);
                version = sLastestVer;
                setVerOptions(version);

                //设置显示本地数据库中指定表的数据量
                setCatalogueInDBCount(businessCode, catalogName, tableName, version);
                this.Invoke(new Action(() =>
                {
                    labInfo.Caption = $"({tableName}, version: {version})，其中更新{iOldDataModifycnt}条，添加{iNewDataInsertCnt}条！";
                }));
                System.Threading.Thread.Sleep(2000); //休息2秒
            }
        }

        /// <summary>
        /// 通过 业务代码得到表名
        /// </summary>
        /// <param name="businessCode">业务代码</param>
        /// <returns></returns>
        private string getTableNameByBusinessCode(string businessCode)
        {
            string sTableName = "";
            DataRow dr = dt_type.AsEnumerable().FirstOrDefault(x => x["BUSINESSCODE"].Equals(businessCode));
            if (dr != null)
                sTableName = dr["SAVETABLE"].ToString();
            return sTableName;
        }

        /// <summary>
        /// 通过 业务代码得到表描述
        /// </summary>
        /// <param name="business">业务代码</param>
        /// <returns></returns>
        private string getTableDescByBusinessCode(string business)
        {
            string sTableDesc = "";
            DataRow dr = dt_type.AsEnumerable().FirstOrDefault(x => x["BUSINESSCODE"].Equals(business));
            if (dr != null)
                sTableDesc = dr["BUSINESSNAME"].ToString();
            return sTableDesc;
        }

        /// <summary>
        /// 下载并保存
        /// </summary>
        /// <param name="businessCode">业务代码</param>
        /// <param name="catalogName">目录名称（数据表描述）</param>
        /// <param name="tableName">数据表名称</param>
        /// <param name="version">版本</param>
        private async void downloadAndSave(string businessCode, string catalogName, string tableName, string version)
        {

            //1.下载
            this.Invoke(new Action(() =>
            {
                labInfo.Caption = $"正在下载【{catalogName}（{businessCode}）】，版本：{version}......";
            }));
            int iret = downLoadCatalogueData(businessCode, catalogName, tableName, version, out _);
            if (iret < 0)
            {
                this.Invoke(new Action(() =>
                {
                    labInfo.Caption = errorMessage;
                }));
                XtraMessageBox.Show(errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            if (iret == 1)//已经是最新数据
            {
                string mess = "已经是最新数据！";
                this.Invoke(new Action(() =>
                {
                    labInfo.Caption = mess;
                }));
                XtraMessageBox.Show(mess, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (dtDownLoadedData.Rows.Count == 0)
            {
                string mess = "无数据可保存！";
                this.Invoke(new Action(() =>
                {
                    labInfo.Caption = mess;
                }));
                XtraMessageBox.Show(mess, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            //2. 保存
            this.Invoke(new Action(() =>
            {
                labInfo.Caption = $"正在保存下载的数据【{catalogName}】，版本：{version}（数据量：{dtDownLoadedData.Rows.Count}）......";
            }));
            bool bret = await saveData(businessCode, catalogName, tableName, version);
            if (!bret)
            {
                this.Invoke(new Action(() =>
                {
                    labInfo.Caption = errorMessage;
                }));
                XtraMessageBox.Show(errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            CommonHelper.WriteLog($"下载完成后，重新获取和设置最大版本号，并查询本地数据库表中的数据量");
            //获取最大版本号
            getLastestVer(businessCode, catalogName, tableName, out string sLastestVer);
            version = sLastestVer;
            setVerOptions(version);

            //设置显示本地数据库中指定表的数据量
            setCatalogueInDBCount(businessCode, catalogName, tableName, version);
            this.Invoke(new Action(() =>
            {
                labInfo.Caption = $"保存完成({tableName}, version: {version})，其中更新{iOldDataModifycnt}条，添加{iNewDataInsertCnt}条！";
            }));
            XtraMessageBox.Show("下载并保存完成！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

        }


        //开始下载
        private async void btnDownload_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            labInfo.Caption = "";

            if (searchLookup_Interface.EditValue == null || string.IsNullOrEmpty(searchLookup_Interface.EditValue.ToString()))
            {
                errorMessage = "请先选择接口！";
                XtraMessageBox.Show(errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            if (searchLookup_catalogueName.EditValue == null || string.IsNullOrEmpty(searchLookup_catalogueName.EditValue.ToString()))
            {
                errorMessage = "请先选择目录！";
                XtraMessageBox.Show(errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (txtVer.EditValue == null || string.IsNullOrEmpty(txtVer.EditValue.ToString()))
            {
                errorMessage = "请选择版本或指定版本！";
                XtraMessageBox.Show(errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            string businessCode = searchLookup_catalogueName.EditValue.ToString();//业务代码
            string catalogName = getTableDescByBusinessCode(businessCode);//目录名称
            string tableName = getTableNameByBusinessCode(businessCode);
            string version = txtVer.EditValue.ToString();//版本

            tableName = getTestTableName(tableName);//zzz临时测试用表


            //如果是下载后自动保存
            if (checkboxAutoSave.Checked)
            {
                if (checkboxLoopDownloadSave.Checked)
                {
                    await loopDownload2Lastest(businessCode, catalogName, tableName, version);//循环下载和保存，直到没有新版本数据
                }
                else
                {
                    downloadAndSave(businessCode, catalogName, tableName, version);//下载和保存本地数据库表中最大版本的下一版本
                }

                //
            }
            //只是下载操作
            else
            {
                int iret = downLoadCatalogueData(businessCode, catalogName, tableName, version, out string filepath);
                if (iret == 0)
                {
                    //下载成功
                    string msg = $"下载完成，{catalogName}({businessCode})，版本：{version}，共{dtDownLoadedData.Rows.Count}条新数据！ 文件路径：{filepath}";
                    XtraMessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else if (iret == 1)
                {
                    //下载无新数据
                    XtraMessageBox.Show("已经是最新的数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else if (iret < 0)
                {
                    //下载失败
                    XtraMessageBox.Show(this.errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }



        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void btnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            labInfo.Caption = "";
            if (dtDownLoadedData.Rows.Count == 0)
            {
                XtraMessageBox.Show("无数据可保存！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            //if (XtraMessageBox.Show("确定保存下载的数据到数据库吗？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
            //    return;

            string businessCode = searchLookup_catalogueName.EditValue.ToString();//业务代码
            string catalogName = getTableDescByBusinessCode(businessCode);//目录名
            string tableName = getTableNameByBusinessCode(businessCode);//表名
            string version = txtVer.EditValue.ToString();//版本

            tableName = getTestTableName(tableName); //zzzz临时测试zzzz

            this.Invoke(new Action(() =>
            {
                labInfo.Caption = $"正在保存下载的数据【{catalogName}】，版本：{version}（数据量：{dtDownLoadedData.Rows.Count}）......";
            }));

            bool bret = await saveData(businessCode, catalogName, tableName, version);
            if (bret)
            {
                this.Invoke(new Action(() =>
                {
                    labInfo.Caption = errorMessage;
                }));
                XtraMessageBox.Show($"保存完成，共更新{iOldDataModifycnt}条数据，添加{iNewDataInsertCnt}条数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);


                CommonHelper.WriteLog($"下载完成后，重新获取和设置最大版本号，并查询本地数据库表中的数据量");
                //获取最大版本号
                getLastestVer(businessCode, catalogName, tableName, out string sLastestVer);
                this.lastestVersion = sLastestVer;
                setVerOptions(sLastestVer);

                //设置显示本地数据库中指定表的数据量
                setCatalogueInDBCount(businessCode, catalogName, tableName, version);
            }
            else
            {
                labInfo.Caption = errorMessage;
                XtraMessageBox.Show(errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 事件

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnImport_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (searchLookup_catalogueName.EditValue == null)
            {
                XtraMessageBox.Show("请先选择目录类型！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            string businessCode = searchLookup_catalogueName.EditValue.ToString();//业务代码
            string catalogName = getTableDescByBusinessCode(businessCode);//目录名
            string tableName = getTableNameByBusinessCode(businessCode);//表名
            string version = txtVer.EditValue.ToString();//版本

            tableName = getTestTableName(tableName); //zzzz临时测试zzzz

            importCatalogueData(businessCode, catalogName, tableName, version);
        }

        private void btnClear_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            clearData();
            //gcData.DataSource = null;
            //gvData.BeginInit();
            //gvData.EndInit();
            //gvData.Columns.Clear();
            ////gridControl1.ViewCollection.Clear();
            ////DevExpress.XtraGrid.Views.Grid.GridView gv = new DevExpress.XtraGrid.Views.Grid.GridView();
            ////gridControl1.ViewCollection.Add(gv);
            ////gridControl1.MainView = gv;
            //if (dtNewData != null)
            //{
            //    dtNewData.Clear();
            //}
        }

        /// <summary>
        /// 导出到excel
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExport_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //方法1
            SaveFileDialog fileDialog = new SaveFileDialog();
            fileDialog.Title = "导出Excel";
            fileDialog.Filter = "Excel文件(*.xls)|*.xls";
            DialogResult dialogResult = fileDialog.ShowDialog(this);
            if (dialogResult == DialogResult.OK)
            {
                DevExpress.XtraPrinting.XlsExportOptions options = new DevExpress.XtraPrinting.XlsExportOptions();
                gcData.ExportToXls(fileDialog.FileName);
                DevExpress.XtraEditors.XtraMessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void gvData_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator)
            {
                if (e.RowHandle >= 0)
                    e.Info.DisplayText = (e.RowHandle + 1).ToString();
            }
            else
            {
                e.Info.DisplayText = "";
            }
            e.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
        }

        private void toolTipController_GridView_GetActiveObjectInfo(object sender, DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs e)
        {
            GridControl gridControl = gcData;
            if (e.SelectedControl != gridControl)
                return;

            GridView view = gridControl.GetViewAt(e.ControlMousePosition) as GridView;
            if (view == null)
                return;
            GridHitInfo hintInfo = view.CalcHitInfo(e.ControlMousePosition);
            if (hintInfo.InRowCell)
            {
                object cellInfo = new CellToolTipInfo(hintInfo.RowHandle, hintInfo.Column, null);
                string cellText = view.GetRowCellDisplayText(hintInfo.RowHandle, hintInfo.Column);

                var column = hintInfo.Column;

                DataRow dr = dtDirConfigInfo.AsEnumerable().FirstOrDefault(x => x["SAVECOLNAME"].Equals(column.FieldName));
                if (dr != null)
                {
                    string fieldNameHint = $"【{dr["DATANAME"]} ({dr["SAVECOLNAME"]})】";
                    string hintText = cellText;//设置单元格提示                
                    e.Info = new ToolTipControlInfo(cellInfo, hintText, fieldNameHint, ToolTipIconType.Information);
                }
            }
        }

        /// <summary>
        /// 选择版本类型切换事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void radioGroup_SelVer_EditValueChanged(object sender, EventArgs e)
        {
            if (searchLookup_catalogueName.EditValue == null)
            {
                XtraMessageBox.Show("请先选择目录！！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            string businessCode = searchLookup_catalogueName.EditValue.ToString();//业务代码
            string catalogName = getTableDescByBusinessCode(businessCode);//目录名
            string tableName = getTableNameByBusinessCode(businessCode);//表名
            string version = txtVer.EditValue.ToString();//版本

            tableName = getTestTableName(tableName); //zzzz临时测试zzzz

            if (!await ReminderSave(dtDownLoadedData, businessCode, catalogName, tableName, version))
                return;

            //获取最大版本号
            getLastestVer(businessCode, catalogName, tableName, out string sLastestVer);
            setVerOptions(sLastestVer);
        }

        /// <summary>
        /// 设置默认版本
        /// </summary>
        /// <param name="lastestVer">最大版本号</param>
        private void setVerOptions(string lastestVer)
        {
            if (radioGroup_SelVer.EditValue.Equals("LASTEST_VER"))//最新版本
            {
                txtVer.Enabled = false;
                txtVer.EditValue = lastestVer;
            }
            else if (radioGroup_SelVer.EditValue.Equals("INIT_VER"))//初始版本
            {
                txtVer.Enabled = false;
                txtVer.EditValue = "0";
            }
            else if (radioGroup_SelVer.EditValue.Equals("SPECIFY_VER"))//指定版本
            {
                txtVer.Enabled = true;
                txtVer.EditValue = lastestVer;
            }
        }

        /// <summary>
        /// 异步获取原表的数据
        /// </summary>
        /// <param name="spc"></param>
        /// <returns></returns>
        public Task<DataTable> Query_OldDataAsync(ServerPublicClient spc, string sql, string tableName)
        {
            return Task.Run(() =>
            {
                return spc.GetDataBySql(sql, tableName).Tables[0]; //原数据表内容
            });
        }


        /// <summary>
        /// 保存到数据库
        /// </summary>
        /// <param name="spc"></param>
        /// <param name="dtOldDataUpdate">更新原表用的数据集</param>
        /// <param name="dtNewDataInsert">插入新数据用的数据集</param>
        /// <param name="businessCode">业务代码</param>
        /// <param name="catalogName">目录名称（数据表描述）</param>
        /// <param name="tableName">数据表名称</param>
        /// <param name="version">版本</param>
        /// <returns></returns>
        private bool save2db(ServerPublicClient spc, DataTable dtOldDataUpdate, DataTable dtNewDataInsert, string businessCode, string catalogName, string tableName, string version)
        {
            OracleBaseClass db = new OracleBaseClass();
            try
            {
                db.OpenDB();
                db.BeginTransaction();

                string msg = "";
                //if (sqlDict.Count > 0)
                //{
                //    string retOldData = spc.SaveExcSql(sqlDict, db);
                //    if (retOldData.Length > 0)
                //    {
                //        msg = $"处理“{tableDesc}({businessCode})”更新原始数据失败（{retOldData}）！";
                //        db.RollbackTransaction();
                //        CommonHelper.WriteLog(msg);
                //        errorMessage = msg;
                //        //XtraMessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                //        return false;
                //    }
                //}

                //如果原表有更新则更新
                DataSet dataSetUpdate = new DataSet();
                dataSetUpdate.Tables.Add(dtOldDataUpdate);
                if (dtOldDataUpdate.DataSet.HasChanges())
                {
                    msg = $"开始保存 更新“{catalogName}({businessCode}，版本：{version})”数据,共{dtOldDataUpdate.Rows.Count}条...";
                    CommonHelper.WriteLog(msg);
                    int retModify = spc.SaveDataSet(dtOldDataUpdate.DataSet, db);
                    if (retModify < 0)
                    {
                        msg = $"更新“{catalogName}({businessCode}，版本：{version})”数据失败！";
                        db.RollbackTransaction();
                        CommonHelper.WriteLog(msg);
                        errorMessage = msg;
                        //XtraMessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return false;
                    }
                    CommonHelper.WriteLog($"更新成功！{catalogName} (版本：{version}) ");
                }
                else
                {
                    CommonHelper.WriteLog($"无待更新的数据，{catalogName} (版本：{version}) ");
                }

                //如果有新数据则插入更新
                DataSet dataSetInsert = new DataSet();
                dataSetInsert.Tables.Add(dtNewDataInsert);
                if (dtNewDataInsert.DataSet.HasChanges())
                {
                    msg = $"开始保存 添加到“{catalogName}({businessCode}，版本：{version})”数据,共{dtNewDataInsert.Rows.Count}条...";
                    CommonHelper.WriteLog(msg);
                    int retAdd = spc.SaveDataSet(dtNewDataInsert.DataSet, db);
                    if (retAdd < 0)
                    {
                        msg = $"添加“{catalogName}({businessCode}，版本：{version})”数据失败！";
                        db.RollbackTransaction();
                        CommonHelper.WriteLog(msg);
                        errorMessage = msg;
                        //XtraMessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return false;
                    }
                    CommonHelper.WriteLog($"添加成功！{catalogName} (版本：{version}) ");
                }
                else
                {
                    CommonHelper.WriteLog($"无待添加的数据，{catalogName} (版本：{version} )");
                }                
                db.CommitTransaction();
                msg = $"保存“{catalogName}({businessCode})，版本：{version}”完成，更新{dtOldDataUpdate.Rows.Count}条数据，添加{dtNewDataInsert.Rows.Count}条数据，共{dtOldDataUpdate.Rows.Count + dtNewDataInsert.Rows.Count}条数据！";
                CommonHelper.WriteLog(msg);
                //XtraMessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return true;
            }
            catch (Exception ex)
            {
                string errmsg = $"保存目录时出错！[{ex.Message}]";
                CommonHelper.WriteLog($"{errmsg}：\r{ex.StackTrace}");
                //XtraMessageBox.Show(this, errmsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                errorMessage = errmsg;
                return false;
            }
            finally
            {
                db.CloseDB();
                db.Dispose();
                dtNewDataInsert.Dispose();
                dtNewDataInsert = null;
                dtOldDataUpdate.Dispose();
                dtOldDataUpdate = null;
            }
        }

        /// <summary>
        /// 得到指定数据表的字段信息
        /// 包括字段名称、字段长度等
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="dtTableInfo"></param>
        /// <returns></returns>
        private bool getTableInfo(string tableName, out DataTable dtTableInfo)
        {
            dtTableInfo = new DataTable();
            try
            {
                if (tableName.Contains("."))//如果表名包含owner，则去掉owner，只留纯表名
                {
                    tableName = tableName.Split('.')[1];
                }
                string strSQL = $@"SELECT DISTINCT A.COLUMN_ID, A.COLUMN_NAME, B.COMMENTS, A.DATA_TYPE, A.TABLE_NAME , C.COMMENTS AS TABLE_COMMENTS, A.OWNER, A.NULLABLE, A.DATA_LENGTH, A.DATA_PRECISION, A.DATA_SCALE, A.HIDDEN_COLUMN 
  FROM ALL_TAB_COLS A
  LEFT JOIN ALL_COL_COMMENTS B
  ON A.TABLE_NAME = B.TABLE_NAME  AND A.COLUMN_NAME = B.COLUMN_NAME AND A.OWNER = B.OWNER
  LEFT JOIN ALL_TAB_COMMENTS C
  ON A.TABLE_NAME = C.TABLE_NAME AND A.OWNER = C.OWNER
 WHERE
    A.TABLE_NAME = UPPER('{tableName.ToUpper()}')
            AND A.HIDDEN_COLUMN <> 'YES'--不取隐藏列
          ORDER BY A.COLUMN_ID";
                dtTableInfo = new ServerPublicClient().GetDataBySql(strSQL, tableName).Tables[0];
                return true;
            }
            catch (Exception ex)
            {
                string errmsg = $"得到指定表的字段信息出错,({tableName})！[{ex.Message}]";
                CommonHelper.WriteLog($"{errmsg}：\r{ex.StackTrace}");
                //XtraMessageBox.Show(this, errmsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                errorMessage = errmsg;
                return false;
            }
        }

        /// <summary>
        /// 得到指定表的关键字段名列表
        /// 提供获得指定表的关键字段名，如：“1320中药配方颗粒目录下载”的关键字段为ZYPFKLDM，非表的首字段，需特殊处理
        /// 其余表目前看应该都是首字段为关键字段
        /// 使用列表是怕以后会有 多个关键字段的联合主键
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="dtDownloaded">下载数据的数据表（为了取表结构）</param>
        /// <param name="keyFieldNames">输出；关键字段名列表</param>
        /// <returns></returns>
        private bool getKeyFieldNames(string tableName, DataTable dtDownloaded, out List<string> keyFieldNames)
        {
            keyFieldNames = new List<string>();
            try
            {
                if (tableName.Contains("1320"))
                {
                    keyFieldNames = new List<string> { "ZYPFKLDM" };//1320中药配方颗粒目录下载”的关键字段为ZYPFKLDM，非表的首字段，需特殊处理
                }
                else if (tableName.Contains("1321"))
                {
                    keyFieldNames = new List<string> { "YLMLBM" }; //1321医疗服务项目（新）目录下载的关键字段为 YLMLBM，非表的首字段，需特殊处理
                }
                else
                {
                    keyFieldNames = new List<string> { dtDownloaded.Columns[0].ColumnName };//其他表都是首字段为关键字段
                }
                return true;
            }
            catch (Exception ex)
            {
                string errmsg = $"得到指定表的关键字段名列表出错,({tableName})！[{ex.Message}]";
                CommonHelper.WriteLog($"{errmsg}：\r{ex.StackTrace}");
                //XtraMessageBox.Show(this, errmsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                errorMessage = errmsg;
                return false;
            }
        }
        /// <summary>
        /// 取已经存在的数据集
        /// 由于像 1320 中药配方颗粒目录这种不能使用一个字段来确定唯一数据
        /// 所以需要在查询数据时的查询条件需特殊处理
        /// 而只有一个关键字段可以确定唯一数据的，则只需要普通的处理即可
        /// </summary>
        /// <param name="spc"></param>
        /// <param name="tableName">表名</param>
        /// <param name="drDownloadData">已下载数据的数据</param>
        /// <param name="dtOldData">输出：已存在的数据数据集</param>
        /// <returns></returns>
        private bool getOldDataDT(ServerPublicClient spc, string tableName, DataRow drDownloadedData, out DataTable dtOldData)
        {
            dtOldData = new DataTable();
            try
            {
                Dictionary<string, string> keyFieldDicts = new Dictionary<string, string>();//能确定唯一数据的关键字段字典，key为字段名，value为字段值

                List<string> keyFieldNames = new List<string>();//关键字段列表
                if (!getKeyFieldNames(tableName, drDownloadedData.Table, out keyFieldNames))
                    return false;

                foreach (var fieldName in keyFieldNames)
                {
                    keyFieldDicts[fieldName] = drDownloadedData[fieldName].ToString();//关键字段及字段值
                }
                //if (tableName.Contains("1320")) //1320 中药配方颗粒目录下载有超过一个关键字段
                //{
                //    string keyFieldNameX = drDownloadedData.Table.Columns["SJWYJLH"].ColumnName;//数据唯一记录号
                //    keyFieldDicts[keyFieldNameX] = drDownloadedData[keyFieldNameX].ToString();
                //    //string keyFieldName0 = drDownloadedData.Table.Columns[0].ColumnName;
                //    //string keyFieldName1 = drDownloadedData.Table.Columns[1].ColumnName;
                //    //keyFieldDicts[keyFieldName0] = drDownloadedData[keyFieldName0].ToString();//关键字段1
                //    //keyFieldDicts[keyFieldName1] = drDownloadedData[keyFieldName1].ToString();//关键字段2
                //}
                //else //其他 目录下载 只有一个关键字段，而且默认都是第1个字段为关键字段
                //{
                //    string keyFieldName = drDownloadedData.Table.Columns[0].ColumnName;//关键字段
                //    keyFieldDicts[keyFieldName] = drDownloadedData[keyFieldName].ToString();
                //}

                // 拼凑sql的where条件
                var whereList = keyFieldDicts.Select(x => $"{x.Key}='{x.Value}'").ToList();
                string whereStatement = string.Join(" and ", whereList); //组合成 aaa='bbb' and ccc='ddd'形式的where语句

                //查询原有数据
                dtOldData = spc.GetDataBySql($"select * from {tableName} where {whereStatement} ").Tables[0];
                return true;
            }
            catch (Exception ex)
            {
                string errmsg = $"获取已存在的数据时出错,({tableName})！[{ex.Message}]";
                CommonHelper.WriteLog($"{errmsg}：\r{ex.StackTrace}");
                //XtraMessageBox.Show(this, errmsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                errorMessage = errmsg;
                return false;
            }
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="spc"></param>       
        /// <param name="businessCode">业务代码</param>
        /// <param name="catalogName">目录名称（数据表描述）</param>
        /// <param name="tableName">数据表名称</param>
        /// <param name="version">版本</param>
        /// <returns>true成功；false失败</returns>
        private bool inner_saveData(ServerPublicClient spc, string businessCode, string catalogName, string tableName, string version)
        {
            //var ppp = dtDownLoadedData.AsEnumerable()
            //    .GroupBy(g => new { no1 = g["ZYPFKLID"], no2 = g["ZYPFKLDM"] })
            //    .Select(p => new { groupKey = p.Key, count = p.Count() })
            //    .Where(x => x.count > 1);

            //var ppp = dtDownLoadedData.AsEnumerable()
            //    .GroupBy(g => g["ZYPFKLDM"].ToString())
            //    .Select(p => new { groupKey = p.Key, count = p.Count() })
            //    .Where(x => x.count > 1).ToList();  //临时测试

            const int SECTION_COUNT = 2000; //分段保存的数量

            //OracleBaseClass db = new OracleBaseClass();
            //dtInsertNewData = dtDownLoadedData.Clone(); //准备插入数据用的datatable
            try
            {
                CommonHelper.WriteLog($"***************************************");
                CommonHelper.WriteLog($"开始保存医保目录 {catalogName} ({tableName}，版本：{version} 表数据，共 {dtDownLoadedData.Rows.Count} 条");

                // string keyFieldName = dtDownLoadedData.Columns[0].ColumnName;

                /*  
                  //string ls_sql = $"select * from " + tableName + " ";//where rownum = 0          
                  string ls_sql = $"select {keyFieldName} from " + tableName + " ";//where rownum = 0          

                  string mess = $"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff")} 开始查询 {tableName} 表数据 sql={ls_sql}。。。。。。";
                  CommonHelper.WriteLog(mess);
                  Debug.WriteLine(mess);

                  stopWatch.Start();

                  DataTable dtKeyFieldName = spc.GetDataBySql(ls_sql, tableName).Tables[0]; //原数据表序号字段的内容

                  stopWatch.Stop();
                  mess = string.Format("{0} 查询 {1} 表数据，用时： 共用时{2:D2}:{3:D2}:{4:D2}.{5}，共{6}条数据, sql={7}",
                       DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff"), tableName, stopWatch.Elapsed.Hours, stopWatch.Elapsed.Minutes, stopWatch.Elapsed.Seconds, stopWatch.Elapsed.Milliseconds, dtKeyFieldName.Rows.Count, ls_sql);
                  CommonHelper.WriteLog(mess);
                  Debug.WriteLine(mess);
                  */
                DataTable dtCatalogue = getTableStructDataTable(spc, tableName);

                DataTable dtInsertNewData = dtDownLoadedData.Clone(); //待插入新数据用的datatable
                DataTable dtUpdateOldData = dtCatalogue.Clone();//待更新原数据用的datatable

                //sqlDict = new Dictionary<string, string>();
                iOldDataModifycnt = 0;
                iNewDataInsertCnt = 0;
                //string ls_Columns = dt_dict.Columns[0].ToString();

                string mess = $"-------- {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff")} 开始处理并保存 {tableName} 表数据, 版本：{version}。。。。。。";
                CommonHelper.WriteLog(mess);
                Debug.WriteLine(mess);

                Stopwatch stopWatch = new Stopwatch();
                stopWatch.Start();

                int saveCount = 0;

                //for (int i = dtDownLoadedData.Rows.Count - 1; i >= 0; i--)
                for (int i = 0; i < dtDownLoadedData.Rows.Count; i++) //从下载的数据中遍历循环
                {
                    DataRow drDownloadedData = dtDownLoadedData.Rows[i];//下载下来的数据
                    //string sNo = drDownloadedData[keyFieldName].ToString();

                    /*   List<string> lstUpdateField = new List<string>();//判断原数据中是否含有新下载的数据行对应的数据，如果有则判断数据是否一致，如果不一致则给出更新字段的语句列表
                       /// 比较新下载的数据与原目录表中的对应数据，
                       /// 如果有对应数据再判断新数据是否与原数据完全一致，
                       /// 如不一致则需要更新对应字段的值
                       if (!compareData(spc, dtInsertNewData, drDownloadedData, tableName, lstUpdateField))
                           return false;

                       if (lstUpdateField.Count > 0)//拼凑update原始数据的sql语句
                       {
                           string sUpdateFields = string.Join(", ", lstUpdateField);//展开列表
                           string sqlUpdate = $"update {tableName} set {sUpdateFields} where {keyFieldName}='{sNo}' ";
                           sqlDict[sqlUpdate] = $"更新原始数据出错！[{sNo}]";
                       }*/


                    //如果新下载的数据在原表中已经存在，则更新原本对应的数据为新下载的数据
                    //DataTable dtOld = spc.GetDataBySql($"select * from {tableName} where {keyFieldName}='{sNo}'").Tables[0];
                    DataTable dtOld = new DataTable();
                    if (!getOldDataDT(spc, tableName, drDownloadedData, out dtOld))//取原有数据，用于看是否有原有数据
                    {
                        return false;
                    }

                    if (dtOld.Rows.Count > 0)//原表中有对应的数据，则更新原表数据
                    {
                        int len = dtDirConfigInfo.Rows.Count;//字段数量
                        //Array.Copy(drDownloadedData.ItemArray, dtOld.Rows[0].ItemArray, len);
                        dtOld.Rows[0].ItemArray = drDownloadedData.ItemArray;
                        if (dtOld.Rows[0].RowState != DataRowState.Modified)
                            dtOld.Rows[0].SetModified();
                        dtUpdateOldData.ImportRow(dtOld.Rows[0]);

                        //iOldDataModifycnt++;
                    }
                    else//原表中没有对应的数据，则添加
                    {
                        dtInsertNewData.ImportRow(drDownloadedData);
                    }

                    //每隔指定的数量执行一次保存（防止下载的内容过多导致内存溢出），还有循环到最后一条记录时执行一次保存
                    if (i > 0 && (i + 1) % SECTION_COUNT == 0 || i == dtDownLoadedData.Rows.Count - 1)
                    {
                        iOldDataModifycnt += dtUpdateOldData.Rows.Count;
                        iNewDataInsertCnt += dtInsertNewData.Rows.Count;
                        CommonHelper.WriteLog($"~~~~保存到数据库，{businessCode},版本:{version}，第{++saveCount}次~~~~");
                        //保存到数据库
                        if (!save2db(spc, dtUpdateOldData, dtInsertNewData, businessCode, catalogName, tableName, version))
                            return false;

                        //
                        dtInsertNewData = dtDownLoadedData.Clone(); //重新准备空的待插入数据用的datatable
                        dtUpdateOldData = dtCatalogue.Clone();//重新准备空的待更新原数据用的datatable
                    }
                }
                stopWatch.Stop();
                mess = string.Format("-------- {0} 处理并保存 {1}表数据完成, 版本:{6}，共用时{2:D2}:{3:D2}:{4:D2}.{5}。。。。。。",
                     DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff"), tableName, stopWatch.Elapsed.Hours, stopWatch.Elapsed.Minutes, stopWatch.Elapsed.Seconds, stopWatch.Elapsed.Milliseconds, version);
                CommonHelper.WriteLog(mess);
                Debug.WriteLine(mess);



                //CommonHelper.WriteLog($"待更新的目录表({tableName})中的数据数量为：{sqlDict.Count}");
                CommonHelper.WriteLog($"保存完成({tableName}, version: {version})，更新到目录表({tableName})中的数据数量为：{iOldDataModifycnt}");
                CommonHelper.WriteLog($"保存完成({tableName}, version: {version})，添加到目录表({tableName})中的新数据量为：{ iNewDataInsertCnt}");
                CommonHelper.WriteLog($"保存完成({tableName}, version: {version})，共处理保存目录表({tableName})的新数据量为：{ iOldDataModifycnt + iNewDataInsertCnt}");

                /*db.OpenDB();
                db.BeginTransaction();

                string msg = "";
                //if (sqlDict.Count > 0)
                //{
                //    string retOldData = spc.SaveExcSql(sqlDict, db);
                //    if (retOldData.Length > 0)
                //    {
                //        msg = $"处理“{tableDesc}({businessCode})”更新原始数据失败（{retOldData}）！";
                //        db.RollbackTransaction();
                //        CommonHelper.WriteLog(msg);
                //        errorMessage = msg;
                //        //XtraMessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                //        return false;
                //    }
                //}

                //如果原表有更新则更新
                if (dtCatalogue.DataSet.HasChanges())
                {
                    msg = $"更新“{tableDesc}({businessCode})”数据，共{dtCatalogue.Rows.Count}...";
                    CommonHelper.WriteLog(msg);
                    int retModify = spc.SaveDataSet(dtCatalogue.DataSet, db);
                    if (retModify < 0)
                    {
                        msg = $"更新“{tableDesc}({businessCode})”数据失败！";
                        db.RollbackTransaction();
                        CommonHelper.WriteLog(msg);
                        errorMessage = msg;
                        //XtraMessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return false;
                    }
                    CommonHelper.WriteLog($"更新成功！{tableDesc} ({tableName}，版本：{this.lastestVerNo} ");
                }
                else
                {
                    CommonHelper.WriteLog($"无待更新的数据，{tableDesc} ({tableName}，版本：{this.lastestVerNo} ");
                }

                //如果有新数据则插入更新
                DataSet dataSet = new DataSet();
                dataSet.Tables.Add(dtInsertNewData);
                if (dtInsertNewData.DataSet.HasChanges())
                {
                    msg = $"添加“{tableDesc}({businessCode})”数据,共{iNewDataInsertCnt}...";
                    CommonHelper.WriteLog(msg);
                    int retAdd = spc.SaveDataSet(dtInsertNewData.DataSet, db);
                    if (retAdd < 0)
                    {
                        msg = $"添加“{tableDesc}({businessCode})”数据失败！";
                        db.RollbackTransaction();
                        CommonHelper.WriteLog(msg);
                        errorMessage = msg;
                        //XtraMessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return false;
                    }
                    CommonHelper.WriteLog("添加成功");
                }
                else
                {
                    CommonHelper.WriteLog("无待添加的数据");
                }

                msg = $"保存“{tableDesc}({businessCode})”完成，共更新{iOldDataModifycnt}条数据，添加{iNewDataInsertCnt}，总数量为{iOldDataModifycnt + iNewDataInsertCnt
                db.CommitTransaction();
                //XtraMessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                */
                dtDownLoadedData.AcceptChanges();
                return true;
            }
            catch (Exception ex)
            {
                string errmsg = $"处理保存目录数据时出错！[{ex.Message}]";
                CommonHelper.WriteLog($"{errmsg}：\r{ex.StackTrace}");
                //XtraMessageBox.Show(this, errmsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                errorMessage = errmsg;
                return false;
            }
            //finally
            //{
            //    db.CloseDB();
            //    db.Dispose();
            //}
        }
        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="spc"></param>
        /// <param name="businessCode">业务代码</param>
        /// <param name="catalogName">目录名称（数据表描述）</param>
        /// <param name="tableName">数据表名称</param>
        /// <param name="version">版本</param>
        /// <returns>true成功，false失败</returns>
        private async Task<bool> saveData2DBAsync(ServerPublicClient spc, string businessCode, string catalogName, string tableName, string version)
        {
            return await Task.Run(() =>
            {
                return inner_saveData(spc, businessCode, catalogName, tableName, version); //原数据表内容
            });
        }

        /// <summary>
        /// 设置控件使能
        /// </summary>
        /// <param name="enabled"></param>
        private void enabledControls(bool enabled)
        {
            btnSave.Enabled = btnDownload.Enabled = btnImport.Enabled = btnExport.Enabled = btnClear.Enabled
                = searchLookup_Interface.Enabled = searchLookup_catalogueName.Enabled = radioGroup_SelVer.Enabled
                = enabled;
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="businessCode">业务代码</param>
        /// <param name="catalogName">目录名称（数据表描述）</param>
        /// <param name="tableName">数据表名称</param>
        /// <param name="version">版本</param>
        /// <returns></returns>
        private async Task<bool> saveData(string businessCode, string catalogName, string tableName, string version)
        {
            //OracleBaseClass db = new OracleBaseClass();
            try
            {
                gvData.ShowLoadingPanel();
                ServerPublicClient spc = new ServerPublicClient();

                //异步执行保存数据
                enabledControls(false);

                bool saveRet = await saveData2DBAsync(spc, businessCode, catalogName, tableName, version);

                enabledControls(true);

                if (saveRet)
                {
                    btnSave.Enabled = false;
                    //XtraMessageBox.Show($"保存“{tableDesc}({businessCode})”完成，共更新{iOldDataModifycnt}条数据，添加{iNewDataInsertCnt}条数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return true;
                }
                else
                {
                    //XtraMessageBox.Show(errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"保存目录时出错！[{ex.Message}]";
                CommonHelper.WriteLog($"{errorMessage}：\r{ex.StackTrace}");
                //XtraMessageBox.Show(this, errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
            finally
            {
                gvData.HideLoadingPanel();
                //db.CloseDB();
                //db.Dispose();
            }
        }


        /// <summary>
        /// 判断数据集状态，提示保存
        /// </summary>
        /// <param name="businessCode">业务代码</param>
        /// <param name="catalogName">目录名称（数据表描述）</param>
        /// <param name="tableName">数据表名称</param>
        /// <param name="version">版本</param>
        /// <returns>保存失败 false，成功true</returns>
        private async Task<bool> ReminderSave(DataTable dataTable, string businessCode, string catalogName, string tableName, string version)
        {
            if (dataTable.DataSet != null && dataTable.DataSet.HasChanges())
            {
                string mess = "下载的数据未保存，是否保存？";
                var rst = XtraMessageBox.Show(mess, "提示", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (rst == DialogResult.Yes)
                {
                    return await saveData(businessCode, catalogName, tableName, version);
                }
                else if (rst == DialogResult.Cancel)
                {
                    return false;
                }
                else
                {
                    clearData();
                    //dataTable.RejectChanges();
                    return true;
                }
            }
            return true;
        }

        /// <summary>
        /// 根据业务代码获取最大的版本号
        /// </summary>
        /// <param name="businessCode">业务代码</param>
        /// <param name="catalogName">目录名称（数据表描述）</param>
        /// <param name="tableName">数据表名称</param>
        /// <param name="version">版本</param>
        /// <param name="lastestVer">输出指定业务代码对应的最大的版本</param>
        private bool getLastestVer(string businessCode, string catalogName, string tableName, out string lastestVer)
        {
            lastestVer = "0";
            try
            {
                string sql = $@"select max(bbh) bbh, max(bbmc) bbmc from {tableName}";
                CommonHelper.WriteLog($"查询 {businessCode} 的最大版本 sql={sql}");
                DataTable dt = new ServerPublicClient().GetDataBySql(sql).Tables[0];
                if (dt.Rows.Count == 0)
                    lastestVer = "0";//无数据版本号从0开始
                else
                {
                    //1308手术目录下载、1314中医疾病目录下载，1307 疾病与诊断目录下载，最大版本使用的是BBH（版本号），其他下载最大版本使用的是BBMC（版本名称）
                    if (businessCode.Contains("1308") //手术目录下载
                        || businessCode.Contains("1314")//1314中医疾病目录
                        || businessCode.Contains("1307") //1307 疾病与诊断目录下载
                        )
                    {
                        //1308和1314 最大版本号使用BBH（版本号）
                        if (dt.Rows[0]["BBH"].Equals(DBNull.Value))
                        {
                            lastestVer = "0";
                        }
                        else
                        {
                            lastestVer = dt.Rows[0]["BBH"].ToString();
                        }
                    }
                    else
                    {
                        //其他下载最大版本使用的是BBMC（版本名称）
                        if (dt.Rows[0]["BBMC"].Equals(DBNull.Value))//使用版本名称
                        {
                            if (!dt.Rows[0]["BBH"].Equals(DBNull.Value))//如果版本名称为空，则判断版本号，如果版本号不为空则使用，否则默认为0
                                lastestVer = dt.Rows[0]["BBH"].ToString();
                            else
                                lastestVer = "0"; //无版本号的也从0开始
                        }
                        else
                        {
                            lastestVer = dt.Rows[0]["BBMC"].ToString();
                        }
                    }
                }

                CommonHelper.WriteLog($"{tableName} 的最大版本为：{lastestVer}");
                return true;
            }
            catch (Exception ex)
            {
                errorMessage = $"获取{businessCode}的最大版本号出错！[{ex.Message}]";
                CommonHelper.WriteLog($"{errorMessage}：\r{ex.StackTrace}");
                //XtraMessageBox.Show(this, errorMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 设置显示本地数据库中指定目录的数据量
        /// </summary>
        /// <param name="businessCode">业务代码</param>
        /// <param name="catalogName">目录名称（数据表描述）</param>
        /// <param name="tableName">数据表名称</param>
        /// <param name="version">版本</param>
        private void setCatalogueInDBCount(string businessCode, string catalogName, string tableName, string version)
        {
            try
            {
                //设置显示数据库中指定表的数据量
                string sql = $"select count(1) from {tableName}";
                CommonHelper.WriteLog($"查询 {businessCode} 的数据量 sql={sql}");
                using (DataTable dataTable = new ServerPublicClient().GetDataBySql(sql).Tables[0])
                {
                    if (dataTable.Rows.Count > 0)
                    {
                        labDataCountInDB.Caption = decimal.Parse(dataTable.Rows[0][0].ToString()).ToString("n0");
                    }
                    else
                    {
                        labDataCountInDB.Caption = "0";
                    }
                    CommonHelper.WriteLog($"本地数据库中{businessCode}的数据量为：{labDataCountInDB.Caption}");
                }
            }
            catch (Exception ex)
            {
                string errmsg = $"获取本地数据库中{businessCode}的数据量出错！[{ex.Message}]";
                CommonHelper.WriteLog($"{errmsg}：\r{ex.StackTrace}");
                XtraMessageBox.Show(this, errmsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 切换目录事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void searchLookup_catalogueName_EditValueChanged(object sender, EventArgs e)
        {
            string businessCode = searchLookup_catalogueName.EditValue.ToString();//业务代码
            string catalogName = getTableDescByBusinessCode(businessCode);//目录名
            string tableName = getTableNameByBusinessCode(businessCode);//表名
            string version = txtVer.EditValue.ToString();//版本

            tableName = getTestTableName(tableName); //zzzz临时测试zzzz

            if (!await ReminderSave(dtDownLoadedData, businessCode, catalogName, tableName, version))
                return;

            clearData();

            //设置默认最大版本号
            getLastestVer(businessCode, catalogName, tableName, out string sLastestVer);
            this.lastestVersion = sLastestVer;
            setVerOptions(sLastestVer);



            //设置显示数据量
            setCatalogueInDBCount(businessCode, catalogName, tableName, version);
        }

        private async void FrmInsur1301_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (searchLookup_catalogueName.EditValue == null)
                return;

            string businessCode = searchLookup_catalogueName.EditValue.ToString();//业务代码
            string catalogName = getTableDescByBusinessCode(businessCode);//目录名
            string tableName = getTableNameByBusinessCode(businessCode);//表名
            string version = txtVer.EditValue.ToString();//版本

            tableName = getTestTableName(tableName); //zzzz临时测试zzzz

            //提示是否保存数据
            if (!await ReminderSave(dtDownLoadedData, businessCode, catalogName, tableName, version))
            {
                e.Cancel = true;//保存失败，取消退出
            }
        }

        private void checkboxAutoSave_CheckedChanged(object sender, EventArgs e)
        {
            if (checkboxAutoSave.Checked)
            {
                checkboxLoopDownloadSave.Enabled = true;
                checkboxLoopDownloadSave.Checked = true;
            }
            else
            {
                checkboxLoopDownloadSave.Enabled = false;
                checkboxLoopDownloadSave.Checked = false;
            }
        }
    }
}
