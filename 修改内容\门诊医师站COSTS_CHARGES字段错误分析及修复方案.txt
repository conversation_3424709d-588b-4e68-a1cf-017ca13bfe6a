================================================================================
门诊医师站 OUTP_ORDERS_STANDARD 表 COSTS 和 CHARGES 字段错误分析及修复方案
================================================================================

项目：Tjhis_Outpdoct_station
分析日期：2025-8-25
问题描述：门诊医师站药品界面中 COSTS 和 CHARGES 字段显示错误金额

================================================================================
一、问题现象
================================================================================

1. 界面显示问题：
   - 门诊医师站药品界面显示的金额与实际应该显示的金额不符
   - 例如：界面显示 5.0，但正确金额应该是 3.4

2. 数据库表现象：
   - OUTP_ORDERS_STANDARD 主表：COSTS=5.000000, CHARGES=5.000000 (错误)
   - OUTP_ORDERS_COSTS_STANDARD 明细表：COSTS=3.400000, CHARGES=3.400000 (正确)
   - CURRENT_PRICE_LIST 价格表：PRICE=3.400000 (正确)

3. 数据一致性问题：
   - 主表和明细表的金额不一致
   - 主表的 COSTS 和 CHARGES 值完全相同（异常）

================================================================================
二、问题根本原因分析
================================================================================

1. 核心问题位置：
   文件：TjhisPlatSource\Tjhis_Outpdoct_station\Dal\OrderDal.cs
   方法：SetDeleteOrderOneCostSql
   行号：第224行

2. 错误代码：
   ```csharp
   sql = string.Format("update OUTP_ORDERS_STANDARD set COSTS={0},CHARGES={0} where his_unit_code =:his_unit_code and  CLINIC_NO =:CLINIC_NO and ORDER_NO=:OrderNo and ORDER_SUB_NO =:OrderSubNo", charges);
   ```

3. 错误分析：
   - 使用了同一个参数 {0} 来设置 COSTS 和 CHARGES 两个字段
   - 两个字段都被设置为 charges 参数的值
   - 导致 COSTS 和 CHARGES 完全相同

4. 正确写法应该是：
   ```csharp
   sql = string.Format("update OUTP_ORDERS_STANDARD set COSTS={0},CHARGES={1} where his_unit_code =:his_unit_code and  CLINIC_NO =:CLINIC_NO and ORDER_NO=:OrderNo and ORDER_SUB_NO =:OrderSubNo", costs, charges);
   ```

================================================================================
三、错误触发的业务场景
================================================================================

1. 主要触发场景：
   - 用户在门诊医师站界面删除医嘱的费用明细项目
   - 修改医嘱数量导致费用重新计算
   - 任何触发费用明细删除和主表金额更新的操作

2. 调用链路：
   ```
   用户删除费用明细
   ↓
   OrderBusiness.DeleteCosts() (第383行)
   ↓
   OrderVersion.SetDeleteOrderOneCostSql()
   ↓
   OrderDal.SetDeleteOrderOneCostSql() (第200行)
   ↓
   执行错误的SQL语句 (第224行)
   ```

3. 具体执行流程：
   - OrderBusiness.DeleteCosts() 第380行：SetCostAmount(0, order, cost)
   - SetCostAmount() 重新计算 order.COSTS 和 order.CHARGES
   - 第383行：OrderVersion.SetDeleteOrderOneCostSql(ref sqls, ref parameters, cost, order.CHARGES)
   - 传递重新计算后的 order.CHARGES 参数
   - 错误SQL将此值同时赋给 COSTS 和 CHARGES

================================================================================
四、COSTS 和 CHARGES 字段的正确计算逻辑
================================================================================

1. 基本计算公式：
   ```csharp
   // 明细表计算
   orderCosts.COSTS = decimal.Round(price * orderCosts.AMOUNT.ToDecimal(1), 精度);
   orderCosts.CHARGES = decimal.Round(chargePrice * orderCosts.AMOUNT.ToDecimal(1), 精度);
   
   // 主表汇总
   order.COSTS = 所有明细的COSTS之和
   order.CHARGES = 所有明细的CHARGES之和
   ```

2. 价格计算流程：
   - 从 CURRENT_PRICE_LIST 表获取基础价格 (price)
   - 调用 f_his21_calc_charge_price_outp 函数计算应收价格 (chargePrice)
   - 根据患者收费类型、项目类别等计算实际应收价格
   - COSTS = 基础价格 × 数量 (计价金额)
   - CHARGES = 应收价格 × 数量 (应收金额)

3. 精度处理：
   - 药品类 (A、B类)：精确到4位小数
   - 非药品类：精确到2位小数

================================================================================
五、修复方案
================================================================================

【推荐方案一：数据库实时计算法（最安全）】

修改文件：TjhisPlatSource\Tjhis_Outpdoct_station\Dal\OrderDal.cs
方法：SetDeleteOrderOneCostSql

```csharp
public void SetDeleteOrderOneCostSql(ref List<string> sqlLst, ref List<DbParameter[]> dbParametersLst, OUTP_ORDERS_COSTS_STANDARD costs, decimal? charges)
{
    // 1. 删除费用明细记录
    string sql = " delete from  OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD  where his_unit_code =:his_unit_code and  CLINIC_NO =:CLINIC_NO and ORDER_NO=:OrderNo and ORDER_SUB_NO =:OrderSubNo and ITEM_NO=:ItemNo";
    DbParameter[] parameters = new DbParameter[]
    {
        db.CreateDbParameter(":his_unit_code",SystemParm.HisUnitCode),
        db.CreateDbParameter(":CLINIC_NO",costs.CLINIC_NO),
        db.CreateDbParameter(":OrderNo",costs.ORDER_NO),
        db.CreateDbParameter(":OrderSubNo",costs.ORDER_SUB_NO),
        db.CreateDbParameter(":ItemNo",costs.ITEM_NO)
    };
    sqlLst.Add(sql);
    dbParametersLst.Add(parameters);
    
    // 2. 删除临时表记录
    sql = " delete from  OUTPDOCT.IND_OUTP_ORDERS_COSTS  where his_unit_code =:his_unit_code and  CLINIC_NO =:CLINIC_NO and ORDER_NO=:OrderNo and ORDER_SUB_NO =:OrderSubNo and ITEM_NO=:ItemNo";
    parameters = new DbParameter[]
    {
        db.CreateDbParameter(":his_unit_code",SystemParm.HisUnitCode),
        db.CreateDbParameter(":CLINIC_NO",costs.CLINIC_NO),
        db.CreateDbParameter(":OrderNo",costs.ORDER_NO),
        db.CreateDbParameter(":OrderSubNo",costs.ORDER_SUB_NO),
        db.CreateDbParameter(":ItemNo",costs.ITEM_NO)
    };
    sqlLst.Add(sql);
    dbParametersLst.Add(parameters);
    
    // 3. 修复：从数据库重新计算主表金额
    sql = @"UPDATE OUTP_ORDERS_STANDARD 
            SET COSTS = (SELECT NVL(SUM(COSTS), 0) FROM OUTP_ORDERS_COSTS_STANDARD 
                        WHERE CLINIC_NO = :CLINIC_NO AND ORDER_NO = :OrderNo AND ORDER_SUB_NO = :OrderSubNo),
                CHARGES = (SELECT NVL(SUM(CHARGES), 0) FROM OUTP_ORDERS_COSTS_STANDARD 
                          WHERE CLINIC_NO = :CLINIC_NO AND ORDER_NO = :OrderNo AND ORDER_SUB_NO = :OrderSubNo)
            WHERE his_unit_code = :his_unit_code AND CLINIC_NO = :CLINIC_NO AND ORDER_NO = :OrderNo AND ORDER_SUB_NO = :OrderSubNo";
    
    parameters = new DbParameter[]
    {
        db.CreateDbParameter(":his_unit_code", SystemParm.HisUnitCode),
        db.CreateDbParameter(":CLINIC_NO", costs.CLINIC_NO),
        db.CreateDbParameter(":OrderNo", costs.ORDER_NO),
        db.CreateDbParameter(":OrderSubNo", costs.ORDER_SUB_NO)
    };
    sqlLst.Add(sql);
    dbParametersLst.Add(parameters);
}
```

【方案一优势】
- 最安全：从数据库实时计算，避免内存和数据库不一致
- 最可靠：不依赖传递的参数值
- 符合数据库设计原则：保证数据一致性
- 彻底解决用户删除操作时被赋予错误值的问题

【方案二：参数传递修复法（次选）】

修改调用方法，传递正确计算后的金额：

1. 修改 OrderBusiness.DeleteCosts 方法：
```csharp
public bool DeleteCosts(OUTP_ORDERS_STANDARD order, OUTP_ORDERS_COSTS_STANDARD cost)
{
    bool saveState = true;
    saveState = order.OrderCosts.Remove(cost);
    if (!saveState)
    {
        return saveState;
    }

    // 先重新计算金额
    SetCostAmount(0, order, cost);

    List<string> sqls = new List<string>();
    List<DbParameter[]> parameters = new List<DbParameter[]>();

    if (cost.STATE != Constants.NEW_ORDER_STATE_STR)
    {
        // 传递正确计算后的金额（需要修改方法签名）
        OrderVersion.SetDeleteOrderOneCostSql(ref sqls, ref parameters, cost, order.COSTS, order.CHARGES);
        saveState = this.db.Excute(sqls, parameters) > 0;
    }
    return saveState;
}
```

2. 修改 SetDeleteOrderOneCostSql 方法签名：
```csharp
public void SetDeleteOrderOneCostSql(ref List<string> sqlLst, ref List<DbParameter[]> dbParametersLst, OUTP_ORDERS_COSTS_STANDARD costs, decimal? orderCosts, decimal? orderCharges)
{
    // ... 删除操作保持不变 ...

    // 修复SQL
    sql = string.Format("update OUTP_ORDERS_STANDARD set COSTS={0},CHARGES={1} where his_unit_code =:his_unit_code and  CLINIC_NO =:CLINIC_NO and ORDER_NO=:OrderNo and ORDER_SUB_NO =:OrderSubNo", orderCosts, orderCharges);

    // ... 参数设置保持不变 ...
}
```

【方案二缺点】
- 需要修改多个方法签名
- 依赖内存中的计算结果
- 存在内存和数据库不一致的风险

六、数据修复脚本
================================================================================

对于已经存在错误数据的记录，可以使用以下SQL脚本进行修复：

```sql
-- 修复 OUTP_ORDERS_STANDARD 表中的错误数据
UPDATE OUTP_ORDERS_STANDARD os
SET COSTS = (
    SELECT NVL(SUM(ocs.COSTS), 0)
    FROM OUTP_ORDERS_COSTS_STANDARD ocs
    WHERE ocs.CLINIC_NO = os.CLINIC_NO
      AND ocs.ORDER_NO = os.ORDER_NO
      AND ocs.ORDER_SUB_NO = os.ORDER_SUB_NO
),
CHARGES = (
    SELECT NVL(SUM(ocs.CHARGES), 0)
    FROM OUTP_ORDERS_COSTS_STANDARD ocs
    WHERE ocs.CLINIC_NO = os.CLINIC_NO
      AND ocs.ORDER_NO = os.ORDER_NO
      AND ocs.ORDER_SUB_NO = os.ORDER_SUB_NO
)
WHERE EXISTS (
    SELECT 1 FROM OUTP_ORDERS_COSTS_STANDARD ocs
    WHERE ocs.CLINIC_NO = os.CLINIC_NO
      AND ocs.ORDER_NO = os.ORDER_NO
      AND ocs.ORDER_SUB_NO = os.ORDER_SUB_NO
);

-- 验证修复结果
SELECT
    os.CLINIC_NO,
    os.ORDER_NO,
    os.ORDER_SUB_NO,
    os.ORDER_TEXT,
    os.COSTS as 主表COSTS,
    os.CHARGES as 主表CHARGES,
    (SELECT SUM(ocs.COSTS) FROM OUTP_ORDERS_COSTS_STANDARD ocs
     WHERE ocs.CLINIC_NO = os.CLINIC_NO AND ocs.ORDER_NO = os.ORDER_NO AND ocs.ORDER_SUB_NO = os.ORDER_SUB_NO) as 明细COSTS汇总,
    (SELECT SUM(ocs.CHARGES) FROM OUTP_ORDERS_COSTS_STANDARD ocs
     WHERE ocs.CLINIC_NO = os.CLINIC_NO AND ocs.ORDER_NO = os.ORDER_NO AND ocs.ORDER_SUB_NO = os.ORDER_SUB_NO) as 明细CHARGES汇总
FROM OUTP_ORDERS_STANDARD os
WHERE os.CLINIC_NO = '2507020000001300009'  -- 替换为具体的门诊号
ORDER BY os.ORDER_NO, os.ORDER_SUB_NO;
```

================================================================================
七、测试验证方案
================================================================================

1. 单元测试：
   - 测试 SetDeleteOrderOneCostSql 方法的SQL生成
   - 验证删除费用明细后主表金额计算正确性
   - 测试各种边界情况（单项目、多项目、全部删除等）

2. 集成测试：
   - 在测试环境中模拟用户删除费用明细操作
   - 验证数据库中主表和明细表数据一致性
   - 测试界面显示金额的正确性

3. 回归测试：
   - 确保修复不影响其他相关功能
   - 测试新增、修改、查询等操作正常
   - 验证不同收费类型的计算正确性

4. 测试用例：
   ```
   测试场景1：删除单个费用明细项目
   - 创建包含多个费用明细的医嘱
   - 删除其中一个明细项目
   - 验证主表金额 = 剩余明细金额之和

   测试场景2：删除最后一个费用明细项目
   - 创建只有一个费用明细的医嘱
   - 删除该明细项目
   - 验证主表金额 = 0

   测试场景3：不同收费类型的计算
   - 测试自费、医保等不同收费类型
   - 验证 COSTS 和 CHARGES 计算正确
   ```

================================================================================
八、部署实施建议
================================================================================

1. 实施步骤：
   - 第一步：在测试环境部署修复代码
   - 第二步：执行数据修复脚本
   - 第三步：进行全面测试验证
   - 第四步：生产环境部署（建议在业务低峰期）
   - 第五步：生产环境数据修复
   - 第六步：监控运行状态

2. 风险控制：
   - 部署前备份相关数据表
   - 准备回滚方案
   - 分批次修复数据，避免长时间锁表
   - 部署后密切监控系统运行状态

3. 监控要点：
   - 监控 OUTP_ORDERS_STANDARD 和 OUTP_ORDERS_COSTS_STANDARD 数据一致性
   - 关注用户反馈的金额显示问题
   - 检查相关业务功能是否正常

================================================================================
九、预防措施
================================================================================

1. 代码审查：
   - 加强对SQL语句的代码审查
   - 特别关注字符串格式化中的参数使用
   - 建立SQL语句编写规范

2. 单元测试：
   - 为关键的金额计算方法编写单元测试
   - 测试覆盖各种业务场景
   - 自动化测试集成到CI/CD流程

3. 数据一致性检查：
   - 定期检查主表和明细表数据一致性
   - 建立数据监控告警机制
   - 开发数据校验工具

4. 开发规范：
   - 建立数据库操作的最佳实践
   - 使用参数化查询避免SQL注入和格式错误
   - 统一金额计算逻辑，避免重复代码

================================================================================
十、总结
================================================================================

本次问题的核心是由于代码中的一个简单错误（SQL格式化参数重复使用）导致的数据不一致问题。虽然错误看似简单，但影响范围较大，涉及到用户界面显示和业务数据准确性。

通过本次分析，我们不仅找到了问题的根本原因，还提供了完整的修复方案和预防措施。推荐使用方案一（数据库实时计算法），因为它最安全可靠，能够彻底解决问题并防止类似问题再次发生。

修复完成后，需要进行充分的测试验证，确保系统稳定运行，用户界面显示正确的金额信息。

================================================================================
文档结束
================================================================================
