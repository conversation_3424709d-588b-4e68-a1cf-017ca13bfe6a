using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;
using PlatCommon.SysBase;
using System;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using Tjhis.Doctor.Station.Base;
using Tjhis.Doctor.Station.Base.DevHelper;
using Tjhis.Doctor.Station.BusinessSrv.Presc;
using Tjhis.Doctor.Station.Common;
using Tjhis.Doctor.Station.Common.Business;
using Tjhis.Doctor.Station.Common.Business.Froms.Presc;
using Tjhis.Doctor.Station.FormBase;
using Tjhis.Doctor.Station.Global;
using Tjhis.Doctor.Station.Global.CachePool;
using Tjhis.Doctor.Station.Global.DevHelper;
using Tjhis.Doctor.Station.Global.Helper;
using Tjhis.Doctor.Station.Global.InputMethod;
using Tjhis.Doctor.Station.Interface;
using Tjhis.Doctor.Station.Interface.RA.Call;
using Tjhis.Doctor.Station.Report;

namespace Tjhis.Doctor.Station.Business.Presc
{
    public partial class frmPrescMain : frmInpBase // frmInpBase
    {
        #region 全局变量 
        private DataRow drPatientInfo;//患者信息       
        private DataTable dtMaster = new DataTable();  /// 本窗口数据暂存
        private DataTable dtDetail = new DataTable(); /// 子表暂存 
        private DataTable dtDetailDel = new DataTable(); /// 子表暂存 
        DataTable dtStorage = new DataTable();
        private Boolean showEnable = true;//窗体是否正常打开
        private Boolean btnEnable = true; //按钮可用状态
        string currentDispensary="";//当前选择的药房
        private Boolean enableEdit = true;//允许操作窗体 
        private int repetitionDefault = 7;//草药默认剂数
        private PrescMainSrv srvPrescMain = new PrescMainSrv();
        private DataTable dtDoctDrugUpload = new DataTable();
        private DataTable dtCDetail = new DataTable();//中药明细记录显示专用dt @meng
        private decimal amountZY = 0; //中药合计用 @meng
        #endregion
        #region 构造函数
        /// <summary>
        /// 住院处方
        /// </summary>
        public frmPrescMain()
        {
            CommonWait.ShowWait();
            try
            {
                Cursor = Cursors.WaitCursor;  
                InitializeComponent();
                ControlInit();
                string patientId = "";
                int visitId = 0;
                showEnable = CommonMethod.GetCurrentPatient(ref patientId, ref visitId);
                CurrentPatientId = patientId;
                CurrentVisitId = visitId;
                InitializeData();
            }
            catch (Exception ex)
            {
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
                //010--程序运行出错，请联系管理查看错误日志，\r\n错误信息：{0} ！
                XtraMessageHelper.Err(Consts.MEDICAL_CARE_COMMON, "010", new string[] { ex.Message });

            }
            finally
            {
                Cursor = Cursors.Default;
                CommonWait.CloseWait();
            }
        }
        /// <summary>
        /// 住院处方
        /// </summary>
        /// <param name="patientId">病人ID</param>
        /// <param name="visitId">住院次数</param>
        public frmPrescMain(string patientId, int visitId)
        {
            CommonWait.ShowWait();
            try
            { 
                InitializeComponent();
                ControlInit();
                CurrentPatientId = patientId;
                CurrentVisitId = visitId;
                InitializeData();
            }
            catch (Exception ex)
            {
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
                //010--程序运行出错，请联系管理查看错误日志，\r\n错误信息：{0} ！
                XtraMessageHelper.Err(Consts.MEDICAL_CARE_COMMON, "010", new string[] { ex.Message });

            }
            finally
            {
                Cursor = Cursors.Default;
                CommonWait.CloseWait();
            }
        }
        #endregion 
        #region 方法 
        #region 控件初始化
        /// <summary>
        /// 控件初始化
        /// </summary>
        private void ControlInit()
        {
            XtraLookUpHelper.Init(lookupDispensary);
            XtraLookUpHelper.Init(cellLookUpDept, cellLookUpWriteoff ,cellLookUpDrugPurpose, cellLookUpPrescStatus , cellLookUpPrescType, cellLookUpUnitInContract, cellLookUpPrescAttr);
            XtraGridViewHelper.GridViewInitReadOnly(gvDrugMaster);
            XtraGridViewHelper.GridViewInitEditable(gvDrugDetail);
            XtraPopupContainerEditHelper.Init(txtDiagnosisName);
            XtraDateEditHelper.InitShortFormat(rideDateFormat);
            XtraButtonHelper.Init(btnRootAdd, btnRootDel, btnRootPresc, btnRootDelPrecs, btnRootChildPresc, btnRootBindingPresc, btnRootRefresh, btnRootSave, btnRootPrint, btnRootClose);
        }
        #endregion 
        #region 初始化调用方法
        /// <summary>
        /// 初始化调用方法
        /// </summary>
        private void InitializeData()
        {
            //中药显示卡数据结构 @meng
            dtCDetail.Columns.Add("DRUG_NAME1", typeof(string));
            dtCDetail.Columns.Add("DOSAGE_EACH1", typeof(decimal));
            dtCDetail.Columns.Add("FREQ_DETAIL1", typeof(string));
            dtCDetail.Columns.Add("CHARGES1", typeof(string));
            dtCDetail.Columns.Add("COUNTRYNAME", typeof(string));
            dtCDetail.Columns.Add("COUNTRYCODE", typeof(string));
            dtCDetail.Columns.Add("SPECIALREQUEST", typeof(string));
            dtCDetail.Columns.Add("ADMINISTRATION", typeof(string));

            lue_specialRequest.DataSource = GetSpecialRequestDict();
            lue_specialRequest.DisplayMember = "ITEM_NAME";
            lue_specialRequest.ValueMember = "ITEM_NAME";
            lue_specialRequest.ShowHeader = false;
            // 草药途径
            CommonDevMethod.BindLookUpAdministrationHerbDict(lue_administration);
            CommonDevMethod.BindPrescDecocTion(lueDecoction);
            CommonDevMethod.BindLookUpUsageDict(lookUpEdit_Usage.Properties);
            if (showEnable)
            {
                if (showEnable)
                {
                    showEnable = CommonPresc.CheckUpDispensarySetUp(null);//药房配置检查
                }
                if (showEnable)
                {
                    #region 合理用药
                    //if (!RationalAdministration.StartUsing()|| Parameter.RATIONAL_ADMINISTRATION != ERationalAdministrationInterface.ManufactorMeiKang)
                    if (!RationalAdministration.StartUsing() || Parameter.PASS_FIRM != "美康")
                    {
                        NWARN.Visible = false;
                    } 
                    #endregion
                    //获取病人信息
                    drPatientInfo = CommonPatientList.GetPatientInfo(CurrentPatientId, CurrentVisitId, ERefreshPrepayment.Yes);
                    enableEdit = CommonPatient.PatientAuthority(this, drPatientInfo);//是否允许操作窗体
                    RemoveEvent();
                    BindingData();//绑定数据 
                    GetParameter();//获取参数
                    OpenEvent();
                    SetControlEdit(enableEdit);//设置控件编辑属性  
                    #region 大通合理用药初始化
                    if (RationalAdministration.StartUsing())
                    {
                        RationalAdministration.InpOrdersOpen(drPatientInfo, dtDetail, SystemParm.LoginUser.USER_NAME, SystemParm.LoginUser.NAME);
                    }
                    #endregion
                    LoadData();
                } 
            } 
        }

        /// <summary>
        /// 特殊要求
        /// </summary>
        /// <returns></returns>
        public DataTable GetSpecialRequestDict()
        {
            string strDecoc = "烘烤;粉碎";
            string[] sa = strDecoc.Split(';');
            DataTable dtRet = new DataTable();
            dtRet.Columns.Add("ITEM_NAME");
            foreach (string s in sa)
            {
                dtRet.Rows.Add(s);
            }
            return dtRet;
        }
        #endregion 初始化调用方法
        #region 获取参数
        /// <summary>
        /// 获取参数
        /// </summary>
        private void GetParameter()
        {
            repetitionDefault = ConvertHelper.ToInt(Parameter.TCM_REPETITION, 7);
        }
        #endregion

        #region 绑定数据
        /// <summary>
        /// 绑定数据
        /// </summary>
        private void BindingData()
        {
            #region 数据绑定LookUpEdit
            #region  绑定处方状态
            CommonDevMethod.BindLookUpPrescStatusDictList(cellLookUpPrescStatus);
            #endregion
            #region 绑定住院处方类型 
            CommonDevMethod.BindLookUpPrescTypeDictList(cellLookUpPrescType);
            #endregion
            #region 绑定合同单位 
            CommonDevMethod.BindLookUpUnitInContractDictList(cellLookUpUnitInContract);
            #endregion
            # region 绑定报销
            CommonDevMethod.BindLookUpWriteoffDictList(cellLookUpWriteoff);
            #endregion
            #region 绑定药房
            CommonDevMethod.BindLookUpDispensaryList(lookupDispensary);
            #endregion
            #endregion 
            #region 抗菌目的
            CommonDevMethod.BindLookUpDrugPurposeDict(cellLookUpDrugPurpose);
            #endregion 
            CommonDevMethod.BindLookUpDept(cellLookUpDept); 
        }
        #endregion
        #region 加载数据
        /// <summary>
        /// 加载数据
        /// </summary>
        public void LoadData()
        {  
            dtMaster = CommonPresc.GetPrescMaster(CurrentPatientId, CurrentVisitId); //srvPrescMain.GetPrescMaster(CurrentPatientId, CurrentVisitId);
            //dtMaster.Columns.Add("sqfx");
            //dtMaster.Columns["sqfx"].ReadOnly = false;
            //foreach (DataRow dr in dtMaster.Rows)
            //{
            //    dr["sqfx"] = "已完成";
            //}
            dtDetail.Clear();
            dtDetail.AcceptChanges();
            gcDrugMaster.DataSource = dtMaster;

            if (dtMaster.Rows.Count< 1 && enableEdit)//无处方信息新增一个处方
            {
                 MasterAdd();//新增
            }
            else
            { 
                if(dtMaster.Rows.Count>0)
                {
                    MasterSelect(gvDrugMaster.RowCount - 1, false);
                } 
            } 
        }
        #endregion 
        #region 设置按钮等可用状态
        /// <summary>
        /// 设置按钮等可用状态
        /// </summary>
        /// <param name="edit">true--可编辑;false--不可以编辑</param>
        private void SetControlEdit(Boolean edit)
        { 
            #region 平台处方闭环
            if (Parameter.PLATFORM_FLAG != "1")
            {
                tsmiClosedLoop.Visible = false;
            }
            #endregion 
            if (edit)
            {
                return;
            } 
            btnEnable = edit;
            btnAdd.Enabled = edit;//加药
            btnDel.Enabled = edit; //减药
            btnPresc.Enabled = edit; //新方
            btnDelPrecs.Enabled = edit; //毁方
            btnChildPresc.Enabled = edit;//子处方
            btnBindingPresc.Enabled = edit; //协定处方
            btnRefresh.Enabled = edit; //刷新
            btnSave.Enabled = edit;//保存  
            btnRootAdd.Enabled = edit;
            btnRootDel.Enabled = edit;
            btnRootPresc.Enabled = edit;
            btnRootDelPrecs.Enabled = edit;
            btnRootChildPresc.Enabled = edit;
            btnRootBindingPresc.Enabled = edit;
            btnRootSave.Enabled = edit;
        }
        #endregion
        #region 设置处方明细
        /// <summary>
        /// 设置处方明细
        /// </summary>
        /// <param name="drMaster">处方主记录</param>
        private void SetPrescDetail(DataRow drMaster)
        { 
            DateTime  prescDate = ConvertHelper.ToDateTime( drMaster["PRESC_DATE"]);
            int presNo = ConvertHelper.ToInt(drMaster["PRESC_NO"].ToString());
            dtDetail = srvPrescMain.GetPrescDetail(prescDate, presNo);
            dtCDetail.Clear();
            if (rdoPrescType.EditValue.Equals("1") && dtDetail.Rows.Count>0)
            {//中医处方明细 @meng
                foreach (DataRow item in dtDetail.Rows)
                {
                    DataRow dr = dtCDetail.NewRow();
                    dr["DRUG_NAME1"] = item["DRUG_NAME"];
                    dr["DOSAGE_EACH1"] = item["DOSAGE_EACH"];
                    dr["FREQ_DETAIL1"] = item["FREQ_DETAIL"];
                    DataRow drInsure = BasicDict.GetDrugInsureInfo(item["DRUG_CODE"].ToString(), item["DRUG_SPEC"].ToString() + item["FIRM_ID"].ToString());
                    if (drInsure != null)
                    {
                        dr["COUNTRYNAME"] = drInsure["INSUR_NAME"];
                        dr["COUNTRYCODE"] = drInsure["INSUR_CODE"];
                    }
                    dr["SPECIALREQUEST"] = item["SPECIAL_REQUEST"];
                    dr["ADMINISTRATION"] = item["ADMINISTRATION"];

                    dtCDetail.Rows.Add(dr);
                }
                gridControl1.DataSource = dtCDetail;
            }
            SetDrugGroup();//设置药品组号标志
            SetDrugToxiProperty();//设置药品的毒理属性   
            #region 合理用药
            RationalAdministration.PrescOpenInit(drPatientInfo,drMaster,ref dtDetail, SystemParm.LoginUser.USER_NAME, SystemParm.LoginUser.NAME);
            #endregion
            //dtDetail.AcceptChanges();
            gcDrugDetail.DataSource = dtDetail;
            dtDetailDel = dtDetail.Clone();
            dtDetailDel.Rows.Clear();
            if (dtDetail.Rows.Count<1 && drMaster["PRESC_STATUS"].ToString().Equals("0"))
            {
                DetailAdd();
            }
        }
        #endregion
        #region 解除事件
        /// <summary>
        /// 解除事件
        /// </summary>
        private void RemoveEvent()
        {
            #region 防止触发事件 先解除事件
            speRepetition.EditValueChanged -= new System.EventHandler(speRepetition_EditValueChanged);
            txtDiagnosisName.TextChanged -= new System.EventHandler(txtDiagnosisName_TextChanged);
            rdoPrescType.EditValueChanged -= new System.EventHandler(rdoPrescType_EditValueChanged);
            chkdischargeTakingIndicator.CheckedChanged -= new System.EventHandler(chkdischargeTakingIndicator_CheckedChanged);
            //chkDecoction.CheckedChanged -= new System.EventHandler(chkDecoction_CheckedChanged);
            lueDecoction.EditValueChanged -= new System.EventHandler(lueDecoction_EditValueChanged);
            txtBindingPrescTitle.TextChanged -= new System.EventHandler(txtBindingPrescTitle_TextChanged);
            lookUpEdit_Usage.EditValueChanged -= new System.EventHandler(txtUsage_TextChanged);
            speCountPerRepetition.EditValueChanged -= new System.EventHandler(speCountPerRepetition_EditValueChanged);
            lookupDispensary.Properties.EditValueChanged -= new
           System.EventHandler(lookupDispensary_EditValueChanged);
            #endregion 
        }
        #endregion
        #region 开启事件
        /// <summary>
        /// 开启事件
        /// </summary>
        private void OpenEvent()
        {
            #region 防止触发事件 增加事件
            speRepetition.EditValueChanged += new System.EventHandler(speRepetition_EditValueChanged);
            txtDiagnosisName.TextChanged += new System.EventHandler(txtDiagnosisName_TextChanged);
            rdoPrescType.EditValueChanged += new System.EventHandler(rdoPrescType_EditValueChanged);
            chkdischargeTakingIndicator.CheckedChanged += new System.EventHandler(chkdischargeTakingIndicator_CheckedChanged);
            //chkDecoction.CheckedChanged += new System.EventHandler(chkDecoction_CheckedChanged);
            lueDecoction.EditValueChanged += new System.EventHandler(lueDecoction_EditValueChanged);
            txtBindingPrescTitle.TextChanged += new System.EventHandler(txtBindingPrescTitle_TextChanged);
            lookUpEdit_Usage.EditValueChanged += new System.EventHandler(txtUsage_TextChanged);
            speCountPerRepetition.EditValueChanged += new System.EventHandler(speCountPerRepetition_EditValueChanged);
            lookupDispensary.Properties.EditValueChanged += new
           System.EventHandler(lookupDispensary_EditValueChanged);
            #endregion
        }
        #endregion
        #region 设置中间的处方主记录的数据
        /// <summary>
        /// 设置中间的处方主记录的数据
        /// </summary>
        /// <param name="drMaster">当前行</param>
        private void SetMasterData(DataRow drMaster)
        {         
            Boolean controlEnabled = true; //处方状态控制控件是否可以编辑 
            RemoveEvent();//解除事件
            chkdischargeTakingIndicator.Checked = false;
            //chkDecoction.Checked = false;
            lueDecoction.EditValue = "0";
            rdoPrescType.Enabled = true;
            txtBindingPrescTitle.Enabled = true;
            rdoRepeatIndicator.Enabled = true;
            chkdischargeTakingIndicator.Enabled = true;
            if (Parameter.GEN_DRUG_ORDER == "1")
            {
                rdoRepeatIndicator.SelectedIndex = 0;//临时医嘱
            }
            else if (Parameter.GEN_DRUG_ORDER == "2")
            {
                rdoRepeatIndicator.SelectedIndex = 1;//长期医嘱
            }
            else
            {
                rdoRepeatIndicator.SelectedIndex = 2;//不生成医嘱
            }
            string prescType = drMaster["PRESC_TYPE"].ToString();//处方类别0--西药；1--草药
            string dischargeTakingIndicator = drMaster["DISCHARGE_TAKING_INDICATOR"].ToString();//出院带药
            string decoction = drMaster["DECOCTION"].ToString();//代煎
            string dispensary = drMaster["DISPENSARY"].ToString();//发药药局 
            string prescStatus = drMaster["PRESC_STATUS"].ToString();//处方状态0--未发药；1--已发药 
            if (!prescStatus.Equals("0"))
            {
                controlEnabled = false;
            }
            txtPrescNo.Text = drMaster["PRESC_NO"].ToString();//处方号
            txtBindingPrescTitle.Text = drMaster["BINDING_PRESC_TITLE"].ToString();//草药处方名称
            lookUpEdit_Usage.EditValue = drMaster["USAGE"].ToString();//草药用法
            txtPayments.Text  = ConvertHelper.ToDecimal(drMaster["PAYMENTS"], 0).ToString("0.00");//应收金额
            txtCosts.Text = ConvertHelper.ToDecimal(drMaster["COSTS"], 0).ToString("0.00");//实收金额
            speRepetition.Text = drMaster["REPETITION"].ToString();//草药剂数
            speCountPerRepetition.Text = drMaster["COUNT_PER_REPETITION"].ToString();//草药每剂煎几份
            txtDiagnosisName.Text = drMaster["DIAGNOSIS_NAME"].ToString();//诊断名称
            txtPrePayment.Text = ConvertHelper.ToDecimal(drPatientInfo["PREPAYMENT"], 0).ToString("0.00");//预交金PREPAYMENT
            if (dischargeTakingIndicator.Equals("1"))//出院带药
            {
                chkdischargeTakingIndicator.Checked = true;
            }
            //if (decoction.Equals("1"))//代煎
            //{
            //    chkDecoction.Checked = true;
            //}
            lueDecoction.EditValue = decoction;
            OpenEvent(); 
            if (prescType.Equals("0"))//处方类别0--西药 
            {
                rdoPrescType.EditValue = "0";
                if (drMaster != null && drMaster.RowState != DataRowState.Added)
                {
                    rdoPrescType.Enabled = false;
                    rdoRepeatIndicator.Enabled = false;
                    rdoRepeatIndicator.SelectedIndex = CommonOrders.GetPrescVsOrdersRepeatIndicator(CurrentPatientId, CurrentVisitId, drMaster);
                }
            }
            else if (prescType.Equals("1"))//处方类别1--草药
            {
                rdoPrescType.EditValue = "1";
                if (drMaster != null && drMaster.RowState != DataRowState.Added)
                {
                    rdoPrescType.Enabled = false;
                    rdoRepeatIndicator.Enabled = false;
                    rdoRepeatIndicator.SelectedIndex = CommonOrders.GetPrescVsOrdersRepeatIndicator(CurrentPatientId, CurrentVisitId, drMaster);
                }
            } 
            SetDispensary(prescType, dispensary, drMaster); 
            string rowState = drMaster.RowState.ToString(); 
            SetMasterEnabled(prescType, controlEnabled, rowState);//设置处方主记录控件的编辑属性
        }
        #endregion
        #region 设置处方主记录控件的编辑属性
        /// <summary>
        /// 设置处方主记录控件的编辑属性
        /// </summary>
        /// <param name="prescType">处方类型</param>
        /// <param name="logicalValue">true--可编辑;false--不可以编辑</param>
        /// <param name="drState">行状态</param>
        private void SetMasterEnabled(string prescType, Boolean logicalValue, string drState)
        {
            txtDiagnosisName.Enabled = logicalValue;//诊断
            lookupDispensary.Enabled= false;//药局 
            txtBindingPrescTitle.Enabled = false;
            if (drState.Equals("Added"))
            {
                txtBindingPrescTitle.Enabled = true;
                lookupDispensary.Enabled = true;
            } 
            speRepetition.Enabled = logicalValue;
            speCountPerRepetition.Enabled = logicalValue;
            lookUpEdit_Usage.Enabled = logicalValue;
            //chkDecoction.Enabled = logicalValue;
            lueDecoction.Enabled = logicalValue;
            chkdischargeTakingIndicator.Enabled = logicalValue;
            if (prescType.Equals("0"))//西药
            {
                lookUpEdit_Usage.Enabled = false;
                speCountPerRepetition.Enabled = false;
                txtBindingPrescTitle.Enabled = false;
                //chkDecoction.Enabled = false;
                lueDecoction.Enabled = false;
                speRepetition.Enabled = false;
            }
            else if (prescType.Equals("1"))//草药
            {
                lookUpEdit_Usage.Enabled = true;
                speCountPerRepetition.Enabled = true;
                txtBindingPrescTitle.Enabled = true;
                //chkDecoction.Enabled = true;
                lueDecoction.Enabled = true;
                speRepetition.Enabled = true;
            } 
        }
        #endregion      
        #region 处方是否被编辑
        /// <summary>
        /// 处方是否被编辑
        /// </summary>
        /// <returns>true--可编辑;false--不可编辑</returns>
        private Boolean PrescModified()
        {
            if (dtMaster.GetChanges() != null || dtDetail.GetChanges() != null)
            {
                return true;
            }
            else
            {
                return false;
            } 
        }
        #endregion
        #region 设置处方的只读属性，不能编辑的
        /// <summary>
        /// 设置处方的只读属性，不能编辑的
        /// </summary>
        private void SetControlReadOnly()
        {
            Boolean logical=true;
            DataRow drMaster = gvDrugMaster.GetDataRow(gvDrugMaster.FocusedRowHandle);//当前选中行
            if (!btnEnable)
            {
                logical = false;
            }
            else
            {
                if (drMaster["PRESC_STATUS"].ToString() == "1") //已发药的处方
                {
                    logical = false;
                }
            }

            string prescDate = drMaster["PRESC_DATE"].ToString();//处方日期
            int prescNo = ConvertHelper.ToInt(drMaster["PRESC_NO"].ToString());//处方号 

            bool isTrans = CommonPresc.CheckUpPrescTranscription(prescDate, ConvertHelper.ToInt(prescNo));
            bool isAddOrDel = false;
            if (logical && isTrans) isAddOrDel = true;
            btnAdd.Enabled = isAddOrDel;//加药
            btnRootAdd.Enabled = isAddOrDel;//加药
            btnDel.Enabled = isAddOrDel; //减药
            btnRootDel.Enabled = isAddOrDel; //减药
            btnDelPrecs.Enabled = logical; //毁方
            btnRootDelPrecs.Enabled = logical; //毁方
            btnChildPresc.Enabled = logical;//子处方
            btnRootChildPresc.Enabled = logical;//子处方
            btnBindingPresc.Enabled = logical; //协定处方
            btnRootBindingPresc.Enabled = logical; //协定处方
            btnRefresh.Enabled = logical; //刷新
            btnRootRefresh.Enabled = logical; //刷新
            btnSave.Enabled = logical;//保存
            btnRootSave.Enabled = logical;//保存 
            //chkdischargeTakingIndicator.Enabled = logical;

        }
        #endregion
        #region 设置列只读属性
        /// <summary>
        /// 设置列只读属性
        /// </summary>
        private void SetColumnReadOnly()
        {
            if (gvDrugDetail.RowCount == 0) return;
            #region 开启编辑属性
            gvDrugDetail.Columns["DRUG_NAME"].OptionsColumn.ReadOnly = false;
            gvDrugDetail.Columns["DRUG_NAME"].OptionsColumn.AllowEdit = true;
            gvDrugDetail.Columns["DOSAGE_EACH"].OptionsColumn.ReadOnly = false;
            gvDrugDetail.Columns["DOSAGE_EACH"].OptionsColumn.AllowEdit = true;
            gvDrugDetail.Columns["ADMINISTRATION"].OptionsColumn.ReadOnly = false;
            gvDrugDetail.Columns["ADMINISTRATION"].OptionsColumn.AllowEdit = true;
            gvDrugDetail.Columns["FREQUENCY"].OptionsColumn.ReadOnly = false;
            gvDrugDetail.Columns["FREQUENCY"].OptionsColumn.AllowEdit = true;
            gvDrugDetail.Columns["DRUG_PURPOSE"].OptionsColumn.ReadOnly = false;
            gvDrugDetail.Columns["DRUG_PURPOSE"].OptionsColumn.AllowEdit = true;
            gvDrugDetail.Columns["FREQ_DETAIL"].OptionsColumn.ReadOnly = false;
            gvDrugDetail.Columns["FREQ_DETAIL"].OptionsColumn.AllowEdit = true;
            gvDrugDetail.Columns["INPUT_AMOUNT"].OptionsColumn.ReadOnly = false;
            gvDrugDetail.Columns["INPUT_AMOUNT"].OptionsColumn.AllowEdit = true;
            #endregion
            #region 设置只读
            DataRow drCurrent = gvDrugDetail.GetDataRow(gvDrugDetail.FocusedRowHandle);//当前选中行
            int antibacterialFlag = ConvertHelper.ToInt(drCurrent["ANTIBACTERIAL_FLAG"], 0);//抗菌用药标志
            int orderSubNo = ConvertHelper.ToInt(drCurrent["ORDER_SUB_NO"], 1); //子序号
            if(antibacterialFlag!=1)
            {
                gvDrugDetail.Columns["DRUG_PURPOSE"].OptionsColumn.ReadOnly = true;
                gvDrugDetail.Columns["DRUG_PURPOSE"].OptionsColumn.AllowEdit = false;
            }
            if (orderSubNo != 1)
            {
                gvDrugDetail.Columns["ADMINISTRATION"].OptionsColumn.ReadOnly = true;
                gvDrugDetail.Columns["ADMINISTRATION"].OptionsColumn.AllowEdit = false;
                gvDrugDetail.Columns["FREQUENCY"].OptionsColumn.ReadOnly = true;
                gvDrugDetail.Columns["FREQUENCY"].OptionsColumn.AllowEdit = false;
            }
            #endregion  
        }
        #endregion
        #region 根据选择的处方类型不同，则明细表展示不同的字段
        /// <summary>
        /// 根据选择的处方类型不同，则明细表展示不同的字段
        /// </summary>
        private void DetailVisible()
        {
            if (gvDrugDetail.RowCount == 0) return;
            string prescType = gvDrugMaster.GetFocusedRowCellValue("PRESC_TYPE").ToString();
            if (rdoPrescType.EditValue.Equals("0") || prescType.Equals("0")) //西药
            {
                gvDrugDetail.Columns["INPUT_AMOUNT"].Visible = true;
                gvDrugDetail.Columns["INPUT_UNITS"].Visible = true;
                gvDrugDetail.Columns["TOXI_PROPERTY"].Visible = true;
                gvDrugDetail.Columns["FREQUENCY"].Visible = true;
                gvDrugDetail.Columns["DRUG_PURPOSE"].Visible = true;
                gvDrugDetail.Columns["SUB_GROUP"].Visible = true; 
                gvDrugDetail.Columns["SUB_GROUP"].VisibleIndex = 0;
                //if (Parameter.RATIONAL_ADMINISTRATION == ERationalAdministrationInterface.ManufactorMeiKang)
                if (RationalAdministration.StartUsing() && (Parameter.PASS_FIRM == "美康"))
                {
                    gvDrugDetail.Columns["NWARN"].VisibleIndex = 1;
                }
                gvDrugDetail.Columns["DRUG_NAME"].VisibleIndex = 2;
                gvDrugDetail.Columns["PACKAGE_SPEC"].VisibleIndex = 3;
                gvDrugDetail.Columns["FIRM_ID"].VisibleIndex = 4;
                gvDrugDetail.Columns["DOSAGE_EACH"].VisibleIndex = 5;
                gvDrugDetail.Columns["DOSAGE_UNITS"].VisibleIndex = 6;
                gvDrugDetail.Columns["ADMINISTRATION"].VisibleIndex = 7;
                gvDrugDetail.Columns["FREQUENCY"].VisibleIndex = 8;
                gvDrugDetail.Columns["DRUG_PURPOSE"].VisibleIndex =9; 
                gvDrugDetail.Columns["FREQ_DETAIL"].VisibleIndex = 10;
                gvDrugDetail.Columns["INPUT_AMOUNT"].VisibleIndex = 11;
                gvDrugDetail.Columns["INPUT_UNITS"].VisibleIndex = 12;
                gvDrugDetail.Columns["COSTS"].VisibleIndex = 13;
                gvDrugDetail.Columns["PAYMENTS"].VisibleIndex = 14;
                gvDrugDetail.Columns["TOXI_PROPERTY"].VisibleIndex = 15;
                layoutControlGroup1.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always;//显示西药页面 @meng
                layoutControlGroup2.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;//隐藏中药页面 @meng

            }
            else if (rdoPrescType.EditValue.Equals("1") || prescType.Equals("1"))  //中药
            {
                gvDrugDetail.Columns["DRUG_PURPOSE"].Visible = false;
                gvDrugDetail.Columns["INPUT_AMOUNT"].Visible = false;
                gvDrugDetail.Columns["INPUT_UNITS"].Visible = false;
                gvDrugDetail.Columns["TOXI_PROPERTY"].Visible = false;
                gvDrugDetail.Columns["FREQUENCY"].Visible = false;
                gvDrugDetail.Columns["SUB_GROUP"].Visible = false;
                gvDrugDetail.Columns["INPUT_AMOUNT"].VisibleIndex = -1;
                gvDrugDetail.Columns["INPUT_UNITS"].VisibleIndex = -1;
                gvDrugDetail.Columns["TOXI_PROPERTY"].VisibleIndex = -1;
                gvDrugDetail.Columns["FREQUENCY"].VisibleIndex = -1;
                gvDrugDetail.Columns["SUB_GROUP"].VisibleIndex = -1;
                //if (Parameter.RATIONAL_ADMINISTRATION == ERationalAdministrationInterface.ManufactorMeiKang)
                if (RationalAdministration.StartUsing() && (Parameter.PASS_FIRM == "美康"))
                {
                    gvDrugDetail.Columns["NWARN"].VisibleIndex = 1;
                }
                gvDrugDetail.Columns["DRUG_NAME"].VisibleIndex = 2;
                gvDrugDetail.Columns["PACKAGE_SPEC"].VisibleIndex = 3;
                gvDrugDetail.Columns["FIRM_ID"].VisibleIndex = 4;
                gvDrugDetail.Columns["DOSAGE_EACH"].VisibleIndex = 5;
                gvDrugDetail.Columns["DOSAGE_UNITS"].VisibleIndex = 6;
                gvDrugDetail.Columns["ADMINISTRATION"].VisibleIndex = 7;
                //gvDrugDetail.Columns["FREQUENCY"].VisibleIndex = 8;
                gvDrugDetail.Columns["DRUG_PURPOSE"].VisibleIndex = 8;
                gvDrugDetail.Columns["FREQ_DETAIL"].VisibleIndex = 9;
                gvDrugDetail.Columns["COSTS"].VisibleIndex = 10;
                gvDrugDetail.Columns["PAYMENTS"].VisibleIndex = 11;
                layoutControlGroup1.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never; //隐藏西药页面 @meng
                layoutControlGroup2.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always; //显示中药页面 @meng
                if (gridControl1.DataSource == null || dtCDetail.Rows.Count == 0)
                {
                    DataRow dr = dtCDetail.NewRow();
                    dtCDetail.Rows.Add(dr);
                    gridControl1.DataSource = dtCDetail;
                    gvDrugDetail.FocusedRowHandle = gvDrugDetail.RowCount - 1;
                    layoutView1.FocusedRowHandle = layoutView1.RowCount - 1;
                }
            }
        }
        #endregion
        #region 根据主记录，显示明细记录
        /// <summary>
        /// 根据主记录，显示明细记录
        /// </summary>
        /// <param name="row">行</param>
        /// <param name="judge">true--判断编辑;false--不判断编辑</param>
        private void MasterSelect(int row ,Boolean judge)
        {
            //if (PrescModified() && judge && btnRootSave.Enabled)
            if (PrescModified() && judge)
            {
                //012 数据已修改，请先保存当前处方！
                XtraMessageHelper.Info(Consts.PRESC, "012");
                int rowIndex = 0;
                int prescNo = 0;
                if (dtDetail.GetChanges() != null && gvDrugDetail.RowCount > 0)
                {
                    prescNo = ConvertHelper.ToInt(dtDetail.Rows[0]["PRESC_NO"].ToString(),0);          
                }
                foreach (DataRow dRow in dtMaster.Rows)
                {
                    if (dRow.RowState == DataRowState.Added || dRow.RowState == DataRowState.Modified)
                    {
                        rowIndex = dRow.Table.Rows.IndexOf(dRow);
                    }
                    else
                    {
                        if (prescNo == ConvertHelper.ToInt(dRow["PRESC_NO"]))
                        {
                            rowIndex = dRow.Table.Rows.IndexOf(dRow);
                        }
                    }
                } 
                gvDrugMaster.FocusedRowHandle = rowIndex;
                return;
            }
            if (gvDrugMaster.FocusedRowHandle > -1)
            {
                gvDrugMaster.UnselectRow(gvDrugMaster.FocusedRowHandle);
            }
            gvDrugMaster.SelectRow(row);
            gvDrugMaster.FocusedRowHandle = row;
            DataRow dr = gvDrugMaster.GetDataRow(row);
            
            SetMasterData(dr);//处方主记录赋值 
            SetPrescDetail(dr);//处方明细 
            if (rdoPrescType.EditValue.Equals("0"))
            {
                layoutControlGroup1.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always;//显示西药页面 @meng
                layoutControlGroup2.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;//隐藏中药页面 @meng
            }
            else
            {
                layoutControlGroup1.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;//隐藏西药页面 @meng
                layoutControlGroup2.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always;//显示中药页面 @meng
            }
            //if (gvDrugMaster.FocusedRowHandle >= 0)
            //{
            //    SetControlReadOnly();
            //    DetailVisible();
            //}
            //dtMaster.AcceptChanges();
            //dtDetail.AcceptChanges();
        }
        #endregion
        #region 明细记录计算汇总金额
        /// <summary>
        /// 明细记录计算汇总金额
        /// </summary>
        /// <param name="drMaster">主记录</param>
        private void DetailComputeMoney(DataRow drMaster)
        {
            decimal computePayments = 0;
            decimal computeCosts = 0;
            DataTable dtClone = dtDetail.Clone();
            foreach (DataRow dr in dtDetail.Rows)
            {
                if (dr.RowState != DataRowState.Deleted)
                {
                    dtClone.Rows.Add(dr.ItemArray);
                }
            }
            if (dtClone.Rows.Count > 0)
            {  
                computePayments = ConvertHelper.ToDecimal(dtClone.Compute("Sum(PAYMENTS)", "").ToString(),0);              
                computeCosts = ConvertHelper.ToDecimal(dtClone.Compute("Sum(COSTS)", "").ToString(),0);
            }
            drMaster["PAYMENTS"] = computePayments;
            drMaster["COSTS"] = computeCosts;
            txtPayments.Text = computePayments.ToString();
            txtCosts.Text = computeCosts.ToString();
        }
        #endregion
        #region 明细记录计算项目金额
        /// <summary>
        /// 明细记录计算项目金额
        /// </summary>
        /// <param name="drMaster">主记录</param>
        /// <param name="drDetail">明细记录</param>
        private void DetailComputeItemMoney(DataRow drMaster, DataRow drDetail)
        {
            //string tempVaule = "";
            decimal quantity = 0;
            string itemClass = "";//项目类别
            string itemCode = "";//项目代码
            string itemName = "";//项目名称
            string itemSpec = "";//项目规格（药品为规格厂家）
            string units = "";//单位
            decimal price = 0;//项目单价
            decimal chargePrice = 0;//应收单价
            string storage = lookupDispensary.EditValue.ToString();
            string firmId= drDetail["FIRM_ID"].ToString();
            itemCode = drDetail["DRUG_CODE"].ToString();//项目代码
            itemName = drDetail["DRUG_NAME"].ToString();//项目名称
            itemSpec = drDetail["PACKAGE_SPEC"].ToString() + drDetail["FIRM_ID"].ToString();//项目规格（药品为规格厂家）
            units = drDetail["PACKAGE_UNITS"].ToString();//单位
            quantity = ConvertHelper.ToDecimal(drDetail["QUANTITY"].ToString(),0);
            if (quantity == 0)
            {
                drDetail["COSTS"] = 0;
                drDetail["PAYMENTS"] = 0;
                return;
            }
            string prescType = drMaster["PRESC_TYPE"].ToString();
            if (prescType.Equals("0"))
            {
                itemClass = "A";//西药
            }
            else
            {
                itemClass = "B";//草药
            }

            //if (CommonClinicVsCharge.GetDrugCalcChargePrice(drPatientInfo["CHARGE_TYPE"].ToString(), itemClass, itemCode, itemName, itemSpec, units, ref price, ref chargePrice))
            if (CommonClinicVsCharge.GetDrugCalcChargePrice(drPatientInfo["CHARGE_TYPE"].ToString(), itemClass, itemCode, itemName, itemSpec, units, firmId,storage, ref price, ref chargePrice))
            {
                drDetail["COSTS"] = price * quantity;
                drDetail["PAYMENTS"] = chargePrice * quantity;

                // 添加调试日志 - 记录费用计算详情
                try
                {
                    decimal dosageEach = ConvertHelper.ToDecimal(drDetail["DOSAGE_EACH"], 0);
                    int repetition = ConvertHelper.ToInt(drMaster["REPETITION"], 1);
                    string logMessage = $"住院处方费用计算 - 药品:{itemName}, 单次剂量:{dosageEach}, 剂数:{repetition}, " +
                                       $"总数量:{quantity}, 单价:{price}, 应收单价:{chargePrice}, " +
                                       $"计算费用:{price * quantity}, 应收费用:{chargePrice * quantity}";

                    string logPath = @"..\Client\LOG\exLOG\住院处方费用计算_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                    string logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + " [INFO] " + logMessage + "\r\n";
                    System.IO.File.AppendAllText(logPath, logEntry);
                }
                catch
                {
                    // 日志记录失败不影响主流程
                }
            }
            DetailComputeMoney(drMaster);//计算汇总金额
        }
        #endregion 明细记录计算项目金额
        #region 检查主处方是否选择正确
        /// <summary>
        /// 检查主处方是否选择正确
        /// </summary>
        /// <param name="captionMess"></param>
        /// <returns></returns>
        private Boolean CheckUpMasterSelect(string captionMess)
        {
            Boolean logicalValue = false;

            if (gvDrugMaster.SelectedRowsCount > 1)
            {
                //022--不能选择多个处方,只能指定一个处方记录！
                XtraMessageHelper.Info(Consts.PRESC, "022"); 
                return logicalValue;
            }
            if (gvDrugMaster.SelectedRowsCount <= 0)
            {
                //023--请指定要操作的处方记录！
                XtraMessageHelper.Info(Consts.PRESC, "023");              
                return logicalValue;
            }
            return true;
        }
        #endregion
        #region 检查主处方是否选择正确
        /// <summary>
        /// 检查主处方是否选择正确
        /// </summary>
        /// <param name="captionMess">提示信息</param>
        /// <param name="drMaster">主记录</param>
        /// <returns></returns>
        private Boolean CheckUpMasterSelect(string captionMess, ref DataRow drMaster)
        {
            Boolean logicalValue = false;
            if (!CheckUpMasterSelect(captionMess))
            {
                return logicalValue;
            }
            int row = gvDrugMaster.FocusedRowHandle;
            drMaster = dtMaster.Rows[row];
            if ((drMaster == null))
            {
                return logicalValue;
            }
            return true;
        }
        #endregion
        #region 检查主处方是否选择正确
        /// <summary>
        /// 检查主处方是否选择正确
        /// </summary>
        /// <param name="captionMess">提示信息</param>
        /// <param name="prescType">处方类型</param>
        /// <returns>true--选择正确;false--选择错误</returns>
        private Boolean CheckUpMasterSelect(string captionMess, ref string prescType)
        {
            Boolean logicalValue = false;
            DataRow drMaster = null;
            if (!CheckUpMasterSelect(captionMess, ref drMaster))
            {
                return logicalValue;
            }
            if(!CheckUpPrescType(captionMess, drMaster))
            {
                return logicalValue;
            }
            prescType = drMaster["PRESC_TYPE"].ToString();
            return true;
        }
        #endregion
        #region 检测处方类别
        /// <summary>
        /// 检测处方类别
        /// </summary>
        /// <param name="captionMess">提示信息</param>
        /// <param name="drMaster">主记录信息</param>
        /// <returns>true--选择正确;false--选择错误</returns>
        private Boolean CheckUpPrescType(string captionMess,DataRow drMaster)
        {
            Boolean logicalValue = false;
            string prescType = drMaster["PRESC_TYPE"].ToString();
            if (string.IsNullOrEmpty(prescType))
            {
                //024-- 请指定处方类别!
                XtraMessageHelper.Info(Consts.PRESC, "024");
                return logicalValue;
            }
            return true;
        }
        #endregion
        #region 检查主处方是否选择正确
        /// <summary>
        /// 检查主处方是否选择正确
        /// </summary>
        /// <param name="captionMess">提示信息</param>
        /// <param name="prescType">处方类型</param>
        /// <param name="drMaster">主记录信息</param>
        /// <returns></returns>
        private Boolean CheckUpMasterSelect(string captionMess, ref string prescType, ref DataRow drMaster)
        {
            Boolean logicalValue = false;
            if (!CheckUpMasterSelect(captionMess, ref drMaster))
            {
                return logicalValue;
            }
            if (!CheckUpPrescType(captionMess, drMaster))
            {
                return logicalValue;
            }
            prescType = drMaster["PRESC_TYPE"].ToString();
            return true;
        }
        #endregion
        #region 加药 
        /// <summary>
        /// 加药
        /// </summary>
        private void DetailAdd()
        {
            string prescType = "";
            if (!CheckUpMasterSelect("加药提示信息", ref prescType))
            {
                return;
            }
            if (dtDetail.Rows.Count > 0)
            {  
                Tjhis.Doctor.Station.Interface.PreWarning.Call.Business.PrescInput(CurrentPatientId, CurrentVisitId, drPatientInfo, ConvertHelper.ToInt(dtMaster.Rows[gvDrugMaster.FocusedRowHandle]["PRESC_NO"]), dtDetail.Rows.Count - 1, dtMaster.Rows[gvDrugMaster.FocusedRowHandle], dtDetail);
            }

            if (prescType.Equals("1"))
            {
                if (layoutView1.RowCount > 0)
                {
                    string drugCName = layoutView1.GetRowCellValue(layoutView1.RowCount - 1, "DRUG_NAME1").ToString();
                    if (string.IsNullOrEmpty(drugCName))
                    {
                        //layoutView1.SelectRow(layoutView1.RowCount - 1);
                        //gvDrugDetail.FocusedRowHandle = gvDrugDetail.RowCount - 1;
                        layoutView1.FocusedRowHandle = layoutView1.RowCount - 1;
                        layoutView1.FocusedColumn = DRUG_NAME1;
                        layoutView1.ShowEditor();
                        return;
                    }
                }
                DataRow drCAdd;
                drCAdd = dtCDetail.NewRow();
                dtCDetail.Rows.Add(drCAdd);
                //gvDrugDetail.FocusedRowHandle = gvDrugDetail.RowCount;
                layoutView1.FocusedRowHandle = layoutView1.RowCount - 1;
                layoutView1.FocusedColumn = DRUG_NAME1;
                layoutView1.ShowEditor();
            }
            else
            {
                if (gvDrugDetail.RowCount > 0)
                {
                    string drugCName = gvDrugDetail.GetRowCellValue(gvDrugDetail.RowCount - 1, "DRUG_NAME").ToString();
                    if (string.IsNullOrEmpty(drugCName))
                    {
                        //gvDrugDetail.SelectRow(gvDrugDetail.RowCount - 1);
                        //gvDrugDetail.FocusedRowHandle = gvDrugDetail.RowCount - 1;
                        gvDrugDetail.FocusedColumn = DRUG_NAME;
                        gvDrugDetail.ShowEditor();
                        return;
                    }
                }
            }

            DataRow drAdd = dtDetail.NewRow();
            //if (prescType.Equals("1"))
            //{
            //    DataRow drLastRow = null;
            //    int selCount = layoutView1.RowCount;// dtDetail.Rows.Count;
            //    if (selCount > 0)
            //    {
            //        drLastRow = dtDetail.Rows[selCount - 1];
            //        drAdd["FREQUENCY"] = drLastRow["FREQUENCY"];
            //        drAdd["ADMINISTRATION"] = drLastRow["ADMINISTRATION"];
            //    }
            //    else
            //    {
            //        drAdd["ADMINISTRATION"] = Parameter.DEFAULT_ADMINISTRATION;
            //    } 
            //}
            dtDetail.Rows.Add(drAdd);
            //中药页添加新药 @meng
            //if (prescType.Equals("1"))
            //{
            //    DataRow drCAdd;
            //    if (layoutView1.RowCount > 0)
            //    {
            //        string drugCName = layoutView1.GetRowCellValue(layoutView1.RowCount - 1, "DRUG_NAME1").ToString();
            //        if (!string.IsNullOrEmpty(drugCName))
            //        {
            //            drCAdd = dtCDetail.NewRow();
            //            dtCDetail.Rows.Add(drCAdd);
            //        }
            //        else
            //        {
            //            layoutView1.SelectRow(layoutView1.RowCount - 1);
            //        }
            //    }
            //}  

            //if (dtDetail.Rows.Count > 0)
            //{
            //    if (rdoPrescType.EditValue.Equals("0"))
            //    {
            //        gvDrugDetail.FocusedRowHandle = gvDrugDetail.RowCount - 1;
            //        gvDrugDetail.FocusedColumn = DRUG_NAME;
            //        gvDrugDetail.ShowEditor();
            //    }
            //    else
            //    {
            //        gvDrugDetail.FocusedRowHandle = gvDrugDetail.RowCount - 1;
            //        layoutView1.FocusedRowHandle = layoutView1.RowCount - 1;
            //        //layoutView1.FocusedColumn = DRUG_NAME1;
            //    }
                
            //}
            gcDrugDetail.DataSource = dtDetail;
            gvDrugDetail.FocusedRowHandle = gvDrugDetail.RowCount - 1;
            gvDrugDetail.FocusedColumn = DRUG_NAME;
            gvDrugDetail.ShowEditor();
        }
        #endregion
        #region 减药
        /// <summary>
        /// 减药
        /// </summary>
        private void DetailDel()
        {
            if (layoutView1.FocusedRowHandle < 0) return;
            DataRow drMaster = null;
            string prescType = "";
            if (!CheckUpMasterSelect("减药提示信息", ref prescType, ref drMaster))
            {
                return;
            }
            if (rdoPrescType.EditValue.Equals("1"))//中药页减药 @meng
            {
                DataRow drCCurrent = layoutView1.GetDataRow(layoutView1.FocusedRowHandle);
                //DataRow[] drCurrent = dtDetail.AsEnumerable().Where(r => r["DRUG_NAME"] == drCCurrent["DRUG_NAME1"]).ToArray();
                DataRow[] drCurrent = dtDetail.Select("DRUG_NAME = '" + drCCurrent["DRUG_NAME1"].ToString() + "'" );
                
                if (drCurrent.Length > 0)
                {
                    Tjhis.Doctor.Station.Interface.PreWarning.Call.Business.PrescDel(CurrentPatientId, CurrentVisitId, drPatientInfo, ConvertHelper.ToInt(drMaster["PRESC_NO"]), gvDrugDetail.LocateByValue("ITEM_NO", ConvertHelper.ToInt(drCurrent[0]["ITEM_NO"])), drMaster, dtDetail);
                    if (drCurrent[0].RowState != DataRowState.Added)
                    {
                        dtDetailDel.Rows.Add(drCurrent[0].ItemArray); //记录删除，保存时先处理
                    }
                    drCurrent[0].Delete();
                }                
                drCCurrent.Delete();
                SetDrugGroup();//设置药品组号标志
                DetailComputeMoney(drMaster);//计算汇总金额
            }
            else
            {
                DataRow drCurrent = gvDrugDetail.GetDataRow(gvDrugDetail.FocusedRowHandle);
                DataRow drNext = gvDrugDetail.GetDataRow(gvDrugDetail.FocusedRowHandle + 1);
                if (drNext != null)
                {
                    int orderSubNoNext = !string.IsNullOrWhiteSpace(drNext["ORDER_SUB_NO"].ToString()) ? int.Parse(drNext["ORDER_SUB_NO"].ToString()) : 0;
                    if (orderSubNoNext > 1)
                    {
                        //025-请先取消后续子处方后再取消当前子处方！
                        XtraMessageHelper.Info(Consts.PRESC, "025");
                        return;
                    }
                }
                Tjhis.Doctor.Station.Interface.PreWarning.Call.Business.PrescDel(CurrentPatientId, CurrentVisitId, drPatientInfo, ConvertHelper.ToInt(drMaster["PRESC_NO"]), gvDrugDetail.LocateByValue("ITEM_NO", ConvertHelper.ToInt(drCurrent["ITEM_NO"])), drMaster, dtDetail);
                if (drCurrent.RowState != DataRowState.Added)
                {
                    dtDetailDel.Rows.Add(drCurrent.ItemArray); //记录删除，保存时先处理
                }
                drCurrent.Delete();
                SetDrugGroup();//设置药品组号标志
                DetailComputeMoney(drMaster);//计算汇总金额
            }
        }
        #endregion
        #region 新方
        /// <summary>
        /// 新增处方
        /// </summary>
        private void MasterAdd()
        { 
            if (PrescModified() && btnRootSave.Enabled)
            {
                //013 数据已修改，必须先保存才能开新方！
                XtraMessageHelper.Info(Consts.PRESC, "013");
                return;
            }
            if (gvDrugMaster.FocusedRowHandle >= 0)
            {
                if (dtMaster.Rows[gvDrugMaster.FocusedRowHandle].RowState == DataRowState.Added)
                {
                    return;
                }
            } 
            //DateTime prescDate = CommonDataBase.GetSysDateTime();
            //int prescNo = CommonPresc.GetPrescNo(); 
            DataRow drNew = dtMaster.NewRow();
            if(   CommonPresc.SetPrescMasterRow(drNew, drPatientInfo))
            {
                dtMaster.Rows.Add(drNew);
            }
            //newdr["PRESC_DATE"] = prescDate;
            //newdr["PRESC_NO"] = prescNo;
            //newdr["PRESC_STATUS"] = 0;
            //newdr["PRESC_TYPE"] = 0;
            //newdr["BINDING_PRESC_TITLE"] = "";
            //newdr["REPETITION"] = repetitionDefault;
            //newdr["COSTS"] = 0;
            //newdr["PAYMENTS"] = 0;
            //newdr["USAGE"] = "";
            //newdr["IDENTITY"] = drPatientInfo["IDENTITY"];
            //newdr["CHARGE_TYPE"] = drPatientInfo["CHARGE_TYPE"].ToString();
            //newdr["ORDERED_BY"] = drPatientInfo["DEPT_CODE"];
            //newdr["PRESCRIBED_BY"] = SystemParm.LoginUser.NAME;//开方医生姓名  
            //newdr["PRESCRIBED_USERCODE"] = SystemParm.LoginUser.USER_NAME; //开方医生代码
            //newdr["PRESC_ATTR"] = "";
            //newdr["DISPENSARY"] = "";
            //newdr["UNIT_IN_CONTRACT"] = drPatientInfo["UNIT_IN_CONTRACT"];
            //newdr["DISPENSING_PROVIDER"] = "";
            //newdr["DIAGNOSIS_NAME"] = drPatientInfo["DIAGNOSIS"];
            //newdr["DISCHARGE_TAKING_INDICATOR"] = 0;
            //if (!string.IsNullOrEmpty(drPatientInfo["ATTENDING_DOCTOR_CODE"].ToString()))
            //{
            //    newdr["DOCTOR_USER"] = drPatientInfo["ATTENDING_DOCTOR_CODE"].ToString(); //主治医生，跨科处置的时候，和开单医生不同，原逻辑为护士站录入的时候使用的字段
            //}
            //else
            //{
            //    newdr["DOCTOR_USER"] = SystemParm.LoginUser.USER_NAME; //开方医生代码()
            //} 
            //newdr["DECOCTION"] = 0;
            //newdr["NEWLY_PRINT"] = 0;
            //newdr["PRESC_SOURCE"] = 1;//处方来源 0-门诊；1-住院；2-手术 
            //newdr["ENTERED_USERCODE"] = SystemParm.LoginUser.USER_NAME;//录入人代码
            //newdr["ENTERED_BY"] = SystemParm.LoginUser.NAME;//录入人姓名
            //newdr["NAME"] = drPatientInfo["NAME"];//姓名
            //newdr["NAME_PHONETIC"] = drPatientInfo["NAME_PHONETIC"];//姓名拼音
            //newdr["SEX"] = drPatientInfo["SEX"];//性别
            //newdr["PATIENT_ID"] = drPatientInfo["PATIENT_ID"];
            //newdr["VISIT_ID"] = drPatientInfo["VISIT_ID"]; 
            //dtMaster.Rows.Add(newdr);
            MasterSelect(gvDrugMaster.RowCount - 1, false); 
            //SetPrescDetail(drNew);//设置处方明细
        }
        #endregion
        #region 毁方 
        /// <summary>
        /// 毁方
        /// </summary>
        /// <param name="drMaster">主记录</param>
        /// <returns>true--成功;false--失败</returns>
        private Boolean MasterDel(DataRow drMaster )
        {
            Boolean logicalValue = false;
            string prescDate = drMaster["PRESC_DATE"].ToString();//处方日期
            string prescNo = drMaster["PRESC_NO"].ToString();//处方号
            string prescType = drMaster["PRESC_TYPE"].ToString(); //处方类型0--西药;1--草药
            string bindingPrescTitle = drMaster["BINDING_PRESC_TITLE"].ToString(); //草药处方名称
            string orderNo = "";
            string orderSubNo = "";
            string orderNoNext = "";
            DataTable dtOrders = new DataTable();
            #region 备用新增删除数据
            DataTable dtSpareOrdersDel = dtOrders.Clone();
            DataTable dtSparePrescMaster = dtMaster.Clone();
            DataRow drSpareMaster = dtSparePrescMaster.NewRow();
            drSpareMaster.ItemArray = drMaster.ItemArray;
            dtSparePrescMaster.Rows.Add(drSpareMaster);
            DataTable dtSparePrescDetailDel = CommonDataTableMethod.GetFindConditionDT(dtDetail, "");
            dtSparePrescDetailDel.TableName = dtDetail.TableName;
            #endregion
            DateTime sysTime = CommonDataBase.GetSysDateTime(); 
            if (drMaster.RowState != DataRowState.Added)//非新增处方需要判断计价属性
            {
                if (!CommonPresc.CheckUpPrescDel(prescDate, ConvertHelper.ToInt(prescNo), "DEL"))//检查处方
                {
                    return logicalValue;
                }
            } 
            #region 移除主表
            DataRow[] drSel = dtMaster.Select("PRESC_DATE='" + prescDate + "' AND PRESC_NO=" + prescNo);
            foreach (DataRow dr in drSel)
            {
                if (drMaster.RowState != DataRowState.Added)
                {
                    dr.Delete();
                } 
            }
            #endregion
            #region 移除子表
            string orderNoList = "";
            string OrderClass = ""; 
            drSel = dtDetail.Select("PRESC_DATE='" + prescDate + "' AND PRESC_NO=" + prescNo); 
            foreach (DataRow dr in drSel)
            {
                if (dr.RowState != DataRowState.Added)
                {
                    if (rdoRepeatIndicator.SelectedIndex == 0 || rdoRepeatIndicator.SelectedIndex == 1)
                    {
                        orderNo = dr["ORDER_NO"].ToString();
                        orderSubNo = dr["ORDER_SUB_NO"].ToString();
                        if (prescType.Equals("0") && !string.IsNullOrEmpty(orderNo)) //西药
                        {
                            OrderClass = "A";
                            orderNoList = orderNoList + orderNo + ",";
                        }
                        if (prescType.Equals("1") && !string.IsNullOrEmpty(orderNo)) //中药
                        {
                            if (orderNoNext != orderNo)
                            {
                                orderNoList = orderNoList + orderNo + ",";
                                OrderClass = "B";
                            }

                        }
                        orderNoNext = orderNo;
                    }
                    
                } 
                dr.Delete();
            }
            #endregion
            if (orderNoList.Length > 0)
            {
                orderNoList = orderNoList.Substring(0, orderNoList.Length - 1);
                if (!CommonOrders.OrderCancelApply(CurrentPatientId, CurrentVisitId, OrderClass, orderNoList, ref dtOrders, true, sysTime))
                {
                    return logicalValue;
                }
            }
            dtSpareOrdersDel = CommonDataTableMethod.GetFindConditionDT(dtOrders, "CURRENT_PRESC_NO=" + prescNo);
            dtSpareOrdersDel.TableName = dtOrders.TableName;
            Boolean SaveState = CommonDataBase.UpdateDataTable("毁方", false, dtMaster, dtDetail, dtOrders);
            if (SaveState)
            { 
                dtMaster.AcceptChanges();
                dtDetail.AcceptChanges();
                LoadData(); //刷新数据
                #region 平台传送
                if (Parameter.PLATFORM_FLAG.Equals("1"))
                {
                    //    IPlatform.SendPrescDel(this, CurrentPatientId, CurrentVisitId, dtSparePrescMaster, dtSparePrescDetailDel, "", "2", CurrentPatientId + "_" + CurrentVisitId, sysTime, SystemParm.LoginUser.NAME, SystemParm.LoginUser.USER_NAME);
                    //    IPlatform.SendOrdersDel(this, CurrentPatientId, CurrentVisitId, dtSpareOrdersDel);
                
                    #region 统一接口 2022-06-22
                    DataTable[] dataTables = new DataTable[2];
                    string strERROR_TEXT = string.Empty;
                    dataTables[0] = dtSparePrescMaster;
                    dataTables[1] = dtSparePrescDetailDel;
                    Tjhis.Interface.Station.Interface_Common.InvokeInterface("Doctws_007", "UPDATE", "DOCTWS", dtSparePrescMaster.Rows[0]["ORDERED_BY"].ToString(), dataTables, ref strERROR_TEXT);

                    dataTables = new DataTable[1];
                    dataTables[0] = dtSpareOrdersDel;
                    Tjhis.Interface.Station.Interface_Common.InvokeInterface("Doctws_010", "UPDATE", "DOCTWS", dataTables[0].Rows[0]["ORDERING_DEPT"].ToString(), dataTables, ref strERROR_TEXT);
                    #endregion
                }
                #endregion

                #region CA 签名
                //if (Parameter.CA_DATASIGN_ENABLED == "1" && PlatCommon.SysBase.SystemParm.LoginUser.CA_ENABLED == "1")
                if (PlatCommon.SysBase.SystemParm.LoginUser.CA_ENABLED == "1")
                {
                    DataTable dtCa = CommonDataTableMethod.GetFindConditionDT(dtSparePrescMaster, "PRESC_DATE='" + prescDate + "' AND PRESC_NO=" + prescNo);
                    if(dtCa!=null&&dtCa.Rows.Count>0&& dtCa.Select("", "", DataViewRowState.Added).Length>0)
                        PlatCommon.SysBase.SystemParm.CaBusiness.CASignData(PlatCommon.SysBase.SystemParm.HisUnitCode
                        , "DOCTWS", this.drPatientInfo["DEPT_CODE"].ToString(), Tjhis.Interface.CA.CaBusiness.CaSignDataType.InpDrugPrescCancel,
                        PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME, "住院处方作废", dtCa.Select("","", DataViewRowState.Added).CopyToDataTable());

                    PlatCommon.SysBase.SystemParm.CaBusiness.CASignData(PlatCommon.SysBase.SystemParm.HisUnitCode
                        , "DOCTWS", this.drPatientInfo["DEPT_CODE"].ToString(), Tjhis.Interface.CA.CaBusiness.CaSignDataType.OrderCancel,
                        PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME, "遗嘱作废", dtSpareOrdersDel);
                    //ICA.UkeySignPresc(CurrentPatientId, CurrentVisitId, dtSparePrescMaster, dtSparePrescDetailDel, "作废");
                    //ICA.UkeySignOrders(CurrentPatientId, CurrentVisitId, dtSpareOrdersDel, "作废");
                }
                #endregion

                #region 决策支持 
                IDecisionSupport.QualityOrdersRecord(CurrentPatientId, CurrentVisitId, dtSpareOrdersDel,
                                                    dtSparePrescMaster, dtSparePrescDetailDel
                                                    );
                #endregion
            }
            return SaveState;
        } 
        private Boolean MasterDel()
        {
            DataRow drMaster = null;
            if (!CheckUpMasterSelect("毁方提示信息", ref drMaster))
            {
                return false;
            }
            if (drMaster.RowState == DataRowState.Added || PrescModified())
            {
                //018-当前处方未保存，不能进行毁方操作,如需要清除数据，请点击【刷新】按钮！
                XtraMessageHelper.Info(Consts.PRESC, "018");              
                return false;
            }
            //019-确实要毁方吗？            
            if (XtraMessageHelper.YesNo(Consts.PRESC, "019") != DialogResult.Yes)
            {
                return false;
            }
            if (MasterDel(drMaster))
            {
                //需要刷新医嘱界面，待封装方法
                //020--毁方成功!
                XtraMessageHelper.Info(Consts.PRESC, "020");
            }
            else
            {
                return false;
            }
            return true;
        }
        #endregion
        #region 设置子处方
        /// <summary>
        /// 设置子处方
        /// </summary>
        private void SetUpChildresc()
        {
            string prescType = "";
            DataRow drMaster = null;
            if (!CheckUpMasterSelect("设置子处方", ref prescType, ref drMaster))
            {
                return ;
            }  
            if (prescType.Equals("1"))
            {
                //026-草药不需要设置子处方!
                XtraMessageHelper.Info(Consts.PRESC, "026");
                return;
            }
            if (drMaster.RowState != DataRowState.Added)
            {
                //027-已保存处方不允许再调整子处方!
                XtraMessageHelper.Info(Consts.PRESC, "027");
                return;
            } 
            if (gvDrugDetail.SelectedRowsCount > 1)
            {
                //028-请选择一行要设置的子处方!
                XtraMessageHelper.Info(Consts.PRESC, "028");
                return;
            } 
            if (gvDrugDetail.FocusedRowHandle == 0)
            {
                //029-第一条药品不能作为子处方!
                XtraMessageHelper.Info(Consts.PRESC, "029");
                return;
            }
            DataRow drCurrent = gvDrugDetail.GetDataRow(gvDrugDetail.FocusedRowHandle);//当前选中行
            DataRow drPrevious = gvDrugDetail.GetDataRow(gvDrugDetail.FocusedRowHandle - 1);//当前选中行的上一行
            DataRow drNext = gvDrugDetail.GetDataRow(gvDrugDetail.FocusedRowHandle + 1);//当前选中行的下一行
            string orderSubNo = ConvertHelper.ToString(drCurrent["ORDER_SUB_NO"],"1"); //当前行的医嘱子序号
            string orderSubNoPre = ConvertHelper.ToString(drPrevious["ORDER_SUB_NO"], "1"); //上一行的医嘱子序号
            string orderSubNext = "";
            if (drNext != null)
            {
                orderSubNext = ConvertHelper.ToString(drNext["ORDER_SUB_NO"].ToString(),"1");//当前行下一行的医嘱子序号
            }   
            if (ConvertHelper.ToInt(orderSubNo) > 1)//取消设置子处方
            { 
                if (drNext != null)
                {
                    int orderSubNoNext = !string.IsNullOrWhiteSpace(drNext["ORDER_SUB_NO"].ToString()) ? int.Parse(drNext["ORDER_SUB_NO"].ToString()) : 0;
                    if (orderSubNoNext > 1)
                    {
                        //030--请先取消后续子处方再取消当前子处方
                        XtraMessageHelper.Info(Consts.PRESC, "030");
                        return;
                    }
                }
                drCurrent["ORDER_SUB_NO"] = "1";
                drCurrent["ORDER_NO"] = DBNull.Value; 
            }
            else//设置子处方
            { 
                if (drNext != null && ConvertHelper.ToInt(orderSubNext) > 1)
                {
                    //031-不能将有子处方的处方设为子处方！
                    XtraMessageHelper.Info(Consts.PRESC, "031");
                    return;
                }
                drPrevious["ORDER_SUB_NO"] = ConvertHelper.ToInt(orderSubNoPre);
                drCurrent["ORDER_NO"] = drPrevious["ORDER_NO"];
                drCurrent["ORDER_SUB_NO"] = ConvertHelper.ToInt(orderSubNoPre) + 1;
                drCurrent["ADMINISTRATION"] = drPrevious["ADMINISTRATION"];
                drCurrent["FREQUENCY"] = drPrevious["FREQUENCY"];
            } 
            SetDrugGroup();//设置药品组号标志
        }
        #endregion
        #region 设置药品组号标志
        /// <summary>
        /// 设置药品组号标志
        /// </summary>
        private void SetDrugGroup()
        {
            CommonDataTableMethod.SetGroupMarkAll(dtDetail, "ORDER_SUB_NO", "SUB_GROUP");
            //DataRow[] drDetail = dtDetail.Select();
            //int i = 0;
            //foreach (DataRow dr in drDetail)
            //{
            //    i++;
            //    dr["SUB_GROUP"] = "";
            //    string valueTemp = "";
            //    int orderSubNo = 0;//当前循环的子序号
            //    int orderSubNoNext = 0;//当前循环的下一行子序号
            //    int orderSubNoPre = 0;//当前循环的上一行子序号
            //    valueTemp = ConvertHelper.ToString(dr["ORDER_SUB_NO"].ToString(), "1");
            //    orderSubNo = ConvertHelper.ToInt(dr["ORDER_SUB_NO"].ToString(), 1);
            //    //取下一行的子序号
            //    if (i + 1 <= drDetail.Count())
            //    {
            //        orderSubNoNext = ConvertHelper.ToInt(drDetail[i]["ORDER_SUB_NO"].ToString(), 1);
            //    }
            //    else
            //    {
            //        orderSubNoNext = 1;
            //    }
            //    //取上一行的子序号
            //    if (i - 1 <= drDetail.Count() && i - 1 > 0)
            //    {
            //        orderSubNoPre = ConvertHelper.ToInt(drDetail[i - 1]["ORDER_SUB_NO"].ToString(), 1);
            //    }
            //    else
            //    {
            //        orderSubNoPre = 1;
            //    }
            //    //处理序号为1的情况
            //    if (orderSubNo == 1)
            //    {

            //        if (orderSubNoNext > 1)
            //        {
            //            dr["SUB_GROUP"] = "╔";
            //        }
            //    }
            //    else if (orderSubNo > 1)  
            //    {
            //        if (orderSubNoNext == 1)
            //        {
            //            dr["SUB_GROUP"] = "╚";
            //        }
            //        else
            //        {
            //            dr["SUB_GROUP"] = "║";
            //        }
            //    }
            //}
        }
        #endregion 
        #region 打印
        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="prescType">处方类型</param>
        /// <param name="prescDate">处方日期</param>
        /// <param name="prescNo">处方号</param>
        private void PrescPrint( int prescType, DateTime prescDate, int prescNo)
        {
            ReportMethod.PrescPrint(prescType, prescDate,prescNo);
        }
        /// <summary>
        /// 处方打印
        /// </summary>
        private void PrescPrint( )
        {
            string prescType="";
            DataRow drMaster = null;
            if(!CheckUpMasterSelect("打印", ref prescType, ref drMaster))
            {
                return;
            }
            if (drMaster.RowState == DataRowState.Added)
            {
                return;
            }
            ReportMethod.PrescPrint(ConvertHelper.ToInt(prescType), ConvertHelper.ToDateTime(drMaster["PRESC_DATE"].ToString()), ConvertHelper.ToInt(drMaster["PRESC_NO"].ToString()));
        }
        #endregion
        #region 检测数据
        /// <summary>
        /// 检测数据
        /// </summary>
        /// <param name="drMaster">主记录</param>
        /// <returns>true--检测数据成功;false--检测数据失败</returns>
        private Boolean CheckedData(ref DataRow drMaster)
        {
            try
            {
                Boolean logicalValue = false;
                //判断出院病人，不能再写医嘱，申请检查检验等           
                if (CommonPatient.GetWhetherLeaveHospital(CurrentPatientId, CurrentVisitId, true))
                {
                    return logicalValue;
                }
                string prescType = "";//处方类型0--西药；1草药
                if (!CheckUpMasterSelect("校验数据", ref prescType, ref drMaster))
                {
                    return logicalValue;
                }

                if (chkdischargeTakingIndicator.Checked && rdoRepeatIndicator.SelectedIndex == 1)
                {
                    XtraMessageBox.Show("出院带药不允许生成长期医嘱!", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }

                string prescDate = drMaster["PRESC_DATE"].ToString();//处方日期
                int prescNo = ConvertHelper.ToInt(drMaster["PRESC_NO"].ToString());//处方号 
                                                                                   //根据处方状态和医嘱计费判断是否处方可以保存
                if (drMaster.RowState != DataRowState.Added && !CommonPresc.CheckUpPrescDel(prescDate, prescNo, "UPDATE"))
                {
                    return logicalValue;
                }
                #region 循环删除处方明细中名称为空的药品
                if (gvDrugDetail.RowCount > 0)
                {
                    for (int i = dtDetail.Rows.Count; i > 0; i--)
                    {
                        DataRow dRow = dtDetail.Rows[i - 1];
                        if (dRow.RowState != DataRowState.Deleted)
                        {
                            if (string.IsNullOrEmpty(dRow["DRUG_NAME"].ToString()))
                            {
                                dtDetail.Rows[i - 1].Delete();
                            }
                        }
                    }
                }
                #endregion

                #region 明细数据为空处理处方
                if (gvDrugDetail.RowCount == 0)
                {
                    if (drMaster.RowState == DataRowState.Added)//处方主记录为新增状态
                    {
                        //032-请新增处方明细后再保存!
                        XtraMessageHelper.Info(Consts.PRESC, "032");
                        return logicalValue;
                    }
                    if (drMaster.RowState != DataRowState.Added)//已经存在的处方,无明细记录，按作废处理。
                    {
                        return true;
                    }
                }
                #endregion

                #region 检测处方主记录
                int repetition = ConvertHelper.ToInt(drMaster["REPETITION"].ToString(), -1);//剂数
                string bindingPrescTitle = drMaster["BINDING_PRESC_TITLE"].ToString();//处方名称
                string diagnosisName = drMaster["DIAGNOSIS_NAME"].ToString();//诊断
                if (prescType.Equals("1"))//草药的判断
                {
                    if (repetition <= 0)
                    {
                        //033-草药请输入剂数!
                        XtraMessageHelper.Info(Consts.PRESC, "033");
                        speRepetition.Focus();
                        return logicalValue;
                    }
                    if (string.IsNullOrEmpty(bindingPrescTitle))
                    {
                        //034-草药请输入处方名称!
                        XtraMessageHelper.Info(Consts.PRESC, "034");
                        txtBindingPrescTitle.Focus();
                        return logicalValue;
                    }
                }
                if (string.IsNullOrEmpty(diagnosisName))
                {
                    //035-请输入处方的诊断!
                    XtraMessageHelper.Info(Consts.PRESC, "035");
                    txtDiagnosisName.Focus();
                    return logicalValue;
                }
                #endregion

                #region 循环检查处方明细数据
                for (int i = 0; i < dtDetail.Rows.Count; i++)
                {
                    DataRow dr = dtDetail.Rows[i];
                    if (dr.RowState == DataRowState.Deleted)
                    {
                        continue;
                    }
                    dr["PRESC_DATE"] = drMaster["PRESC_DATE"];
                    dr["PRESC_NO"] = drMaster["PRESC_NO"];
                    dr["ITEM_NO"] = i + 1;
                    dr["HIS_UNIT_CODE"] = PlatCommon.SysBase.SystemParm.HisUnitCode ;
                    string drugName = dr["DRUG_NAME"].ToString();//药品名称
                    string drugCode = dr["DRUG_CODE"].ToString();//药品代码
                    string drugSpecFrim = dr["PACKAGE_SPEC"].ToString() + dr["FIRM_ID"].ToString();//规格厂家
                    DataTable dtDrugInfo = null;
                    if (CommonDrug.GetDrugInfo(drugCode, drugName, drugSpecFrim, ref dtDrugInfo) == -1) //停价则返回
                    {
                        CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, i, DRUG_NAME);
                        return logicalValue;
                    }
                    int antibacterialFlag = ConvertHelper.ToInt(dr["ANTIBACTERIAL_FLAG"], 0);//抗菌药物标志
                    string drugPurpose = ConvertHelper.ToString(dr["DRUG_PURPOSE"], "");//用药目的
                    decimal dosageEach = ConvertHelper.ToDecimal(dr["DOSAGE_EACH"].ToString(), 0);
                    if (dosageEach <= 0)//单次剂量
                    {
                        //036-第{0}行药品【{1}】，单次剂量不合法！
                        XtraMessageHelper.Info(Consts.PRESC, "036", new string[] { (i + 1).ToString(), drugName });
                        CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, i, DOSAGE_EACH);
                        return logicalValue;
                    }
                    decimal dosage = ConvertHelper.ToDecimal(dr["DOSAGE"].ToString(), 0);
                    if (dosage <= 0)//最小单位剂量
                    {
                        //037-第{0}行药品【{1}】，请最小单位剂量（DOSAGE）不合法！
                        XtraMessageHelper.Info(Consts.PRESC, "037", new string[] { (i + 1).ToString(), drugName });
                        CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, i, DOSAGE);
                        return logicalValue;
                    }
                    decimal maxDosage = ConvertHelper.ToDecimal(dr["MAX_DOSAGE"].ToString(), 0);//单次最大用量
                    if (maxDosage > 0 && dosageEach > maxDosage)
                    {
                        //038-第{0}行药品【{1}】，单次剂量超过最大剂量【{2}】！
                        XtraMessageHelper.Info(Consts.PRESC, "038", new string[] { (i + 1).ToString(), drugName, maxDosage.ToString() });
                        CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, i, DOSAGE);
                        //return logicalValue;
                    }
                    if (string.IsNullOrEmpty(dr["DOSAGE_UNITS"].ToString()))//剂量单位
                    {
                        //039-第{0}行药品【{1}】，剂量单位不能为空！
                        XtraMessageHelper.Info(Consts.PRESC, "039", new string[] { (i + 1).ToString(), drugName });
                        CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, i, DOSAGE_UNITS);
                        return logicalValue;
                    }
                    if (string.IsNullOrEmpty(dr["ADMINISTRATION"].ToString()))//途径
                    {                        
                        if (prescType.Equals("1"))
                        {
                            dr["ADMINISTRATION"] = "水煎服";
                        }
                        else
                        {
                            //040-第{0}行药品【{1}】，请选择途径！
                            XtraMessageHelper.Info(Consts.PRESC, "040", new string[] { (i + 1).ToString(), drugName });
                            CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, i, ADMINISTRATION);
                            return logicalValue;
                        }                        
                    }
                    if (string.IsNullOrEmpty(dr["FREQUENCY"].ToString()) && prescType.Equals("0"))//频次
                    {
                        //041-第{0}行药品【{1}】，请选择频次！
                        XtraMessageHelper.Info(Consts.PRESC, "041", new string[] { (i + 1).ToString(), drugName });
                        CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, i, FREQUENCY);
                        return logicalValue;
                    }
                    int inputAmount = ConvertHelper.ToInt(dr["INPUT_AMOUNT"].ToString(), 0);//输入数量
                    if (inputAmount <= 0)
                    {
                        //042-第{0}行药品【{1}】，总量不合法！
                        XtraMessageHelper.Info(Consts.PRESC, "042", new string[] { (i + 1).ToString(), drugName });
                        CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, i, INPUT_AMOUNT);
                        return logicalValue;
                    }
                    decimal maxPrescDosage = ConvertHelper.ToDecimal(dr["MAX_PRESC_DOSAGE"].ToString());//单处方最大开药量
                    if (maxPrescDosage > 0 && inputAmount > maxPrescDosage)
                    {
                        //043-第{0}行药品【{1}】，总量超过最大总量【{2}】！
                        XtraMessageHelper.Info(Consts.PRESC, "043", new string[] { (i + 1).ToString(), drugName, maxPrescDosage.ToString() });
                        CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, i, INPUT_AMOUNT);
                        //return logicalValue;
                    }
                    //药品的价格等于0 不能保存成功处方 
                    if (ConvertHelper.ToDecimal(dr["COSTS"].ToString(), 0) <= 0 || ConvertHelper.ToDecimal(dr["PAYMENTS"].ToString(), 0) < 0) //明细计价金额 
                    {
                        //044--第{0}行药品【{1}】，价表错误！
                        XtraMessageHelper.Info(Consts.PRESC, "044", new string[] { (i + 1).ToString(), drugName });
                        CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, i, DRUG_NAME);
                        return logicalValue;
                    }
                    if (antibacterialFlag == 1 && string.IsNullOrEmpty(drugPurpose))
                    {
                        //072--第{0}行药品【{1}】，请选择用药目的！
                        XtraMessageHelper.Info(Consts.PRESC, "072", new string[] { (i + 1).ToString(), drugName });
                        CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, i, DRUG_PURPOSE);
                        return logicalValue;
                    }
                    decimal decQUANTITY = ConvertHelper.ToDecimal(dr["QUANTITY"].ToString(), 0);//数量

                    #region 判断库存 开始               
                    if (!CommonDrug.CheckUpDrugStock(drugCode, drugName, drugSpecFrim, currentDispensary, decQUANTITY))
                    {
                        CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, i, DRUG_NAME);
                        return logicalValue;
                    }
                    #endregion


                    #region 根据批号取库存数量 20220426  
                    //string strPACKAGE_SPEC = dr["PACKAGE_SPEC"].ToString();
                    //string strPACKAGE_UNITS = dr["PACKAGE_UNITS"].ToString();
                    //string strFIRM_ID = dr["FIRM_ID"].ToString();
                    //Boolean boolDRUG = false; //是否满足条件
                    //string strFILTER_TEMP = string.Empty;
                    //string strDRUG_STOCK_MANAGE = " SELECT STORAGE, DRUG_CODE, DRUG_SPEC, UNITS, ";
                    //strDRUG_STOCK_MANAGE += " PACKAGE_SPEC, PACKAGE_UNITS, FIRM_ID, ";
                    //strDRUG_STOCK_MANAGE += " BATCH_NO, BATCH_CODE, EXPIRE_DATE, GUID, ";
                    //strDRUG_STOCK_MANAGE += " PURCHASE_PRICE, TRADE_PRICE, RETAIL_PRICE, QUANTITY ";
                    //strDRUG_STOCK_MANAGE += " FROM OUTPDOCT.V_DOCTOR_DRUG_STOCK_MANAGE  ";
                    //strDRUG_STOCK_MANAGE += " WHERE STORAGE = '" + currentDispensary + "'";
                    //strDRUG_STOCK_MANAGE += " AND DRUG_CODE = '" + drugCode + "'";
                    //strDRUG_STOCK_MANAGE += " AND PACKAGE_SPEC   = '" + strPACKAGE_SPEC + "'";
                    //strDRUG_STOCK_MANAGE += " AND PACKAGE_UNITS = '" + strPACKAGE_UNITS + "'";
                    //strDRUG_STOCK_MANAGE += " AND FIRM_ID = '" + strFIRM_ID + "'";
                    //strDRUG_STOCK_MANAGE += " ORDER BY SERIAL_NO ";

                    //DataSet dataSetDRUG_STOCK_MANAGE = new NM_Service.NMService.ServerPublicClient().GetDataBySql(strDRUG_STOCK_MANAGE);

                    //for (int k = 0; k < dataSetDRUG_STOCK_MANAGE.Tables[0].Rows.Count; k++)
                    //{
                    //    DataRow dataRowMANAGE = dataSetDRUG_STOCK_MANAGE.Tables[0].Rows[k];
                    //    string strBATCH_NO = dataRowMANAGE["BATCH_NO"].ToString();
                    //    string strBATCH_CODE = dataRowMANAGE["BATCH_CODE"].ToString();
                    //    string strEXPIRE_DATE = dataRowMANAGE["EXPIRE_DATE"].ToString();
                    //    string strGUID = dataRowMANAGE["GUID"].ToString();
                    //    string strTRADE_PRICE = dataRowMANAGE["TRADE_PRICE"].ToString();
                    //    string strITEM_PRICE = dataRowMANAGE["RETAIL_PRICE"].ToString();
                    //    string strQUANTITY = dataRowMANAGE["QUANTITY"].ToString();
                    //    decimal decDRUGSTOCK_QUANTITY = Convert.ToDecimal(strQUANTITY); //实际库存数

                    //    //待发药数量记录
                    //    StringBuilder sbb = new StringBuilder();
                    //    sbb.Append(" select nvl(sum(quantity),0) from doct_drug_presc_detail ddpd , doct_drug_presc_master  ddpm   ");
                    //    sbb.Append(" where ddpd.presc_date = ddpm.presc_date");
                    //    sbb.Append(" and ddpd.presc_no = ddpm.presc_no");
                    //    sbb.Append(" and ddpm.dispensary = :t1");
                    //    sbb.Append(" and ddpd.drug_code = :t2  ");
                    //    sbb.Append(" and ddpd.package_spec = :t3 ");
                    //    sbb.Append(" and ddpd.package_units = :t4 ");
                    //    sbb.Append(" and ddpd.firm_id = :t5 ");
                    //    sbb.Append(" and ddpd.BATCH_NO = :t6 ");
                    //    sbb.Append(" and ddpd.BATCH_CODE = :t7 ");
                    //    sbb.Append(" and ddpd.TRADE_PRICE = :t8 ");
                    //    sbb.Append(" and ddpd.RETAIL_PRICE = :t9 ");
                    //    System.Collections.ArrayList list1 = new System.Collections.ArrayList();
                    //    List<string> paras1 = new List<string>();
                    //    paras1.Add("t1");
                    //    paras1.Add("t2");
                    //    paras1.Add("t3");
                    //    paras1.Add("t4");
                    //    paras1.Add("t5");
                    //    paras1.Add("t6");
                    //    paras1.Add("t7");
                    //    paras1.Add("t8");
                    //    paras1.Add("t9");
                    //    list1.Add(currentDispensary);
                    //    list1.Add(drugCode);
                    //    list1.Add(strPACKAGE_SPEC);
                    //    list1.Add(strPACKAGE_UNITS);
                    //    list1.Add(strFIRM_ID);
                    //    list1.Add(strBATCH_NO);
                    //    list1.Add(strBATCH_CODE);
                    //    list1.Add(strTRADE_PRICE);
                    //    list1.Add(strITEM_PRICE);
                    //    DataTable dataTableDOCT_DRUG_PRESC_DETAIL = new NM_Service.NMService.ServerPublicClient().GetDataTable_Para(sbb.ToString(), paras1, list1).Tables[0];

                    //    decimal decDRUGTEMP_QUANTITY = 0; //待发药数量
                    //    if (dataTableDOCT_DRUG_PRESC_DETAIL != null && dataTableDOCT_DRUG_PRESC_DETAIL.Rows.Count > 0)
                    //    {
                    //        decDRUGTEMP_QUANTITY = decimal.Parse(dataTableDOCT_DRUG_PRESC_DETAIL.Rows[0][0].ToString());
                    //    }
                    //    else
                    //    {
                    //        decDRUGTEMP_QUANTITY = 0;
                    //    }

                    //    strFILTER_TEMP += "\r\n批次BATCH_NO: " + strBATCH_NO + " 批号BATCH_CODE: " + strBATCH_CODE +
                    //        " 开单数量: " + decQUANTITY.ToString() + " 库存数量: " + decDRUGSTOCK_QUANTITY.ToString() + " 代发药数量: " + decDRUGTEMP_QUANTITY.ToString();

                    //    //医生开单数量  + 代发药数量 + 代收费数量 - 库存数量
                    //    if (decQUANTITY + decDRUGTEMP_QUANTITY - decDRUGSTOCK_QUANTITY <= 0)
                    //    {
                    //        dr["BATCH_NO"] = strBATCH_NO;
                    //        dr["BATCH_CODE"] = strBATCH_CODE;
                    //        dr["GUID"] = strGUID;
                    //        dr["TRADE_PRICE"] = strTRADE_PRICE;
                    //        dr["RETAIL_PRICE"] = strITEM_PRICE;
                    //        dr["COSTS"] = (decQUANTITY * Convert.ToDecimal(strITEM_PRICE)).ToString();
                    //        dr["PAYMENTS"] = (decQUANTITY * Convert.ToDecimal(strITEM_PRICE)).ToString();

                    //        boolDRUG = true;
                    //        break;
                    //    }
                    //}
                    //if (!boolDRUG)
                    //{
                    //    XtraMessageBox.Show("药品[" + drugName + "]库存不足!" + strFILTER_TEMP, "提示");
                    //    return logicalValue;
                    //}
                    #endregion

                }
                #endregion

                #region 合理用药
                if (!RationalAdministration.InpPrescSave(drPatientInfo, drMaster, ref dtDetail))
                {
                    return false;
                }
                //if (Parameter.RATIONAL_ADMINISTRATION == ERationalAdministrationInterface.ManufactorDaTong)
                if (Parameter.PASS_FIRM == "大通")
                {
                    if (!RationalAdministration.InpPrescSave(drPatientInfo, drMaster, dtDetail))
                    {
                        return false;
                    }
                }
                if (Parameter.PASS_FIRM == "慧药通")
                {
                    if (!RationalAdministration.InpPrescSave(drPatientInfo, drMaster, dtDetail, ref dtDoctDrugUpload))
                    {
                        return false;
                    }
                }
                if (Parameter.PASS_FIRM == "壹途")
                {
                    if (!RationalAdministration.InpPrescSave(drPatientInfo, drMaster, dtDetail))
                    {
                        return false;
                    }
                }

                #endregion

                #region 处方属性
                if (drMaster.RowState == DataRowState.Added)
                {
                    for (int i = 0; i < dtDetail.Rows.Count; i++)
                    {
                        string toxiProperty = ConvertHelper.ToString(dtDetail.Rows[i]["TOXI_PROPERTY"], "");
                        Boolean poisonous = CommonDrug.JudgeDrugToxicHempVsPsychotropicOne(toxiProperty);//是否是毒麻精神一类药品
                        if (poisonous)
                        {
                            drMaster["PRESC_ATTR"] = "毒麻精一";
                            break;
                        }
                        Boolean psychotropicII = CommonDrug.JudgeDrugPsychotropicTwo(toxiProperty);//精神二类药品   
                        if (psychotropicII)
                        {
                            drMaster["PRESC_ATTR"] = "精二处方";
                            break;
                        }
                    }
                }
                #endregion

                return true;
            }
            catch (Exception ex )
            { 
                throw new Exception("\r\nfrmPrescMain-CheckedData-Exception:" + ex.Message );
            }
            
        }
        #endregion
        #region 保存前费用重新计算
        /// <summary>
        /// 保存前费用重新计算 - 确保剂数信息正确应用到费用计算中
        /// </summary>
        private void RecalculateCostBeforeSave()
        {
            try
            {
                string logPath = @"..\Client\LOG\exLOG\住院处方费用重新计算_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                string logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + " [INFO] 开始保存前费用重新计算\r\n";
                System.IO.File.AppendAllText(logPath, logEntry);

                // 获取当前选中的处方主记录
                if (gvDrugMaster.FocusedRowHandle < 0) return;

                DataRow drMaster = gvDrugMaster.GetDataRow(gvDrugMaster.FocusedRowHandle);
                if (drMaster == null) return;

                string prescType = drMaster["PRESC_TYPE"].ToString();
                if (!prescType.Equals("1")) return; // 只处理草药处方

                int repetition = ConvertHelper.ToInt(drMaster["REPETITION"], 1);
                if (repetition <= 0) repetition = 1;

                logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + $" [INFO] 处方类型:{prescType}, 剂数:{repetition}\r\n";
                System.IO.File.AppendAllText(logPath, logEntry);

                // 重新计算所有明细的数量和费用
                for (int i = 0; i < dtDetail.Rows.Count; i++)
                {
                    DataRow drDetail = dtDetail.Rows[i];
                    if (drDetail.RowState == DataRowState.Deleted) continue;

                    decimal dosageEach = ConvertHelper.ToDecimal(drDetail["DOSAGE_EACH"], 0);
                    decimal dosePerUnit = ConvertHelper.ToDecimal(drDetail["DOSAGE"], 0);

                    if (dosageEach > 0 && dosePerUnit > 0)
                    {
                        // 重新计算数量（确保包含剂数）
                        decimal inputAmount = 0;
                        if (Parameter.HERBS_DIVIDE_DOSE_PER_UNIT == "1")
                        {
                            inputAmount = Math.Ceiling(Math.Round((dosageEach * repetition) / dosePerUnit, 2));
                        }
                        else
                        {
                            inputAmount = Math.Ceiling(Math.Round(dosageEach * repetition, 2));
                        }

                        drDetail["INPUT_AMOUNT"] = inputAmount;
                        drDetail["QUANTITY"] = inputAmount;

                        // 重新计算费用
                        DetailComputeItemMoney(drMaster, drDetail);

                        logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") +
                                  $" [INFO] 药品:{drDetail["DRUG_NAME"]}, 单次剂量:{dosageEach}, 重新计算数量:{inputAmount}, " +
                                  $"费用:{drDetail["COSTS"]}, 应收:{drDetail["PAYMENTS"]}\r\n";
                        System.IO.File.AppendAllText(logPath, logEntry);
                    }
                }

                logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + " [INFO] 保存前费用重新计算完成\r\n";
                System.IO.File.AppendAllText(logPath, logEntry);
            }
            catch (Exception ex)
            {
                try
                {
                    string logPath = @"..\Client\LOG\exLOG\住院处方费用重新计算_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                    string logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + $" [ERROR] 保存前费用重新计算异常: {ex.Message}\r\n";
                    System.IO.File.AppendAllText(logPath, logEntry);
                }
                catch
                {
                    // 日志记录失败不影响主流程
                }
            }
        }
        #endregion 保存前费用重新计算
        #region 保存
        /// <summary>
        /// 保存
        /// </summary>
        /// <returns>true--保存成功;false--保存失败</returns>
        private Boolean  PrescSave()
        {
            try
            {
                // 保存前重新计算费用，确保剂数信息正确
                RecalculateCostBeforeSave();

                DataRow drMaster = null;
                if (!CheckedData(ref drMaster))
                {
                    return false;
                }
                DateTime systime = CommonDataBase.GetSysDateTime();//系统时间 0
                string orderNoList = "";
                DataTable dtOrdersCosts = new DataTable();
                DataTable dtOrders = new DataTable();
                string orderClass = "";
                string prescType = drMaster["PRESC_TYPE"].ToString();//处方类型0--西药；1草药 
                if (prescType.Equals("0"))
                {
                    orderClass = "A";
                }
                else
                {
                    orderClass = "B";
                }
                if (dtDetail.Rows.Count > 0)
                {
                    int repeatIndicator = ConvertHelper.ToInt(rdoRepeatIndicator.EditValue, 0);
                    if (repeatIndicator == 1 || repeatIndicator == 0)
                    {
                        CommonPresc.SetPrescToOrders(repeatIndicator, ref dtOrders, ref dtOrdersCosts, drMaster, dtDetail);
                    }
                }
                #region 备用新增删除数据
                DataTable dtSpareOrdersAdd = dtOrders.Clone();
                DataTable dtSpareOrdersDel = dtOrders.Clone();
                DataTable dtSparePrescMaster = dtMaster.Clone();
                DataTable dtSparePrescDetailDel = CommonDataTableMethod.GetFindConditionDT(dtDetailDel, "");
                dtSparePrescDetailDel.TableName = dtDetailDel.TableName;
                DataTable dtSparePrescDetailAdd = CommonDataTableMethod.GetFindConditionDT(dtDetail, "");
                dtSparePrescDetailAdd.TableName = dtDetail.TableName;
                DataRow drSpareMaster = dtSparePrescMaster.NewRow();
                drSpareMaster.ItemArray = drMaster.ItemArray;
                dtSparePrescMaster.Rows.Add(drSpareMaster);
                if (dtSparePrescDetailAdd.Rows.Count > 0)
                {
                    //新增医嘱
                    dtSpareOrdersAdd = CommonDataTableMethod.GetFindConditionDT(dtOrders, "CURRENT_PRESC_NO=" + ConvertHelper.ToString(drMaster["PRESC_NO"]));
                    dtSpareOrdersAdd.TableName = dtOrders.TableName;
                }
                else
                {
                    dtSpareOrdersDel = CommonDataTableMethod.GetFindConditionDT(dtOrders, "CURRENT_PRESC_NO=" + ConvertHelper.ToString(drMaster["PRESC_NO"]));
                    dtSpareOrdersDel.TableName = dtOrders.TableName;
                }
                #endregion
                #region 处理删除数据 
                if (dtDetailDel.Rows.Count > 0)
                {
                    DataRow drMasterDel = dtMaster.NewRow();
                    drMasterDel.ItemArray = drMaster.ItemArray;
                    #region 无明细数据按作废处理
                    if (dtDetail.Select().Length < 1)
                    {
                        if (drMaster.RowState != DataRowState.Added)
                        {
                            drMaster.Delete();
                        }
                        else
                        {
                            dtMaster.Rows.Remove(drMaster);
                        }
                    }
                    #endregion
                    if (rdoRepeatIndicator.SelectedIndex == 0 || rdoRepeatIndicator.SelectedIndex == 1)
                    {
                        CommonPresc.GetPrescToOrderNoList(dtDetailDel, ref orderNoList);
                        if (!CommonOrders.OrderCancelApply(CurrentPatientId, CurrentVisitId, orderClass, orderNoList, ref dtOrders, false, systime))
                        {
                            return false;
                        }
                    }
                }
                #endregion
                //医疗质量
                if (!Tjhis.Doctor.Station.Interface.MedicalQuality.Call.Business.PrescSave(CurrentPatientId, CurrentVisitId, drPatientInfo, drMaster, dtDetail))
                {
                    return false;
                }
                Boolean SaveState = CommonDataBase.UpdateDataTable("处方", false, dtMaster, dtDetail, dtOrders, dtOrdersCosts, dtDoctDrugUpload);
                if (SaveState)
                {
                    //事前提前
                    Tjhis.Doctor.Station.Interface.PreWarning.Call.Business.PrescSave(CurrentPatientId, CurrentVisitId, drPatientInfo, drMaster, dtDetail);

                    if (Parameter.PLATFORM_FLAG.Equals("1"))
                    {
                        #region 统一接口 2022-04-25
                        string strSQL = " SELECT * FROM DOCT_DRUG_PRESC_MASTER " +
                        " WHERE PRESC_DATE = TO_DATE('" + drMaster["PRESC_DATE"].ToString() + "','YYYY-MM-DD HH24:MI:SS') " +
                        " AND PRESC_NO = '" + drMaster["PRESC_NO"].ToString() + "'";
                        DataTable dataTableTEMP = new NM_Service.NMService.ServerPublicClient().GetDataBySql(strSQL).Tables[0];
                        DataTable[] dataTables = new DataTable[] { dataTableTEMP };
                        string strERROR_TEXT = string.Empty;
                        Tjhis.Interface.Station.Interface_Common.InvokeInterface("Doctws_001", "UPDATE", "DOCTWS", drMaster["ORDERED_BY"].ToString(), dataTables, ref strERROR_TEXT);
                        #endregion
                    }

                    #region CA 签名
                    //if (Parameter.CA_DATASIGN_ENABLED == "1" && PlatCommon.SysBase.SystemParm.LoginUser.CA_ENABLED == "1")
                    if (PlatCommon.SysBase.SystemParm.LoginUser.CA_ENABLED == "1")
                    {
                        //ICA.UkeySignPresc(CurrentPatientId, CurrentVisitId, dtSparePrescMaster, dtSparePrescDetailAdd, "提交");
                        //ICA.UkeySignPresc(CurrentPatientId, CurrentVisitId, dtSparePrescMaster, dtSparePrescDetailDel, "作废");
                        //ICA.UkeySignOrders(CurrentPatientId, CurrentVisitId, dtSpareOrdersAdd, "提交");
                        //ICA.UkeySignOrders(CurrentPatientId, CurrentVisitId, dtSpareOrdersDel, "作废");
                        PlatCommon.SysBase.SystemParm.CaBusiness.CASignData(PlatCommon.SysBase.SystemParm.HisUnitCode
                           , "DOCTWS", this.drPatientInfo["DEPT_CODE"].ToString(), Tjhis.Interface.CA.CaBusiness.CaSignDataType.InpDrugPresc,
                           PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME, "住院处方提交", dtSparePrescMaster.Select("","", DataViewRowState.Added).CopyToDataTable());

                        PlatCommon.SysBase.SystemParm.CaBusiness.CASignData(PlatCommon.SysBase.SystemParm.HisUnitCode
                            , "DOCTWS", this.drPatientInfo["DEPT_CODE"].ToString(), Tjhis.Interface.CA.CaBusiness.CaSignDataType.OrderSave,
                            PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME, "遗嘱提交", dtSpareOrdersAdd);
                    }
                    #endregion

                    #region 决策支持
                    if (IDecisionSupport.EnableFlag)
                    {
                        DataTable dtMergeOrders = CommonDataTableMethod.MergeDataTable(dtSpareOrdersAdd, dtSpareOrdersDel);
                        DataTable dtMergePrescDetail = CommonDataTableMethod.MergeDataTable(dtSparePrescDetailAdd, dtSparePrescDetailDel);
                        IDecisionSupport.QualityOrdersRecord(CurrentPatientId, CurrentVisitId, dtMergeOrders,
                                                            dtSparePrescMaster, dtMergePrescDetail
                                                             );
                    }
                    #endregion
                    #region 事前事中
                    string error="";
                    TjhisInterfaceInsurance.MedicalInsur3101_02_03.SendInpBusinessHandle(CurrentPatientId, CurrentVisitId.ToString(), "3101",ref error);
                    TjhisInterfaceInsurance.MedicalInsur3101_02_03.SendInpBusinessHandle(CurrentPatientId, CurrentVisitId.ToString(), "3102", ref error);
                    #endregion

                    XtraMessageHelper.Info(Consts.MEDICAL_CARE_COMMON, "001", true, new string[] { "处方" });
                    //XtraMessageHelper.BubbleTipMessageList(this, Consts.MEDICAL_CARE_COMMON,"001", new string[] { "处方" });
                    //新增处方是否自动打印
                    if (drMaster.RowState == DataRowState.Added && Parameter.PRESC_PRINT_NEW.Equals("1"))//新增
                    {
                        PrescPrint(ConvertHelper.ToInt(prescType), ConvertHelper.ToDateTime(drMaster["PRESC_DATE"].ToString()), ConvertHelper.ToInt(drMaster["PRESC_NO"].ToString()));
                    }//修改过的处方是否自动打印
                    else if (drMaster.RowState == DataRowState.Modified && Parameter.PRESC_PRINT_MODIFIED.Equals("1"))//编辑
                    {
                        PrescPrint(ConvertHelper.ToInt(prescType), ConvertHelper.ToDateTime(drMaster["PRESC_DATE"].ToString()), ConvertHelper.ToInt(drMaster["PRESC_NO"].ToString()));
                    }
                    dtDetail.AcceptChanges();
                    dtMaster.AcceptChanges();
                    LoadData(); //刷新数据   
                    CommonOrders.RefreshOrdersWinData();
                }
                return SaveState;
            }
            catch (Exception ex )
            {
                throw new Exception("\r\nfrmPrescMain-PrescSave-Exception:" + ex.Message);
            } 
        }
        #endregion 
        #region 设置药品的毒理属性
        /// <summary>
        /// 设置药品的毒理属性
        /// </summary>
        private void SetDrugToxiProperty()
        {
            DataRow[] drDetail = dtDetail.Select();
            DataTable dt = null;
            foreach (DataRow dr in drDetail)
            {
                string state = dr.RowState.ToString();
                string drugCode = dr["DRUG_CODE"].ToString();
                string drugName = dr["DRUG_NAME"].ToString();
                if (CommonDrug.GetDrugInfo(drugCode, drugName,"", ref dt) == -1) //停价则返回
                {
                    return;
                }               
                dr["TOXI_PROPERTY"] = dt.Rows[0]["TOXI_PROPERTY"];//毒理分类
                dr["ANTIBACTERIAL_FLAG"] = dt.Rows[0]["ANTIBACTERIAL_FLAG"];//抗生素标志
                if (state == "Unchanged")
                {
                    dr.AcceptChanges();
                }
            }
        }
        #endregion
        #region 设置药局
        /// <summary>
        /// 设置药局
        /// </summary>
        /// <param name="prescType">处方类型</param>
        /// <param name="storageCode">药房代码</param>
        /// <param name="drMaster">处方主记录</param>
        private void SetDispensary(string prescType, string storageCode,DataRow drMaster)
        {
            string STORAGE_TYPE = PlatCommon.SysBase.SystemParm.GetParameterValue("CONFIG_SEL_STORAGE_TYPE", "DOCTWS", SystemParm.LoginUser.DEPT_CODE, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode); //药房属性 内容 药房或者药局
            string findStr = "";
            if (!string.IsNullOrEmpty(STORAGE_TYPE))
            {
                findStr = " STORAGE_TYPE in ('" + STORAGE_TYPE + "')";
            }
            //(string.IsNullOrEmpty(Parameter.DispensingWesternPharmacy) && string.IsNullOrEmpty(Parameter.DispensingHerbHouse))
            string DispensingWesternPharmacy = Parameter.DispensingWesternPharmacy; // PlatCommon.SysBase.SystemParm.GetParaValue("PRESC_DRUG_DISPS", "DOCTWS", "*", "*", "");//发药西药房 PRESC_DRUG_DISPS
            string DispensingHerbHouse = Parameter.DispensingHerbHouse;//PlatCommon.SysBase.SystemParm.GetParaValue("PRESC_CDRUG_DISPS", "DOCTWS", "*", "*", ""); //发药草药房 PRESC_CDRUG_DISPS
            string DispensingAorB = "";
            if (prescType.Equals("0"))
            {
                DispensingAorB = DispensingWesternPharmacy;
            }
            else
            {
                DispensingAorB = DispensingHerbHouse;
            }
            dtStorage = BasicDict.GetDispensaryList(prescType, storageCode, findStr, DispensingAorB);
            lookupDispensary.Properties.EditValueChanged -= new
            System.EventHandler(lookupDispensary_EditValueChanged);
            if (dtStorage.Rows.Count > 0)
            {
                string state = drMaster.RowState.ToString();
                currentDispensary = dtStorage.Rows[0]["STORAGE_CODE"].ToString();
                currentDispensary = CommonPresc.GetStorageDefault(dtStorage, currentDispensary, prescType);
                CommonDevMethod.BindLookUpDispensaryList(lookupDispensary, dtStorage, storageCode);
                lookupDispensary.EditValue = currentDispensary;
                if(drMaster["DISPENSARY"].ToString()!= currentDispensary)
                {
                    drMaster["DISPENSARY"] = currentDispensary;
                }
                if (state == "Unchanged")
                {
                    drMaster.AcceptChanges();
                }
            }
            lookupDispensary.Properties.EditValueChanged += new
            System.EventHandler(lookupDispensary_EditValueChanged);
        }
        #endregion 
        #region 加载输入法
        /// <summary>
        /// 加载输入法
        /// </summary>
        private void LoadingInputMethod()
        {
            #region 药品名称 
            string DispensingDrug = Parameter.DispensingWesternPharmacy;
            if(string.IsNullOrEmpty(DispensingDrug))
            {
                if (Parameter.DispensingHerbHouse.Length > 0)
                {
                    DispensingDrug = Parameter.DispensingHerbHouse;
                } 
            }
            else
            {
                if(Parameter.DispensingHerbHouse.Length>0)
                {
                    DispensingDrug = DispensingDrug + "," + Parameter.DispensingHerbHouse;
                } 
            } 
            ucDrugName.DictType = "住院诊疗药品项目";
            ucDrugName.InputCode = Parameter.INPUT;
            ucDrugName.FilterSql = "STORAGE IN ( " + DispensingDrug + ")";
            ucDrugName.FilterSql = ucDrugName.FilterSql + " AND (HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode  + "')";
            
            ucDrugName.InitGcInputColumnAndData();
            ucDrugName.deInputResult += ItemNameInputResult;
            #endregion 
            #region 频次
            ucFreq.DictType = "住院频次字典";
            ucFreq.InputCode = Parameter.INPUT;
            ucFreq.FilterSql = "";
            ucFreq.InitGcInputColumnAndData();
            ucFreq.deInputResult += FrequencyInputResult;
            #endregion  
            #region 途径
            ucAdministration.DictType = "住院途径字典";
            ucAdministration.InputCode = Parameter.INPUT;
            ucAdministration.FilterSql = "";
            ucAdministration.InitGcInputColumnAndData();
            ucAdministration.deInputResult += AdministrationInputResult;
            #endregion 
            #region 诊断
            ucDiagnosis.DictType = "诊断字典";
            ucDiagnosis.InputCode = Parameter.INPUT;
            ucDiagnosis.FilterSql = "";
            ucDiagnosis.InitGcInputColumnAndData();
            ucDiagnosis.deInputResult += DiagnosisInputResult;
            #endregion  
        }
        #endregion
        #region 获取 输入法的返回值 
        #region 药品名称输入法的返回值
        /// <summary>
        /// 药品名称输入法的返回值
        /// </summary>
        /// <param name="e"></param>
        private void ItemNameInputResult(InputResult e)
        {
            if (string.IsNullOrEmpty(e.ItemCode))
            {
                return;
            }

            DataTable dtDrugInfo = null;
            if (CommonDrug.GetDrugInfo(e.ItemCode, e.ItemName, e.ItemSpec, ref dtDrugInfo) == -1) //停价则返回
            {
                return;
            }
            DataRow drMaster = null;
            string prescType = "";
            if (!CheckUpMasterSelect("输入药品信息", ref prescType, ref drMaster))
            {
                return;
            }

            DataRow drDrugInfo = dtDrugInfo.Rows[0];
            decimal dosageEach = 0;//单次用量       
            decimal maxPrescDosage = 0;//单处方最大开药量
            decimal maxDosage = 0;//单次最大用量  
            string minSpec = drDrugInfo["MIN_SPEC"].ToString();//最小单位规格
            string minUnits = drDrugInfo["MIN_UNITS"].ToString();//最小规格单位
            string drugSpec = drDrugInfo["DRUG_SPEC"].ToString();//规格
            string firmId = drDrugInfo["FIRM_ID"].ToString();//厂家
            string units = drDrugInfo["UNITS"].ToString();//单位
            string toxiProperty = drDrugInfo["TOXI_PROPERTY"].ToString();//毒理分类
            int antibacterialFlag = ConvertHelper.ToInt(drDrugInfo["ANTIBACTERIAL_FLAG"], 0);
            string drugAuthoritySerialNo = "";
            string drugPurpose = "";
            #region 判断毒理是否相同 毒理药品限制的问题
            if (!CommonDrug.ToxiPropertySame(toxiProperty, e.ItemName, dtDetail))//判断毒理是否相同
            { 
                return;
            }
            #endregion
            int amountPerPackage = ConvertHelper.ToInt(drDrugInfo["AMOUNT_PER_PACKAGE"].ToString(),1);//包装量
            decimal dosePerUnit = ConvertHelper.ToDecimal(drDrugInfo["DOSE_PER_UNIT"].ToString(),1);  //最小单位剂量 
            #region 分线用药判断
            int limitDrugRet = CommonDrug.JudgeLimitDrug(dtDrugInfo, CurrentPatientId, CurrentVisitId, drPatientInfo["DEPT_CODE"].ToString());
            if (limitDrugRet == -1)
            {
                return;
            }
            //抗菌药物
            if (antibacterialFlag == 1 && limitDrugRet != 1 && !CommonDrug.GetAntibacterialsApply(drPatientInfo, e.ItemCode, e.ItemName, limitDrugRet, ref drugAuthoritySerialNo, ref drugPurpose))
            {
                return;
            }
            #endregion  

            if (prescType.Equals("1"))
            {
                DataRow[] rowArray = dtDetail.AsEnumerable().Where(r => r.GetType().Name == "DataRow" && r["DRUG_NAME"].ToString() == e.ItemName).ToArray();
                if (rowArray.Length >= 1)
                {
                    XtraMessageBox.Show("药品[" + e.ItemName + "]在当前草药方中已存在，请勿重复开写!" , "温馨提示");
                    return;
                }
            }
                

            //公费用药            
            int writeoff = CommonDrug.JudgePublicDrug(drPatientInfo["CHARGE_TYPE"].ToString(), e.ItemClass, e.ItemCode, e.ItemName, e.ItemSpec, "inp");
            //防止触发事件
            gvDrugDetail.CellValueChanged -= new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(gvDrugDetail_CellValueChanged);
            #region 赋值开始 
            DataRow drRD = BasicDict.GetDrugRationalDosageRow(e.ItemCode, minSpec); 
            if (drRD != null)
            {   
                dosageEach = ConvertHelper.ToDecimal(drRD["NORMAL_DOSAGE"].ToString(), 0);
                if (dosageEach == 0)
                {
                    dosageEach = dosePerUnit;
                }
                maxPrescDosage = ConvertHelper.ToDecimal(drRD["MAX_PRESC_DOSAGE"].ToString(), 0);//单处方最大开药量
                maxDosage = ConvertHelper.ToDecimal(drRD["MAX_DOSAGE"].ToString(), 0);//单次最大用量   
                gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "ADMINISTRATION", drRD["ADMINISTRATION"].ToString());//途径
                gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "FREQUENCY", drRD["FREQUENCY"].ToString()); //频次
            }
            else
            {
                dosageEach = dosePerUnit;
            }
            int inputAmount = ConvertHelper.ToInt(gvDrugDetail.GetRowCellValue(gvDrugDetail.FocusedRowHandle, INPUT_AMOUNT), 0);
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "DRUG_NAME", e.ItemName);//药品名称 
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "DRUG_CODE", e.ItemCode);//药品代码
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "DRUG_SPEC", minSpec);//最小单位规格
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "DOSAGE_UNITS", e.ItemUnit);//最小单位规格
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "UNITS", minUnits);//最小规格单位（本次新增，6.6版本中未存储） 
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "FIRM_ID", firmId);//厂家
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "PACKAGE_SPEC", drugSpec);//规格
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "PACKAGE_UNITS", units);//单位
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "INPUT_UNITS", units);//输入数量单位 
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "QUANTITY", 0);
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "INPUT_AMOUNT", 0); 
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "DETAIL_COSTS", 0);
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "DETAIL_PAYMENTS", 0);
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "AMOUNT_PER_PACKAGE", amountPerPackage);
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "DOSAGE", dosePerUnit); //最小单位剂量   
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "TOXI_PROPERTY", toxiProperty); //毒理分类 
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "MAX_PRESC_DOSAGE", maxPrescDosage); //单处方最大开药量
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "MAX_DOSAGE", maxDosage); //单次最大用量
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "WRITEOFF", writeoff);
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "ANTIBACTERIAL_FLAG", antibacterialFlag);
            if (!string.IsNullOrEmpty(drugAuthoritySerialNo))
            {
                gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "DRUG_AUTHORITY_SERIAL_NO", drugAuthoritySerialNo);
            }
            if (!string.IsNullOrEmpty(drugPurpose))
            {
                gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "DRUG_PURPOSE", drugPurpose);
            }
            //防止触发事件
            gvDrugDetail.CellValueChanged += new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(gvDrugDetail_CellValueChanged);

            if (prescType.Equals("0"))
            {
                gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "DOSAGE_EACH", dosageEach); //单次剂量 
            }
            else
            {
                gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "DOSAGE_EACH", 0); //单次剂量                 
            }

            if (inputAmount > 0)
            {
                gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "INPUT_AMOUNT", inputAmount);
            }
            #endregion

            
            if (prescType.Equals("1"))
            {
                layoutView1.FocusedColumn = DOSAGE_EACH1;
                layoutView1.ShowEditor();
            }
            else
            {
                gvDrugDetail.FocusedColumn = DOSAGE_EACH;//切换焦点到单次用量 
                gvDrugDetail.ShowEditor();//打开行编辑  
            }            

            rdoPrescType.Enabled = false;
            lookupDispensary.Enabled = false;
            chkdischargeTakingIndicator.Enabled = false;
        }
        #endregion 
        #region 途径输入法的返回值
        /// <summary>
        /// 途径输入法的返回值
        /// </summary>
        /// <param name="e"></param>
        private void AdministrationInputResult(InputResult e)
        {
            if (string.IsNullOrEmpty(e.ItemCode))
            {
                return;
            }
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "ADMINISTRATION", e.ItemName); 
        }
        #endregion 
        #region 频次 输入法的返回值
        /// <summary>
        /// 频次 输入法的返回值
        /// </summary>
        /// <param name="e"></param>
        private void FrequencyInputResult(InputResult e)
        {
            if (string.IsNullOrEmpty(e.ItemName))
            {
                return;
            }
            gvDrugDetail.SetRowCellValue(gvDrugDetail.FocusedRowHandle, "FREQUENCY", e.ItemName);
            if (ConvertHelper.ToInt(gvDrugDetail.GetRowCellValue(gvDrugDetail.FocusedRowHandle,ANTIBACTERIAL_FLAG),0)==1)
            {
                gvDrugDetail.FocusedColumn = DRUG_PURPOSE;//抗菌目的
            }
            else
            {
                gvDrugDetail.FocusedColumn = FREQ_DETAIL;//切换焦点到单次用量 
            } 
            gvDrugDetail.ShowEditor();//打开行编辑  
            DataRow  drMaster = dtMaster.Rows[gvDrugMaster.FocusedRowHandle];
            if (RationalAdministration.PrescInpInput(drPatientInfo, drMaster, dtDetailDel) == -1)//修改数据
            {
                
            }
        }
        #endregion 
        #region 诊断输入法返回值
        /// <summary>
        /// 诊断输入法返回值
        /// </summary>
        /// <param name="e"></param>
        private void DiagnosisInputResult(InputResult e)
        {
            if (string.IsNullOrEmpty(e.ItemCode))
            {
                return;
            }
            if (string.IsNullOrEmpty(txtDiagnosisName.Text))
            {
                txtDiagnosisName.Text = e.ItemName;
            }
            else
            {
                txtDiagnosisName.Text = txtDiagnosisName.Text + ";" + e.ItemName;
            }
        }
        #endregion

        #region 药品名称输入法刷新数据源
        /// <summary>
        /// 药品名称输入法刷新数据源
        /// </summary>
        private void InputDrugName()
        {
            string filter = "";
            //首先按照中药，西药的类型过滤输入法 //rdoDrug，rdoCdrug  
            //然后如果药局没选，输入法过滤所有配置的药局药品
            //如果选择了药局，按照药局过滤输入法
            if (rdoPrescType.EditValue.Equals("0")) //西药
            { 
                filter = "DRUG_INDICATOR = 'A'";
                if (!string.IsNullOrEmpty(lookupDispensary.EditValue.ToString()))
                {

                    string storage = lookupDispensary.EditValue.ToString();
                    filter += " and STORAGE='" + storage + "'";
                }
                else
                {
                    filter += " and STORAGE IN (" + Parameter.DispensingWesternPharmacy + ")";
                } 
            }
            else if (rdoPrescType.EditValue.Equals("1")) //中药
            {
                filter = "DRUG_INDICATOR = 'B'";
                if (!string.IsNullOrEmpty(lookupDispensary.EditValue.ToString()))
                {
                    string storage = lookupDispensary.EditValue.ToString();
                    filter += " and STORAGE='" + storage + "'";
                }
                else
                {
                    filter += " and STORAGE IN (" + Parameter.DispensingHerbHouse + ")";
                }
            } 
            ucDrugName.RefreshGridData(filter);
        }
        #endregion 
        #region  协定处方调用
        /// <summary>
        /// 协定处方调用
        /// </summary>
        private void PrescGroupCall()
        {
            if (string.IsNullOrEmpty(currentDispensary))
            {
                //016--请先选择药局!
                XtraMessageHelper.Info(Consts.PRESC,"016"); 
                lookupDispensary.Focus();
                return;
            }
            DataRow drMaster = null;
            string prescType = "";
            if (!CheckUpMasterSelect("协定处方调用", ref prescType, ref drMaster))
            {
                return;
            }
            DataTable dtTemple = new DataTable();
            frmPrescModelCall frm = new frmPrescModelCall(SystemParm.LoginUser.USER_NAME, SystemParm.LoginUser.DEPT_CODE, ConvertHelper.ToInt(prescType, 0), currentDispensary, drPatientInfo, ConvertHelper.ToInt(speRepetition.Text, 1));
            if (frm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            dtTemple = frm.dtRetItems;
            if(dtTemple.Rows.Count<1)
            {
                return;
            }
            DataRow drTemple = dtTemple.Rows[0];  
            string itemClass = "";
            if (prescType.Equals("1"))//草药处方
            {
                string usage = "";//草药用法
                string topic = drTemple["TOPIC"].ToString();//处方名称
                usage = drTemple["USAGE"].ToString();//草药用法
                if (!string.IsNullOrEmpty(usage))
                {
                    drMaster["USAGE"] = usage;//草药用法
                    lookUpEdit_Usage.EditValue = usage;
                }
                if (!string.IsNullOrEmpty(topic))
                {
                    drMaster["BINDING_PRESC_TITLE"] = topic;//草药处方名称
                    txtBindingPrescTitle.Text = topic;
                }
                itemClass = "B";//草药
            }
            else if (prescType.Equals("0"))//西药处方
            {
                itemClass = "A";//西药
            }
            for (int i = 0; i < dtTemple.Rows.Count; i++)
            {
                drTemple = dtTemple.Rows[i]; 
                string drugCode = drTemple["DRUG_CODE"].ToString();
                string drugName = drTemple["DRUG_NAME"].ToString();
                string specFirm = drTemple["PACKAGE_SPEC"].ToString() + drTemple["FIRM_ID"].ToString();
                #region 判断毒理是否相同 毒理药品限制的问题
                if (!CommonDrug.ToxiPropertySame(drTemple["TOXI_PROPERTY"].ToString(), drugName, dtDetail))//判断毒理是否相同
                { 
                    return;
                }
                #endregion
                int writeoff = CommonDrug.JudgePublicDrug(drPatientInfo["CHARGE_TYPE"].ToString(), itemClass, drugCode, drugName, specFirm, "inp");
                DataRow drDetail = dtDetail.NewRow();  //新增行
                drDetail["AMOUNT_PER_PACKAGE"] = drTemple["AMOUNT_PER_PACKAGE"];//包装量
                drDetail["DRUG_SPEC"] = drTemple["MIN_SPEC"];//最小单位规格 
                drDetail["UNITS"] = drTemple["MIN_UNITS"];//最小规格单位（本次新增，6.6版本中未存储）  
                drDetail["DOSAGE"] = dtTemple.Rows[i]["DOSE_PER_UNIT"];//最小单位剂量
                drDetail["DRUG_CODE"] = drTemple["DRUG_CODE"];//药品代码
                drDetail["DRUG_NAME"] = drTemple["DRUG_NAME"];//药品名称
                drDetail["PACKAGE_SPEC"] = drTemple["PACKAGE_SPEC"];//规格
                drDetail["PACKAGE_UNITS"] = drTemple["PACKAGE_UNITS"];//单位
                drDetail["FIRM_ID"] = drTemple["FIRM_ID"]; //厂家
                drDetail["DOSAGE_EACH"] = drTemple["DOSAGE"]; //单次用量  
                drDetail["DOSAGE_UNITS"] = drTemple["DOSAGE_UNITS"];//剂量单位
                drDetail["ADMINISTRATION"] = drTemple["ADMINISTRATION"];//途径
                drDetail["FREQUENCY"] = drTemple["FREQUENCY"];//执行频率描述
                drDetail["INPUT_AMOUNT"] = drTemple["QUANTITY"];
                drDetail["INPUT_UNITS"] = dtTemple.Rows[i]["UNITS"];//输入数量单位 
                drDetail["QUANTITY"] = itemClass.Equals("A")? drTemple["QUANTITY"] : drTemple["DOSAGE"];
                drDetail["WRITEOFF"] = writeoff;//公费报销
                drDetail["ORDER_SUB_NO"] = drTemple["ORDER_SUB_NO"];//医嘱子序号       
                drDetail["MAX_PRESC_DOSAGE"] = drTemple["MAX_PRESC_DOSAGE"];//单处方最大开药量
                drDetail["TOXI_PROPERTY"] = drTemple["TOXI_PROPERTY"];//毒理分类
                drDetail["MAX_DOSAGE"] = drTemple["MAX_DOSAGE"];//单次最大用量  
                drDetail["FREQ_DETAIL"] = drTemple["FREQ_DETAIL"];//医生说明
                drDetail["PRESC_ID"] = drTemple["MODEL_ID"];//协定处方号 
                drDetail["ANTIBACTERIAL_FLAG"] = drTemple["ANTIBACTERIAL_FLAG"];
                drDetail["DRUG_AUTHORITY_SERIAL_NO"] = drTemple["DRUG_AUTHORITY_SERIAL_NO_TEMP"];//越权开药序号(ANTIBACTERIALS_APPLY_REC.SERIAL_NO)
                if (!string.IsNullOrEmpty(ConvertHelper.ToString(drTemple["DRUG_PURPOSE_TEMP"])))
                {
                    drDetail["DRUG_PURPOSE"] = drTemple["DRUG_PURPOSE_TEMP"];
                }
                dtDetail.Rows.Add(drDetail);
                if (rdoPrescType.EditValue.Equals("1"))
                {
                    DataRow drCDetail = dtCDetail.NewRow();
                    DataRow dr = dtCDetail.NewRow();
                    drCDetail["DRUG_NAME1"] = drDetail["DRUG_NAME"];
                    drCDetail["DOSAGE_EACH1"] = drDetail["DOSAGE_EACH"];
                    drCDetail["FREQ_DETAIL1"] = drDetail["FREQ_DETAIL"];
                    dtCDetail.Rows.Add(drCDetail);

                }
                DetailComputeItemMoney(drMaster, drDetail);//明细记录计算项目金额
            }
            SetDrugGroup();//设置药品组号标志
            rdoPrescType.Enabled = false;
            lookupDispensary.Enabled = false;
            chkdischargeTakingIndicator.Enabled = false;
        }
        #endregion
        #endregion 
        #region 到最后一行，可编辑的列的时候，再按回车的时候，直接新增一行 
        /// <summary>
        /// 到最后一行
        /// </summary>
        /// <param name="view">GridView</param>
        /// <param name="focusedColumn">定位字段列</param>
        /// <returns></returns>
        private static bool IsLastEditableColumn(GridView view, GridColumn focusedColumn)
        {
            int idx = view.VisibleColumns.IndexOf(focusedColumn);
            for (int i = view.VisibleColumns.Count - 1; i >= 0; i--)
            {
                GridColumn col = view.VisibleColumns[i];
                //忽略不可编辑Column 
                if (!col.OptionsColumn.AllowEdit) continue;
                if (idx != i)
                    return false; //非最后一个可编辑Column 
                else
                    return true;
            }
            return true;
         }
        public void OnDetailGridViewKeyDown(object sender, KeyEventArgs e)
        {
            if (!(e.KeyCode == Keys.Enter || e.KeyCode == Keys.Tab)) return; 
            GridView view = (GridView)sender; 
            //最后一条记录 
            if ((view.FocusedRowHandle == view.RowCount - 1) && IsLastEditableColumn(view, view.FocusedColumn))
            {
                e.Handled = true;
                //下面代码: 最后可编辑栏位,则新增记录. 
                DetailAdd();
            }
            if (view.FocusedColumn == FREQ_DETAIL)
            {
                gvDrugDetail.FocusedColumn = INPUT_AMOUNT;//切换焦点到数量
                gvDrugDetail.ShowEditor();//打开行编辑             
            }
            else if (view.FocusedColumn == DOSAGE_EACH)
            {
                gvDrugDetail.FocusedColumn = ADMINISTRATION;
            }
            else if (view.FocusedColumn == DRUG_PURPOSE)
            {
                gvDrugDetail.FocusedColumn = FREQ_DETAIL;
            }
        }
        #endregion
        #region dgvDetail窗口变化事件引用
        /// <summary>
        /// GridCellValueChanging
        /// </summary>
        /// <param name="columnName">列名称</param>
        private void GridCellValueChanging(string columnName)
        { 
            DataRow drMaster = gvDrugMaster.GetDataRow(gvDrugMaster.FocusedRowHandle);
            DataRow drDetail = gvDrugDetail.GetDataRow(gvDrugDetail.FocusedRowHandle);
            string drugCode = drDetail["DRUG_CODE"].ToString();
            string administration = drDetail["ADMINISTRATION"].ToString();
            string frequency = drDetail["FREQUENCY"].ToString();
            string drugName = drDetail["DRUG_NAME"].ToString();
            string packageSpec = drDetail["PACKAGE_SPEC"].ToString();
            string packageUnits = drDetail["PACKAGE_UNITS"].ToString();
            string minSpec = drDetail["DRUG_SPEC"].ToString();//最小规格
            string firmId = drDetail["FIRM_ID"].ToString();
            string inpuUnits = drDetail["INPUT_UNITS"].ToString();
            string drugSpecFrimId = packageSpec + firmId;//厂家规格
            string tempVaule = "";
            decimal dosePerUnit = 1;
            decimal inputAmount = 0;//输入数量  
            decimal maxDosage = 0;//单次最大用量 
            int amountPerPackage = 1;//包装量  
            tempVaule = gvDrugDetail.GetRowCellValue(gvDrugDetail.FocusedRowHandle, "DOSAGE").ToString();
            if (string.IsNullOrEmpty(tempVaule) && !string.IsNullOrEmpty(drugCode))//最小单位剂量为空，用于调整处方
            {
                DataTable dtDrugInto = null;
                if (CommonDrug.GetDrugInfo(drugCode, drugName, drugSpecFrimId, ref dtDrugInto)==-1) //停价则返回
                {
                    return;
                }
                dosePerUnit = ConvertHelper.ToDecimal(dtDrugInto.Rows[0]["DOSE_PER_UNIT"].ToString());  //最小单位剂量
                amountPerPackage = ConvertHelper.ToInt(dtDrugInto.Rows[0]["AMOUNT_PER_PACKAGE"].ToString());//包装量
                drDetail["TOXI_PROPERTY"] = dtDrugInto.Rows[0]["TOXI_PROPERTY"].ToString();//毒理分类
                drDetail["DOSAGE"] = dosePerUnit;//最小单位剂量
                drDetail["AMOUNT_PER_PACKAGE"] = amountPerPackage;//包装量
                DataRow drRD = BasicDict.GetDrugRationalDosageRow(drugCode, minSpec); 
                if (drRD!=null)
                {    
                    drDetail["MAX_PRESC_DOSAGE"] = ConvertHelper.ToDecimal(drRD["MAX_PRESC_DOSAGE"].ToString(),0);//单处方最大开药量
                    drDetail["MAX_DOSAGE"] = ConvertHelper.ToDecimal(drRD["MAX_DOSAGE"].ToString(),0);//单次最大用量
                    drDetail["DOSAGE"] = ConvertHelper.ToDecimal(drRD["NORMAL_DOSAGE"].ToString(),0); 
                }
            }
            else
            {
                dosePerUnit = ConvertHelper.ToDecimal(tempVaule);
            }
            decimal dosageEach  = ConvertHelper.ToDecimal(gvDrugDetail.GetRowCellValue(gvDrugDetail.FocusedRowHandle, "DOSAGE_EACH").ToString(),0);//输入单次剂量
            string prescType = drMaster["PRESC_TYPE"].ToString();//处方类 
            int repetition = ConvertHelper.ToInt(drMaster["REPETITION"].ToString(),1);//剂数
            drMaster["REPETITION"] = repetition; 
            switch (columnName)
            {
                #region 单次剂量 开始
                case "DOSAGE_EACH":
                    if (prescType.Equals("1"))//草药
                    {
                        if(Parameter.HERBS_DIVIDE_DOSE_PER_UNIT == "1")
                        {
                            inputAmount = Math.Ceiling(Math.Round((dosageEach * repetition) / dosePerUnit, 2));
                        }
                        else
                        {
                            inputAmount = Math.Ceiling(Math.Round(dosageEach * repetition, 2));
                        } 
                        drDetail["INPUT_AMOUNT"] = inputAmount; //输入数量
                        drDetail["QUANTITY"] = inputAmount; //发药数量
                        DetailComputeItemMoney(drMaster, drDetail);//明细记录计算项目金额
                    }
                    else//西药
                    {
                        //有单次用量最大限制,则提醒
                        if (maxDosage > 0 && dosageEach > maxDosage)
                        {
                            //071--药品单次剂量({0})超过单次最大剂量({1})!
                            XtraMessageHelper.Info(Consts.PRESC, "071", new string[] { dosageEach.ToString(), maxDosage.ToString() }); 
                        }       
                    }
                    break;
                #endregion 单次剂量 结束
                #region 途径 开始
                case "ADMINISTRATION":
                    if (prescType.Equals("1"))//草药
                    {
                        SynchrodataGroupDrug("ADMINISTRATION", prescType);//同步成组药品数据
                        gvDrugDetail.FocusedColumn = FREQ_DETAIL;//切换焦点到单次用量 
                        gvDrugDetail.ShowEditor();//打开行编辑 
                    }
                    else //西药
                    {
                        SynchrodataGroupDrug("ADMINISTRATION", prescType);//同步成组药品数据
                        gvDrugDetail.FocusedColumn = FREQUENCY;//切换焦点到单次用量 
                        gvDrugDetail.ShowEditor();//打开行编辑 
                    }
                    break;
                #endregion 途径 结束
                #region 频次 开始
                case "FREQUENCY":
                    SynchrodataGroupDrug("FREQUENCY", prescType);//同步成组药品数据
                    gvDrugDetail.FocusedColumn = FREQ_DETAIL;//切换焦点到医生说明 
                    gvDrugDetail.ShowEditor();//打开行编辑 
                    break;
                #endregion 频次 结束
                case "FREQ_DETAIL":
                    gvDrugDetail.FocusedColumn = INPUT_AMOUNT;//切换焦点到数量
                    gvDrugDetail.ShowEditor();//打开行编辑 
                    break;
                #region 输入总量 开始
                case "INPUT_AMOUNT":
                    if (inpuUnits.Equals(packageUnits))
                    {

                        drDetail["QUANTITY"] = drDetail["INPUT_AMOUNT"];
                    }
                    else
                    {
                    }
                    DetailComputeItemMoney(drMaster, drDetail);//明细记录计算项目金额
                    break;
                #endregion 
            }
            if (rdoPrescType.EditValue.Equals("1"))//输入法后中药页赋值 @meng
            {
                DataRow drCDetail = layoutView1.GetDataRow(layoutView1.FocusedRowHandle);
                drCDetail["DRUG_NAME1"] = drugName;
                drCDetail["CHARGES1"] = drDetail["COSTS"];
                drCDetail["DOSAGE_EACH1"] = drDetail["DOSAGE_EACH"];
                DataRow drInsure = BasicDict.GetDrugInsureInfo(drDetail["DRUG_CODE"].ToString(), drDetail["DRUG_SPEC"].ToString() + drDetail["FIRM_ID"].ToString());
                if (drInsure != null)
                {
                    drCDetail["COUNTRYNAME"] = drInsure["INSUR_NAME"].ToString();
                    drCDetail["COUNTRYCODE"] = drInsure["INSUR_CODE"].ToString();
                }

                dtCDetail.AcceptChanges();
                layoutView1.CloseEditor();
                SendKeys.Send("%{DOWN 1}");
            }
        }
        #endregion 
        #region  同步成组药品数据
        /// <summary>
        /// 同步成组药品数据
        /// </summary>
        /// <param name="columnName">列名称</param>
        /// <param name="prescType">处方类型</param>
        private void SynchrodataGroupDrug(string columnName, string prescType)
        { 
            string columnValue = gvDrugDetail.GetRowCellValue(gvDrugDetail.FocusedRowHandle, columnName).ToString();
            if (prescType.Equals("1"))//草药
            { 
                DataRow[] drs = dtDetail.Select();
                foreach (DataRow dr in drs)
                {
                    dr[columnName] = columnValue.ToString();
                }
            }
            else
            {
                DataRow dr = null; 
                for (int i = gvDrugDetail.FocusedRowHandle + 1; i <= gvDrugDetail.RowCount - 1; i++)
                { 
                    dr = gvDrugDetail.GetDataRow(i);
                    if (dr["ORDER_SUB_NO"].ToString().Equals("1") || string.IsNullOrEmpty(dr["ORDER_SUB_NO"].ToString()))
                    {
                        break;
                    }
                    dr[columnName] = columnValue.ToString();
                }
            }
        }
        #endregion
        #region 复制处方
        /// <summary>
        /// 复制处方
        /// </summary>
        private void CopyPresc()
        { 
            if (gvDrugMaster.FocusedRowHandle<0)
            {
                return;
            }
            DataRow drCopy = dtMaster.Copy().Rows[gvDrugMaster.FocusedRowHandle];
            DataTable dtCopy = dtDetail.Copy(); 
            DateTime prescDateCopy = ConvertHelper.ToDateTime(drCopy["PRESC_DATE"].ToString());
            int prescNoCopy = ConvertHelper.ToInt(drCopy["PRESC_NO"].ToString());
            MasterAdd();//新方 
            DataRow drNew = dtMaster.Rows[gvDrugMaster.FocusedRowHandle];
            drNew["USAGE"] = drCopy["USAGE"].ToString();
            drNew["PRESC_TYPE"] = drCopy["PRESC_TYPE"].ToString();
            drNew["DISPENSARY"] = drCopy["DISPENSARY"].ToString();
            drNew["REPETITION"] = ConvertHelper.ToInt(drCopy["REPETITION"].ToString(), 0); 
            drNew["DECOCTION"] = ConvertHelper.ToInt(drCopy["DECOCTION"].ToString(), 0); 
            drNew["COUNT_PER_REPETITION"] = ConvertHelper.ToInt(drCopy["COUNT_PER_REPETITION"].ToString(), 0);
            SetMasterData(drNew); //设置中间的数据
            DateTime prescDateNew = ConvertHelper.ToDateTime(drNew["PRESC_DATE"].ToString());
            int prescNoNew = ConvertHelper.ToInt(drNew["PRESC_NO"].ToString());
            dtDetail = srvPrescMain.GetPrescDetail(prescDateNew, prescNoNew);
            dtDetailDel.Clear();
            foreach (DataRow dr in dtCopy.Rows)
            {
                string drugName = dr["DRUG_NAME"].ToString();//药品名称
                string drugCode = dr["DRUG_CODE"].ToString();//药品代码
                string drugSpecFrim = dr["PACKAGE_SPEC"].ToString() + dr["FIRM_ID"].ToString();//规格厂家
                DataTable dtDrugInfo = null;
                if (CommonDrug.GetDrugInfo(drugCode, drugName, drugSpecFrim, ref dtDrugInfo) == -1) //停价则返回
                {
                    continue;
                }
                #region 分线用药判断
                int limitDrugRet = CommonDrug.JudgeLimitDrug(dtDrugInfo, CurrentPatientId, CurrentVisitId, drPatientInfo["DEPT_CODE"].ToString());
                if (limitDrugRet == -1)
                {
                    continue;
                }
                int antibacterialFlag = ConvertHelper.ToInt(dtDrugInfo.Rows[0]["ANTIBACTERIAL_FLAG"], 0);//抗菌用药标志
                string drugAuthoritySerialNo = "";
                string drugPurpose = "";
                //抗菌药物
                if (antibacterialFlag == 1 && limitDrugRet != 1 && !CommonDrug.GetAntibacterialsApply(drPatientInfo, drugCode, drugName, limitDrugRet, ref drugAuthoritySerialNo, ref drugPurpose))
                {
                    continue;
                }
                #endregion
                DataRow drDetail = dtDetail.NewRow();
                drDetail = dr;
                drDetail["PRESC_DATE"] = prescDateNew;
                drDetail["PRESC_NO"] = prescNoNew;
                if (!string.IsNullOrEmpty(drugAuthoritySerialNo))
                {
                    drDetail["DRUG_AUTHORITY_SERIAL_NO"] = drugAuthoritySerialNo;
                }
                if (!string.IsNullOrEmpty(drugPurpose))
                {
                    drDetail["DRUG_PURPOSE"] = drugPurpose;
                }
                dtDetail.Rows.Add(dr.ItemArray);
                DetailComputeItemMoney(drNew, drDetail);//明细记录计算项目金额
            }
            gcDrugDetail.DataSource= dtDetail;
        }
        #endregion
        #endregion
        #region 事件 
        #region  诊断输入法弹框的事件
        private void txtDiagnosisName_Enter(object sender, EventArgs e)
        {
            PopupContainerEdit pce = (PopupContainerEdit)sender;
            ucDiagnosis.ShowPopControl(this.gcDrugDetail, pce, true); //弹出输入法
            SendKeys.SendWait("%{DOWN}");
        }
        #endregion
        #region 频次输入法弹框的事件
        //频次输入法弹框
        private void cellpopupFrequency_Enter(object sender, EventArgs e)
        {
            PopupContainerEdit pce = (PopupContainerEdit)sender;
            ucFreq.ShowPopControl(this.gcDrugDetail, pce, true); //弹出输入法
            SendKeys.SendWait("%{DOWN}");
        }
        #endregion
        #region 途径 输入法弹框的事件
        //途径输入法弹框
        private void cellpopupAdministration_Enter(object sender, EventArgs e)
        {
            DataRow drCurrent = gvDrugDetail.GetDataRow(gvDrugDetail.FocusedRowHandle);
            string drugSpec = drCurrent["DRUG_SPEC"].ToString();//最小单位规格
            string drugCode = drCurrent["DRUG_CODE"].ToString();
            string administrationLists = CommonDrug.GetDrugRationalDosageAdministrationList(drugCode, drugSpec);
            if (rdoPrescType.EditValue.Equals("0") && !string.IsNullOrEmpty(administrationLists)) //如果是西药
            {
                ucAdministration.RefreshGridData("ADMINISTRATION_NAME in (" + administrationLists + ")");
            }
            else
            {
                ucAdministration.RefreshGridData("");
            }
            if (rdoPrescType.EditValue.Equals("1")) //如果是中药的话
            {
                ucAdministration.RefreshGridData("CDRUG_USING_FLAG ='1'");
            }
            PopupContainerEdit pce = (PopupContainerEdit)sender;
            ucAdministration.ShowPopControl(this.gcDrugDetail, pce, true); //弹出输入法
            SendKeys.SendWait("%{DOWN}");
        }
        #endregion 
        #region 药品名称输入法弹框的事件
        //药品名称输入法弹框
        private void cellpopupDrugName_Enter(object sender, EventArgs e)
        {
            InputDrugName();
            PopupContainerEdit pce = (PopupContainerEdit)sender;
            pce.Properties.PopupControl = pccDrugName;
            ucDrugName.ShowPopControl(this.gcDrugDetail, pce, true); //弹出输入法
            //SendKeys.SendWait("%{DOWN}");
        }
        #endregion
        #region frmPrescMain的事件
        #region Shown事件
        private void frmPrescMain_Shown(object sender, EventArgs e)
        {
            if (!showEnable)
            {
                this.Close();
            }
            else
            {
                LoadingInputMethod();//加载输入法  
                this.gvDrugMaster.MoveNextPage();//打开的时候，处方数量过多的时候，滚动条滚动到最底端 
                                                 //2020-11-1 wanglijun 解决药品进入程序后，直接选择草药，输入法还是显示西药的问题
                                                 //CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, gvDrugDetail.FocusedRowHandle, false, ADMINISTRATION);
                                                 //CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, gvDrugDetail.FocusedRowHandle, false, DRUG_NAME);
                CommonDevMethod.GridViewLocateEditColumns(gvDrugDetail, gvDrugDetail.FocusedRowHandle, false, PACKAGE_SPEC);
            } 
        }
        #endregion
        #region FormClosed 事件
        private void frmPrescMain_FormClosed(object sender, FormClosedEventArgs e)
        {
            XtraLookUpHelper.CloseDispose(lookupDispensary);
            XtraPopupContainerControlHelper.CloseDispose(pccDrugName, pccFreq);
        }
        #endregion
        #endregion
        #region   gvDrugMaster的事件  
        private void gvDrugMaster_Click(object sender, EventArgs e)
        {
            if(gvDrugMaster.FocusedRowHandle>-1)
            {
                MasterSelect(gvDrugMaster.FocusedRowHandle,true);
            } 
        }
        private void gvDrugMaster_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            if (gvDrugMaster.FocusedRowHandle >= 0)
            {
                SetControlReadOnly();
                DetailVisible();
            }
        }
        #endregion
        #region gvDrugDetail事件
        #region CellValueChanged  事件
        private void gvDrugDetail_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (gvDrugDetail.RowCount > 0 && e.RowHandle >= 0)
            {
                string columnName = e.Column.FieldName;
                if (columnName.Equals("DOSAGE_EACH") || columnName.Equals("ADMINISTRATION")
                    || columnName.Equals("FREQUENCY") || columnName.Equals("INPUT_AMOUNT") || columnName.Equals("FREQ_DETAIL"))
                {
                    GridCellValueChanging(columnName);//窗口变化事件 -长临、剂量的内容变更事件
                }
            }
        }
        #endregion
        #region KeyDown 事件
        private void gvDrugDetail_KeyDown(object sender, KeyEventArgs e)
        {
            OnDetailGridViewKeyDown(sender, e);
        }
        #endregion
        #region ShowingEditor  事件
        private void gvDrugDetail_ShowingEditor(object sender, System.ComponentModel.CancelEventArgs e)
        {
            DataRow drMaster = gvDrugMaster.GetDataRow(gvDrugMaster.FocusedRowHandle);//当前选中行
            if (drMaster == null) return; 
            if (ConvertHelper.ToInt(drMaster["PRESC_STATUS"]) != 0) //非新方
            {
                e.Cancel = true;
            }
            else
            {
                SetColumnReadOnly();//设置只读属性 
            } 
        }
        #endregion
        #region FocusedRowChanged 事件
        private void gvDrugDetail_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            SetColumnReadOnly();//设置只读属性 
            /*
            if (gvDrugDetail.FocusedColumn == gvDrugDetail.Columns.ColumnByFieldName("DRUG_NAME"))
            {
                RationalAdministration.InpPrescDrug(gvDrugDetail.GetDataRow(gvDrugDetail.FocusedRowHandle));
            }
            else
            {
                RationalAdministration.CloseDrugHint();//关闭浮窗
            }*/
        }
        #endregion
        #region FocusedRowChanged  事件
        private void gvDrugDetail_FocusedColumnChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventArgs e)
        {
            SetColumnReadOnly();//设置只读属性 
            /*
            if (gvDrugDetail.FocusedRowHandle >= 0)
            {
                DataRow dr = gvDrugDetail.GetDataRow(gvDrugDetail.FocusedRowHandle);

                if (e.FocusedColumn == DRUG_NAME)
                {
                    RationalAdministration.InpPrescDrug(gvDrugDetail.GetDataRow(gvDrugDetail.FocusedRowHandle));
                }
                else
                {
                    RationalAdministration.CloseDrugHint();//关闭浮窗
                }
            }*/
        }
        #endregion 
        #endregion
        #region 修改处方主记录相关事件
        #region 诊断
        //诊断
        private void txtDiagnosisName_TextChanged(object sender, EventArgs e)
        {
            DataRow drMaster = null;
            if (!CheckUpMasterSelect("请输入诊断", ref drMaster))
            {
                return;
            }
            drMaster["DIAGNOSIS_NAME"] = txtDiagnosisName.Text; 
        }
        #endregion 
        #region 处方名        
        private void txtBindingPrescTitle_TextChanged(object sender, EventArgs e)
        {
            DataRow drMaster = null;
            if (!CheckUpMasterSelect("设置草药处方名", ref drMaster))
            {
                return;
            }
            drMaster["BINDING_PRESC_TITLE"] = txtBindingPrescTitle.Text; 
        }
        #endregion 
        #region 药局
        private void lookupDispensary_EditValueChanged(object sender, EventArgs e)
        {
            DataRow drMaster = null;
            if (!CheckUpMasterSelect("选择药局", ref drMaster))
            {
                return;
            }
            currentDispensary = lookupDispensary.EditValue.ToString();
            drMaster["DISPENSARY"] = currentDispensary;  
        }
        #endregion 
        #region 出院带药
        private void chkdischargeTakingIndicator_CheckedChanged(object sender, EventArgs e)
        {
            DataRow drMaster = null;
            if (!CheckUpMasterSelect("设置出院带药", ref drMaster))
            {
                return;
            }
            int dischargeTakingIndicator = 0;
            if (chkdischargeTakingIndicator.Checked)
            {
                dischargeTakingIndicator = 1;
                drMaster["DISCHARGE_TAKING_INDICATOR"] = dischargeTakingIndicator;
            }
            else
            {
                dischargeTakingIndicator = 0;
                drMaster["DISCHARGE_TAKING_INDICATOR"] = dischargeTakingIndicator; 
            }
        }
        #endregion 
        #region 剂数
        private void speRepetition_EditValueChanged(object sender, EventArgs e)
        {
            DataRow drMaster = null;
            if (!CheckUpMasterSelect("设置草药剂数", ref drMaster))
            {
                return;
            }
            int decoction = 1; 
            if (!string.IsNullOrEmpty(speRepetition.Text))
            {
                decoction = ConvertHelper.ToInt(speRepetition.Text,1);
            }
            drMaster["REPETITION"] = decoction;
            foreach (DataRow drDetail in dtDetail.Select())
            {
                decimal dosePerUnit = ConvertHelper.ToDecimal(drDetail["DOSAGE"]);//最小单位剂量
                if (dosePerUnit == 0)
                {
                    MessageBox.Show("最小单位剂量不能为空，请录入药品的最小单位剂量！");
                    return;
                }
                decimal dosageEach = ConvertHelper.ToDecimal(drDetail["DOSAGE_EACH"], 0);//输入单次剂量
                decimal inputAmount = 0;
                if (Parameter.HERBS_DIVIDE_DOSE_PER_UNIT == "1")
                {
                    inputAmount=Math.Ceiling(Math.Round((dosageEach * decoction) / dosePerUnit, 2));
                } 
                else
                {
                    inputAmount= Math.Ceiling(Math.Round(dosageEach * decoction, 2));
                }
                drDetail["INPUT_AMOUNT"] = inputAmount; //输入数量
                drDetail["QUANTITY"] = inputAmount; //发药数量
                DetailComputeItemMoney(drMaster, drDetail);//明细记录计算项目金额
            }
        }
        #endregion 
        #region 每剂煎几份
        private void speCountPerRepetition_EditValueChanged(object sender, EventArgs e)
        {
            DataRow drMaster = null;
            if (!CheckUpMasterSelect("设置草药煎几份", ref drMaster))
            {
                return;
            }
            int countPerRepetition = 0;
            if (!string.IsNullOrEmpty(speCountPerRepetition.Text))
            {
                countPerRepetition = ConvertHelper.ToInt(speCountPerRepetition.EditValue);
            }
            drMaster["COUNT_PER_REPETITION"] = countPerRepetition;
        }
        #endregion 
        #region 用法
        private void txtUsage_TextChanged(object sender, EventArgs e)
        {
            DataRow drMaster = null;
            if (!CheckUpMasterSelect("设置草药用法", ref drMaster))
            {
                return;
            }
            drMaster["USAGE"] = lookUpEdit_Usage.Text;
        }
        #endregion 
        #region 是否代煎
        //private void chkDecoction_CheckedChanged(object sender, EventArgs e)
        //{
        //    DataRow drMaster = null;
        //    if (!CheckUpMasterSelect("设置代煎", ref drMaster))
        //    {
        //        return;
        //    }
        //    int decoction = 0;
        //    if (chkDecoction.Checked)
        //    {
        //        decoction = 1;
        //    }
        //    drMaster["DECOCTION"] = decoction;
        //}
        #endregion 
        #region 处方类型
        private void rdoPrescType_EditValueChanged(object sender, EventArgs e)
        {
            string prescType = rdoPrescType.EditValue.ToString();
            DataRow drMaster = gvDrugMaster.GetDataRow(gvDrugMaster.FocusedRowHandle);
            if (gvDrugMaster.RowCount > 0 && drMaster != null)
            { 
                SetDispensary(prescType, "", drMaster);//绑定药房数据  
                if (drMaster.RowState == DataRowState.Added)
                { 
                    drMaster["PRESC_TYPE"] = ConvertHelper.ToInt(prescType);
                    SetMasterEnabled(prescType, true, drMaster.RowState.ToString());//设置处方主记录控件的编辑属性 
                } 
            }
            DetailVisible(); 
        }
        #endregion 
        #endregion 
        #region 按钮事件
        #region 加药单击事件  
        private void btnAdd_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        { 
            DetailAdd(); 
        } 
        private void btnRootAdd_Click(object sender, EventArgs e)
        {
            DetailAdd();
        }
        #endregion
        #region 减药按钮单击事件 
        private void btnDel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (gvDrugDetail.RowCount > 0)
            {
                DetailDel();//减药
            }
        }
        private void btnRootDel_Click(object sender, EventArgs e)
        {
            if (gvDrugDetail.RowCount > 0)
            {
                DetailDel();//减药
            }
        }
        #endregion 
        #region 新方按钮单击事件  
        private void btnPresc_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            MasterAdd(); 
        }
        private void btnRootPresc_Click(object sender, EventArgs e)
        {
            MasterAdd(); 
        }
        #endregion
        #region 毁方按钮单击事件  
        private void btnDelPrecs_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            MasterDel();//毁方 
        }
        private void btnRootDelPrecs_Click(object sender, EventArgs e)
        { 
            MasterDel();//毁方 
        }
        #endregion
        #region 子处方按钮单击事件  
        private void btnChildPresc_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (gvDrugDetail.RowCount > 0)
            {
                SetUpChildresc(); //设置子处方
            }
        }
        private void btnRootChildPresc_Click(object sender, EventArgs e)
        {
            if (gvDrugDetail.RowCount > 0)
            { 
                SetUpChildresc(); //设置子处方
            }
        }
        #endregion
        #region 协定处方单击按钮事件 
        //协定处方
        private void btnBindingPresc_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            PrescGroupCall();
        }
        private void btnRootBindingPresc_Click(object sender, EventArgs e)
        {
            PrescGroupCall(); 
        }
        #endregion
        #region 刷新按钮单击事件 
        //刷新
        private void btnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData(); //刷新
        }
        private void btnRootRefresh_Click(object sender, EventArgs e)
        {
            LoadData(); //刷新
        }
        #endregion
        #region 保存按钮单击事件 
        private void BtnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            { 
                PrescSave();
            }
            catch (Exception ex )
            {
                XtraMessageBox.Show("\r\nfrmPrescMain-BtnSave_ItemClick-Exception:" + ex.Message,"温馨提示"); 
            }
        }
        private void BtnRootSave_Click(object sender, EventArgs e)
        {
            try
            {
                PrescSave();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("\r\nfrmPrescMain-BtnRootSave_Click-Exception:" + ex.Message, "温馨提示");
            }
        }
        #endregion
        #region 打印按钮单击事件  
        private void btnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            PrescPrint();
        } 
        private void btnRootPrint_Click(object sender, EventArgs e)
        {
            PrescPrint();
        }
        #endregion 
        #region 关闭按钮单击事件 
        private void btnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        private void btnRootClose_Click(object sender, EventArgs e)
        {
            this.Close();
        } 
        #endregion
        #endregion 
        #region 右键菜单事件
        private void MenuTop_ItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            if (gvDrugMaster.FocusedRowHandle < 0)
            {
                return;
            }
            if ((e.ClickedItem).Name == "MenuCopy")//复制
            {
                CopyPresc();
            }
            else if ((e.ClickedItem).Name == "tsmiClosedLoop")//闭环查询
            {
                MenuTop.Close();
                IPlatform.PlatformPrescClosedLoop(gvDrugMaster.GetDataRow(gvDrugMaster.FocusedRowHandle));
            }
        }

        #endregion
        #region 药品说明书
        private void tsmiRADrugInstructions_Click(object sender, EventArgs e)
        {
            RationalAdministration.InpPrescDrug(gvDrugDetail.GetDataRow(gvDrugDetail.FocusedRowHandle));
        }
        #endregion
        #region cellLookUpDrugPurpos 事件
        private void cellLookUpDrugPurpose_Enter(object sender, EventArgs e)
        {
            SendKeys.SendWait("%{DOWN}");
        }

        #endregion

        #endregion
        /// <summary>
        /// 修改中药数量写入到处方明细表 @meng
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void layoutView1_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (e.Column.FieldName.Contains("DOSAGE_EACH"))
            {
                DataRow drZY = this.layoutView1.GetDataRow(this.layoutView1.FocusedRowHandle);
                DataRow[] rowArray = dtDetail.AsEnumerable().Where(r => r.GetType().Name == "DataRow" && r["DRUG_NAME"] == drZY["DRUG_NAME1"]).ToArray();
                DataRow row2 = this.gvDrugMaster.GetDataRow(this.gvDrugMaster.FocusedRowHandle);
                if (rowArray.Length == 1)
                {
                    if (drZY["DRUG_NAME1"].Equals(rowArray[0]["DRUG_NAME"]))
                    {
                        rowArray[0]["DOSAGE_EACH"] = decimal.Parse(drZY[e.Column.FieldName].ToString());//单次计量
                        //rowArray[0]["DOSAGE"] = rowArray[0]["DRUG_SPEC"];//decimal.Parse(drZY[e.Column.FieldName].ToString());//单次计量
                        rowArray[0]["QUANTITY"] = decimal.Parse(speRepetition.Text) * decimal.Parse(drZY[e.Column.FieldName].ToString());//计算数量  剂数*单次剂量
                        rowArray[0]["INPUT_AMOUNT"] = decimal.Parse(speRepetition.Text) * decimal.Parse(drZY[e.Column.FieldName].ToString());//计算数量  剂数*单次剂量
                        rowArray[0]["ADMINISTRATION"] = "水煎服";
                        //row2["COSTS"] = rowArray[0]["COSTS"];
                        //row2["PAYMENTS"] = rowArray[0]["PAYMENTS"];
                        //amountZY += decimal.Parse(row2["PAYMENTS"].ToString());
                        //sumZYJE.Text = "合计：" + amountZY + "元";
                        DetailComputeItemMoney(row2, rowArray[0]);
                    }
                }
            }
            if (e.Column.FieldName.Contains("FREQ_DETAIL"))
            {
                DataRow drZY = this.layoutView1.GetDataRow(this.layoutView1.FocusedRowHandle);//当前中药行的数据
                DataRow[] rowArray = dtDetail.AsEnumerable().Where(r => r.GetType().Name == "DataRow" && r["DRUG_NAME"] == drZY["DRUG_NAME1"]).ToArray();
                if (rowArray.Length == 1)
                {
                    if (drZY["DRUG_NAME1"].Equals(rowArray[0]["DRUG_NAME"]))
                    {
                        rowArray[0]["FREQ_DETAIL"] = drZY[e.Column.FieldName];//医生说明
                    }
                }
            }
            gvDrugDetail.UpdateCurrentRow();
        }

        /// <summary>
        /// 中药回车事件切换到下一个输入位置,最后一个调用新增 @meng
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void layoutView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if ("DRUG_NAME1".Equals(layoutView1.FocusedColumn.FieldName))
                {
                    DataRow drZY = this.layoutView1.GetDataRow(this.layoutView1.FocusedRowHandle);//当前中药行的数据
                    if (string.IsNullOrEmpty(drZY["DRUG_NAME1"].ToString()))
                    {
                        SendKeys.Send("%{DOWN 1}");
                    }
                    else
                    {
                        SendKeys.Send("%{DOWN 1}");
                    }
                }
                if ("DOSAGE_EACH1".Equals(layoutView1.FocusedColumn.FieldName))
                {
                    //SendKeys.Send("%{DOWN 1}");
                    this.layoutView1.MoveNext();
                    SendKeys.SendWait("{Tab}");
                    SendKeys.SendWait("{Tab}");
                    this.layoutView1.ShowEditor();
                }
                if ("FREQ_DETAIL1".Equals(layoutView1.FocusedColumn.FieldName))
                {
                    layoutView1.CloseEditor();
                    layoutView1.UpdateCurrentRow();
                    DetailAdd();
                    layoutView1.CloseEditor();
                    //SendKeys.Send("%{ENTER 1}");
                    SendKeys.Send("%{DOWN 1}");
                }
            }
        }
        /// <summary>
        /// 药品名称输入法弹框
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void repositoryItemPopupContainerEdit1_Enter(object sender, EventArgs e)
        {
            try
            {
                InputDrugName();
                PopupContainerEdit pce = (PopupContainerEdit)sender;
                pce.Properties.PopupControl = pccDrugName;
                ucDrugName.ShowPopControl(this.gridControl1, pce, true); //弹出输入法
                //SendKeys.SendWait("%{DOWN}");
            }
            catch (Exception)
            {

              
            }
            
        }

        private void layoutView1_FocusedColumnChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventArgs e)
        {
            gvDrugDetail.FocusedRowHandle = layoutView1.FocusedRowHandle;
        }

        /// <summary>
        /// 特殊用法值变化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lue_specialRequest_EditValueChanged(object sender, EventArgs e)
        {
            DataRow dr = layoutView1.GetFocusedDataRow();
            if (dr == null) return;

            DevExpress.XtraEditors.Controls.ChangingEventArgs args = e as DevExpress.XtraEditors.Controls.ChangingEventArgs;
            dr["SPECIALREQUEST"] = args.NewValue;

            DataRow dr1 = gvDrugDetail.GetFocusedDataRow();
            dr1["SPECIAL_REQUEST"] = args.NewValue;
        }

        /// <summary>
        /// 煎法值变化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lue_administration_EditValueChanged(object sender, EventArgs e)
        {
            DataRow dr = layoutView1.GetFocusedDataRow();
            if (dr == null) return;

            DevExpress.XtraEditors.Controls.ChangingEventArgs args = e as DevExpress.XtraEditors.Controls.ChangingEventArgs;
            dr["ADMINISTRATION"] = args.NewValue;

            DataRow dr1 = gvDrugDetail.GetFocusedDataRow();
            dr1["ADMINISTRATION"] = args.NewValue;
        }

        private void lueDecoction_EditValueChanged(object sender, EventArgs e)
        {
            DataRow drMaster = null;
            if (!CheckUpMasterSelect("设置代煎", ref drMaster))
            {
                return;
            }
            
            drMaster["DECOCTION"] = lueDecoction.EditValue??"".ToString(); ;
        }

        private void btnSaveAsPrescription_Click(object sender, EventArgs e)
        {
            DataRow drMaster = gvDrugMaster.GetDataRow(gvDrugMaster.FocusedRowHandle);
            if (drMaster == null)
                return;
            DataTable dtDetail =(DataTable)gcDrugDetail.DataSource;
            Presc.Template.frmPrescCipherPrescription frm = new Template.frmPrescCipherPrescription(drMaster, dtDetail);
            frm.ShowDialog();
        }
    }
}
