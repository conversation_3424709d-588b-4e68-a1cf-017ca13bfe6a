﻿using DevExpress.XtraEditors;
using NM_Service.NMService;
using PlatCommon.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraBars;
using DevExpress.XtraBars.ViewInfo;
//using Obilling_Station.insur_interface;
using PlatCommon.SysBase;
using System.Xml;
using PlatCommonForm;
using static Tjhis.Obilling.Station.View.frm_invoice_set;
using PlatCommon.Comm;
using Tjhis_Reconciliation_Platform;
using Tjhis.Interface.Station;
using TjhisInterfaceInsurance;
using TjhisInterfaceInsurance.Army;
using Tjhis.Reconciliation.Platform;
using Tjhis.WebService.Station.ElInvoice;
using Tjhis.Interface.Station.IdentityCard;
using Tjhis.Interface.Station.PaymentMode;
using System.Collections;

namespace Tjhis.Obilling.Station.View
{
    public partial class frm_refund : PlatCommon.SysBase.ParentForm
    {
        bool part_refund = false;
        bool ib_back = false;
        bool completed = false;
        bool start_save;               //是否开始保存
        string rcpt_no;
        string is_opercode; // 退费项目门诊操作码
        private string acctNo;//结账号
        string is_patientid;  //病人ID
        string name;   //病人姓名
        string name_phonetic; //姓名拼音
        string identity;//身份
        string charge_type;//费别
        string contract_code; //合同单位代码
        string contract_name;//合同单位名称
        DateTime idt_visitdate; //就诊日期及时间
        decimal total_costs, total_charges;
        string insurance_no;
        string insurance_type;
        string is_type;
        bool ib_is_medcard = false; //是否医疗卡用户 感觉没啥用了
        Int32 charge_numerator;
        Int32 charge_denominator;
        bool special_indicator;
        Int32 price_type;   //优惠标志
        DataTable dw_query_rcpt;
        DataTable dw_bill_detail;
        DataTable dw_bill_detail_new;
        DataTable dw_order;
        DataTable dw_order_new;
        DataTable dw_payments_money;
        DataTable dw_payments_money_new;
        DataTable dw_payment;
        DataTable dw_rcpt_detail;
        DataTable dw_rcpt_detail_init;
        int money_rows = 0;
        string gs_real_payways;  //可退现金的支付方式
        string prior_money_type;
        string rcpt_refund_no;  //退费收据号
        string gs_refund_payway; //可退现金的支付方式
        string is_bankrefundmode = "";//退现金模式
        string gs_payway_prepayment; // 预交金支付方式名称
        Dictionary<string, string> idc; //保存方式
        bool prepayment_flag = false;  //预交金使用标识
        string gs_insurance_chargetype; //医保费别
        bool insurance_flag;
        string rcpt_curr_no;     //新收据号
        bool print_card;
        string gs_capability;
        string is_pwd;
        string is_id_no;
        string isInsurCardNo = "";
        string is_wcaalipay = "";
        Dictionary<string, string> dic = new Dictionary<string, string>();
        string isOpenThirdPay =SystemParm.GetParameterValue("ISOPENTHIRDPAY", "*", "*", SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);//是否启用第三方支付总开关
        string selPayMode =SystemParm.GetParameterValue("SELECTPAYMODE", "*", "*", SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);//启用的是哪个第三方接口
        public frm_refund()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 设置卡按钮状态
        /// </summary>
        private void SetCardButton()
        {
            //身份证
            if (dic.ContainsKey("READ_ID_NO_TYPE") && dic["READ_ID_NO_TYPE"] != "00")
            {
                barIDCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barIDCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
            //院内卡
            if (dic.ContainsKey("READ_CARD_NO_TYPE") && dic["READ_CARD_NO_TYPE"] != "00")
            {
                barHospitalCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barHospitalCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
            //电子健康卡
            if (dic.ContainsKey("READ_HEALTH_NO_TYPE") && dic["READ_HEALTH_NO_TYPE"] != "00")
            {
                barHealthCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barHealthCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
        }
        private void frm_refund_Load(object sender, EventArgs e)
        {
            init();
            is_wcaalipay = SystemParm.GetParameterValue("WCAALIPAY", "*", this.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);//"0"天健支付平台微信支付宝支付
            //设定读卡状态
            dic = ReadIdentityCardBusiness.GetCardParameter(this.AppCode);
            SetCardButton();
        }

        private void init()
        {
            //labelkey1.Text = "收据查询";
            //labelkey2.Text = "";
            //labelkey3.Text = "备份查询";
            //labelkey4.Text = "全部退费";
            //labelkey5.Text = "清屏";
            //labelkey6.Text = "接口读卡";//部分退费
            //labelkey7.Text = "";
            //labelkey8.Text = "退出";

            sle_rcpt.Text = new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString("yyyyMMdd");
            //sle_no.ShowingEditor();  //设置 焦点 指向
            sle_no.Focus();

            //获取实收款的支付方式
            string ls_valus = PlatCommon.SysBase.SystemParm.GetParameterValue("PAY_WAY_REAL", "OBILLING", this.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
            if (string.IsNullOrEmpty(ls_valus))
            {
                gs_real_payways = "现金";
            }
            else
            {
                gs_real_payways = ls_valus;
            }
            //可退现金的支付方式
            ls_valus = PlatCommon.SysBase.SystemParm.GetParameterValue("PAY_WAY_REAL", "OBILLING", this.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
            if (string.IsNullOrEmpty(ls_valus))
            {
                gs_refund_payway = "无";
            }
            else
            {
                gs_refund_payway = ls_valus;
            }


            // 预交金支付方式名称
            ls_valus = PlatCommon.SysBase.SystemParm.GetParameterValue("PAY_WAY_PREPAYMENT", "OBILLING", this.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
            if (string.IsNullOrEmpty(ls_valus))
            {
                gs_payway_prepayment = "门诊预存";
            }
            else
            {
                gs_payway_prepayment = ls_valus;
            }

            //是否支持预交金判断
            ls_valus = PlatCommon.SysBase.SystemParm.GetParameterValue("PREPAYMENT_FLAG", "OBILLING", this.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
            if (ls_valus.Equals("yes"))
            {
                prepayment_flag = true;
            }
            else
            {
                prepayment_flag = false;
            }


            //医保启用的费别
            ls_valus = PlatCommon.SysBase.SystemParm.GetParameterValue("INSURANCE_CHARGE_TYPE", "OBILLING", this.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
            if (!string.IsNullOrEmpty(ls_valus))
            {
                insurance_flag = true;
                gs_insurance_chargetype = ls_valus;
            }
            else
            {
                insurance_flag = false;
                gs_insurance_chargetype = "无";
            }

            //获取权限值
            string sql = "select a.capability  from app_grants  a  where a.application='OBILLING' and a.user_id='" + PlatCommon.SysBase.SystemParm.LoginUser.ID + "'";
            DataTable dt = new ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt.Rows.Count > 0)
            {
                gs_capability = dt.Rows[0]["CAPABILITY"].ToString();
            }
            else
            {
                gs_capability = "无";
            }

            dw_retrieve();

        }


        private void dw_retrieve()
        {
            string sql = string.Format("SELECT FEE_CLASS_CODE,0 AMOUNT,FEE_CLASS_NAME FROM OUTP_RCPT_FEE_DICT WHERE HIS_UNIT_CODE='{0}'   ORDER BY OUTP_RCPT_FEE_DICT.FEE_CLASS_CODE ASC  ", SystemParm.HisUnitCode);
            dw_rcpt_detail = new ServerPublicClient().GetList(sql).Tables[0];
            gridControl1.DataSource = dw_rcpt_detail;
            dw_rcpt_detail_init = dw_rcpt_detail;
        }

        //初始化药品分类
        public void GetItem_Input()
        {
            List<AuxiliaryDictionary> list = new List<AuxiliaryDictionary>();
            list.Add(new AuxiliaryDictionary("药品", "药品"));
            list.Add(new AuxiliaryDictionary("非药品", "非药品"));
            repositoryItemLookUpInput.DataSource = list;
            repositoryItemLookUpInput.DisplayMember = "Name";
            repositoryItemLookUpInput.ValueMember = "ID";

            DataTable dt = class_obilling_public.GetDeptName();
            repositoryItemLookUpDEPT.DataSource = dt;
            repositoryItemLookUpDEPT.DisplayMember = "DEPT_NAME";
            repositoryItemLookUpDEPT.ValueMember = "DEPT_CODE";
        }

        private void frm_refund_KeyDown(object sender, KeyEventArgs e)
        {
            string ls_invoice_no, ls_rcpt_operator, ls_charge_type;
            //sle_rcpt.Text = rcpt;
            //sle_no.Text = no;

            rcpt_no = sle_rcpt.Text.ToString().Trim() + sle_no.Text.ToString().Trim();
            ls_invoice_no = sle_invoice_no.Text.ToString();
            Keys key = e.KeyCode;
            switch (e.KeyCode)
            {
                case Keys.F1:
                    blbtnSelect_ItemClick(null, null);
                    break;
                case Keys.Enter:
                    blbtnSelect_ItemClick(null, null);
                    break;
                case Keys.F2:
                    break;
                case Keys.F3:
                    blbtnCoptSelect_ItemClick(null, null);
                    break;
                case Keys.F4:
                    blbtnAllReturn_ItemClick(null, null);
                    break;
                case Keys.F5:
                    ue_cancel();
                    break;
                case Keys.F6:
                    ue_cancel();
                    break;
                case Keys.F7:
                    break;
                case Keys.F8:
                    this.Close();
                    break;

            }

        }

        //开始退费
        private void ue_complete()
        {
            int li_tmp = 0;
            decimal ldec_costs, ldec_charges; //费用
            if (total_costs == 0)
            {
                XtraMessageBox.Show("退费前请先查询收据信息！", "退费处理出错");
                start_save = false;
                return;
            }
            //退费是否判断已结账
            //IsAlreadyAccted(acctNo, out bool bAlreadyAcct);//判断是否已经结账，如果已经结账了，则不让退费
            //if (bAlreadyAcct)
            //    return; 

            if (part_refund == true)
            {
                ue_settle();
                if (decimal.Parse(em_costs.Text) == 0) part_refund = false;
            }

            //st_info.Text = "正在作退费处理,请稍候... ...";

            //取退的收据号
            li_tmp = wf_get_rcpt_no(ref rcpt_refund_no);
            if (li_tmp != 0)
            {
                Close();
                return;
            }
            string ls_err = "";
            // =====================================
            // 若发票使用银联支付，则提前验证，并让用户选择银联支付的退款方式
            // 这个就先把数据取着 然后
            // =====================================
            string ls_transno, ls_cardno;
            string ls_InfoTypeCode, ls_InfoNumber, ls_tj_flag, li_infokind, ls_his_unit_code;
            DateTime ldt_startdate, ldt_enddate, ldt_sysdate, ldt_visit_date;
            string ldt_banktransdate; //交易时间 海鹚也用
            string ls_patient_id, ls_pat_name, ls_charge_type, ls_return_name, ls_drug_return;
            string ls_tranno, ls_app_source, ls_paymentmode;//桃谷 海鹚 12320 自助机等订单号 退费及对账用  // APP 来源 如 桃谷 海鹚 健康卡  pos  //APP支付方式码 0A 支付宝 OB微信
            string Systemreferno = string.Empty;
            string ls_amt = string.Empty;
            string oldoperatorno = string.Empty;
            string yhjyly = string.Empty;//考虑到天桃老数据
            string sql = string.Format("Select bank_trans_no, bank_card_no, bank_trans_date, sysdate, trunc(sysdate) startdate, trunc(sysdate) + 0.998 enddate ,visit_date , Invoice_infokind , Invoice_infotypecode , Invoice_infonumber ,  patient_id , name ,  charge_type ,  tj_flag , return_name,HIS_UNIT_CODE ,visit_no,clinic_no,tranno,app_source,paymentmode,drug_return,SYSTEMREFERNO,AMT,OPERATOR_NO,yhjy_LY From outp_rcpt_master Where rcpt_no ='{0}' and his_unit_code='{1}'", rcpt_no, SystemParm.HisUnitCode);
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count <= 0)
            {
                XtraMessageBox.Show("查询收费记录失败！", "提示");
                return;
            }
            ls_transno = dt.Rows[0]["BANK_TRANS_NO"].ToString();
            ls_cardno = dt.Rows[0]["BANK_CARD_NO"].ToString();
            ldt_banktransdate = dt.Rows[0]["BANK_TRANS_DATE"].ToString();
            ldt_sysdate = DateTime.Parse(dt.Rows[0]["SYSDATE"].ToString());
            ldt_startdate = DateTime.Parse(dt.Rows[0]["STARTDATE"].ToString());
            ldt_enddate = DateTime.Parse(dt.Rows[0]["ENDDATE"].ToString());
            ldt_visit_date = DateTime.Parse(dt.Rows[0]["VISIT_DATE"].ToString());
            li_infokind = dt.Rows[0]["INVOICE_INFOKIND"].ToString();
            ls_InfoTypeCode = dt.Rows[0]["INVOICE_INFOTYPECODE"].ToString();
            ls_InfoNumber = dt.Rows[0]["INVOICE_INFONUMBER"].ToString();
            ls_patient_id = dt.Rows[0]["PATIENT_ID"].ToString();
            ls_pat_name = dt.Rows[0]["NAME"].ToString();
            ls_charge_type = dt.Rows[0]["CHARGE_TYPE"].ToString();
            ls_tj_flag = dt.Rows[0]["TJ_FLAG"].ToString();
            ls_return_name = dt.Rows[0]["RETURN_NAME"].ToString();
            ls_drug_return = dt.Rows[0]["DRUG_RETURN"].ToString();
            ls_his_unit_code = dt.Rows[0]["HIS_UNIT_CODE"].ToString();
            string is_clinic_no = dt.Rows[0]["CLINIC_NO"].ToString();
            string ls_visit_no = dt.Rows[0]["VISIT_NO"].ToString();
            ls_tranno = dt.Rows[0]["TRANNO"].ToString(); //桃谷 海鹚 12320 自助机等订单号 退费及对账用
            ls_app_source = dt.Rows[0]["APP_SOURCE"].ToString(); //APP 来源 如 桃谷 海鹚 健康卡  pos 东北国际是银联 
            ls_paymentmode = dt.Rows[0]["PAYMENTMODE"].ToString(); //APP支付方式码 ZFB 支付宝 WX微信 POS机
            Systemreferno = dt.Rows[0]["SYSTEMREFERNO"].ToString(); //东北国际银联退款参考号
            ls_amt = dt.Rows[0]["AMT"].ToString();
            oldoperatorno = dt.Rows[0]["OPERATOR_NO"].ToString(); //id
            yhjyly = dt.Rows[0]["yhjy_LY"].ToString(); //天桃老数据有值

            DialogResult dialogResult = XtraMessageBox.Show("是否针对病人姓名为< " + ls_pat_name + " >收据号为< " + rcpt_no + " >进行退费操作???", "温馨提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
            if (dialogResult == DialogResult.No)
            {
                completed = false;
                start_save = false;

                return;
            }

            // 产生退费信息
            ue_refund();

            if (!IsRcptReturn(rcpt_no))
            {
                XtraMessageBox.Show("该收据已经退费,不能重复退费！", "提示");
                return;
            }
            if (gs_capability.IndexOf("Z") < 0)
            {
                XtraMessageBox.Show("您没有退费权限，请查证！！", "提示");
                return;
            }

            if ((string.IsNullOrEmpty(ls_return_name)))
            {
                XtraMessageBox.Show("该收据没有签字,请您先签字后再退费", "提示");
                return;
            }
            string sql2 = "select count(*)sl  from outp_bill_items where rcpt_no='" + rcpt_no + "' and confirmed_operator is not null ";
            DataTable dt2 = new ServerPublicClient().GetDataBySql(sql2).Tables[0];
            if (!dt2.Rows[0]["SL"].ToString().Equals("0"))
            {
                XtraMessageBox.Show("该收据的费用已经由辅助科室确认！不允许退费，请联系相应科室取消确认！", "提示");
                return;
            }

            //string sql3 = "select count(*) sl  from drug_presc_master where rcpt_no='" + rcpt_no + "' and item_class='C'";
            string sql3 = "select count(*) sl  from drug_presc_master where rcpt_no='" + rcpt_no + "'";
            DataTable dt3 = new ServerPublicClient().GetDataBySql(sql3).Tables[0];
            if (!dt3.Rows[0]["SL"].ToString().Equals("0"))
            {
                if (string.IsNullOrEmpty(ls_return_name))
                {
                    XtraMessageBox.Show("该收据没有签字,请您先签字后再退费", "提示");
                    return;
                }
            }

            //全部退费
            if (part_refund != true)
            {

                string ls_drug = "";
                sql = " Select wm_concat((select dept_name from dept_dict where dept_code=drug_presc_master.dispensary )) dept_name From outp_order_desc, drug_presc_master Where  outp_order_desc.visit_date = drug_presc_master.presc_date  ";
                sql = sql + "And outp_order_desc.visit_no = drug_presc_master.presc_no And outp_order_desc.rcpt_no ='" + rcpt_no + "' And outp_order_desc.presc_indicator > 0 And drug_presc_master.flag = 1 and  his_unit_code='" + SystemParm.HisUnitCode + "'";
                DataTable dd = new ServerPublicClient().GetList(sql).Tables[0];
                if (dd.Rows.Count > 0)
                {
                    ls_drug = (dd.Rows[0]["dept_name"].ToString());
                }
                if (!string.IsNullOrEmpty(ls_drug))
                {
                    // XtraMessageBox.Show("该患者还存在药没有退的情况!请先退药!", "提示");
                    ls_err = ls_err + "该患者还存在药没有退的情况!请先退药+药局为：" + ls_drug;

                }

                //检查确认判断已做
                StringBuilder sb = new StringBuilder();
                string sql1 = "select count(*)sl into :li_exam_count from outp_bill_items where rcpt_no='" + rcpt_no + "' and item_class='D' and  his_unit_code='" + SystemParm.HisUnitCode + "' ";
                DataTable dt1 = new ServerPublicClient().GetDataBySql(sql1).Tables[0];
                if (!dt1.Rows[0]["SL"].ToString().Equals("0"))
                {
                    sb.Append("select exam_no, cancel_oper  from exam_master where exam_no in (select outp_orders_standard.appoint_no from  outp_orders_standard ");
                    sb.AppendFormat("where outp_orders_standard.rcpt_no = '{0}' and outp_orders_standard.his_unit_code='{1}') and his_unit_code='" + SystemParm.HisUnitCode + "'", rcpt_no, SystemParm.HisUnitCode);

                    DataTable exam = new ServerPublicClient().GetDataBySql(sb.ToString()).Tables[0];
                    if (exam.Rows.Count > 0)
                    {
                        if (string.IsNullOrEmpty(exam.Rows[0]["CANCEL_OPER"].ToString()))
                        {
                            //查询出需要退的检查项目
                            string ls_str = string.Format("select wm_concat((select dept_name from dept_dict where dept_code=a.performed_by and his_unit_code='{1}')) dept_name  from exam_master a where  a.rcpt_no='{0}' and his_unit_code='{1}'", rcpt_no, SystemParm.HisUnitCode); ;
                            DataTable dt_exam = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_str).Tables[0];
                            if (dt_exam.Rows.Count > 0)
                            {
                                string ls_dept = dt_exam.Rows[0][0].ToString();
                                if (!string.IsNullOrEmpty(ls_dept))
                                {
                                    ls_err = ls_err + "\r\n" + "该收据的检查项目已经由执行科室确认！不允许退费,涉及科室：" + dt_exam.Rows[0][0].ToString();
                                }
                            }
                            //XtraMessageBox.Show("该收据的检查项目已经由执行科室确认！不允许退费！", "提示");
                            //return;
                        }

                    }
                }

                if (!string.IsNullOrEmpty(ls_err))
                {
                    XtraMessageBox.Show(ls_err, "提示");
                    return;
                }

                //判断门诊护士站输液执行状态已执行的不让退费
                string strsql = string.Format("select * from  outp_orders_standard a where a.rcpt_no ='{0}' and a.execute_status ='0' and his_unit_code='{1}'", rcpt_no, SystemParm.HisUnitCode);
                DataTable dt_n = new NM_Service.NMService.ServerPublicClient().GetDataBySql(strsql).Tables[0];
                if (dt_n.Rows.Count > 0)
                {
                    XtraMessageBox.Show("护士已经执行不能退费！", "提示");
                    return;
                }
                //判断门诊护士站输液执行状态已执行的不让退费

                //已发药需要医生签字
                sql = " Select drug_presc_master.flag  From outp_order_desc, drug_presc_master Where  outp_order_desc.visit_date = drug_presc_master.presc_date  ";
                sql = sql + "And outp_order_desc.visit_no = drug_presc_master.presc_no And outp_order_desc.rcpt_no ='" + rcpt_no + "' and drug_presc_master.his_unit_code='" + SystemParm.HisUnitCode + "'";
                DataTable pre = new ServerPublicClient().GetDataBySql(sql).Tables[0];
                if (pre != null && pre.Rows.Count > 0)
                {
                    string tflag = pre.Rows[0][0].ToString();
                    if (!tflag.Equals("3"))
                    {
                        XtraMessageBox.Show("请先退药后再退费", "提示");
                        return;
                    }
                }



                // 同步医生站相关退费标记
                if (wf_save_doct() == -1)
                    return;

            }
            else  //部分退费  暂不处理
            {
                //整理数据
            }

            //备份记录 暂留
            if (ib_back == true) wf_back_set();

            ldec_costs = decimal.Parse(em_costs.Text);
            ldec_charges = decimal.Parse(em_charges.Text);

            //优惠字典 暂时看没有什么用 就不加了 需要就加

            //优惠字典 暂时看没有什么用 就不加了 需要就加

            ldec_costs = 0 - total_costs;
            ldec_charges = 0 - total_charges;


            //处理预交金
            if (wf_refund_prepayment(is_patientid, rcpt_no) < 0)
            {
                XtraMessageBox.Show("退还病人预交金失败！", "提示");
                start_save = false;
                return;
            }

            //保存开单记录
            for (int o = 0; o < dw_order_new.Rows.Count; o++)
            {
                string ls_visit_date = dw_order_new.Rows[o]["VISIT_DATE"].ToString();
                long ll_visit_no = long.Parse(dw_order_new.Rows[o]["VISIT_NO"].ToString());
                string os_patient_id = dw_order_new.Rows[o]["PATIENT_ID"].ToString();
                string os_ordered_by_dept = dw_order_new.Rows[o]["ORDERED_BY_DEPT"].ToString();
                string os_ordered_by_doctor = dw_order_new.Rows[o]["ORDERED_BY_DOCTOR"].ToString();
                string os_rcpt_no = dw_order_new.Rows[o]["RCPT_NO"].ToString();
                int li_presc_indicator = int.Parse(dw_order_new.Rows[o]["PRESC_INDICATOR"].ToString());
                string ls_presc_attr = dw_order_new.Rows[o]["PRESC_ATTR"].ToString();
                string ls_clinic_no = dw_order_new.Rows[o]["CLINIC_NO"].ToString();

                sql = "INSERT INTO OUTP_ORDER_DESC ( VISIT_DATE, VISIT_NO, PATIENT_ID, ORDERED_BY_DEPT, ORDERED_BY_DOCTOR, RCPT_NO, PRESC_INDICATOR, PRESC_ATTR, CLINIC_NO ) ";
                sql = sql + "values (to_date('" + ls_visit_date + "','yyyy-mm-dd hh24:mi:ss')," + ll_visit_no + ",'" + os_patient_id + "','" + os_ordered_by_dept + "','" + os_ordered_by_doctor + "','" + os_rcpt_no + "'," + li_presc_indicator + ",'" + ls_presc_attr + "','" + ls_clinic_no + "' )";
                idc.Add(sql, "开单记录保存失败");
            }


            //保存费用明细
            for (int i = 0; i < dw_bill_detail_new.Rows.Count; i++)
            {
                string ls_visit_date = dw_bill_detail_new.Rows[i]["VISIT_DATE"].ToString();
                long ll_visit_no = long.Parse(dw_bill_detail_new.Rows[i]["VISIT_NO"].ToString());
                string b_rcpt_no = dw_bill_detail_new.Rows[i]["RCPT_NO"].ToString();
                long ll_item_no = long.Parse(dw_bill_detail_new.Rows[i]["ITEM_NO"].ToString());
                string f_item_class = dw_bill_detail_new.Rows[i]["ITEM_CLASS"].ToString();
                string f_class_on_rcpt = dw_bill_detail_new.Rows[i]["CLASS_ON_RCPT"].ToString();
                string f_item_name = dw_bill_detail_new.Rows[i]["ITEM_NAME"].ToString();
                decimal f_amount = decimal.Parse(dw_bill_detail_new.Rows[i]["AMOUNT"].ToString());
                string f_units = dw_bill_detail_new.Rows[i]["UNITS"].ToString();
                string f_performed_by = dw_bill_detail_new.Rows[i]["PERFORMED_BY"].ToString();
                decimal f_costs = decimal.Parse(dw_bill_detail_new.Rows[i]["COSTS"].ToString());
                decimal f_charges = decimal.Parse(dw_bill_detail_new.Rows[i]["CHARGES"].ToString());
                string f_flag = dw_bill_detail_new.Rows[i]["FLAG"].ToString();
                string f_invoice_no = dw_bill_detail_new.Rows[i]["INVOICE_NO"].ToString();
                int f_repetition = 1;
                try
                {
                    if (dw_bill_detail_new.Rows[i]["REPETITION"] != null || dw_bill_detail_new.Rows[i]["REPETITION"] != DBNull.Value)
                    { f_repetition = int.Parse(dw_bill_detail_new.Rows[i]["REPETITION"].ToString()); }
                }
                catch
                {
                    f_repetition = 1;
                }

                string f_class_on_reckoning = dw_bill_detail_new.Rows[i]["CLASS_ON_RECKONING"].ToString();
                string f_subj_code = dw_bill_detail_new.Rows[i]["SUBJ_CODE"].ToString();
                string f_price_quotiety = dw_bill_detail_new.Rows[i]["PRICE_QUOTIETY"].ToString();
                decimal f_item_price = decimal.Parse(dw_bill_detail_new.Rows[i]["ITEM_PRICE"].ToString());
                string f_order_no = dw_bill_detail_new.Rows[i]["ORDER_NO"].ToString();
                string f_sub_order_no = dw_bill_detail_new.Rows[i]["SUB_ORDER_NO"].ToString();
                string f_item_code = dw_bill_detail_new.Rows[i]["ITEM_CODE"].ToString();
                string f_item_spec = dw_bill_detail_new.Rows[i]["ITEM_SPEC"].ToString();
                string f_oper_code = dw_bill_detail_new.Rows[i]["OPER_CODE"].ToString();
                string f_order_doctor = dw_bill_detail_new.Rows[i]["ORDER_DOCTOR"].ToString();
                string f_order_dept = dw_bill_detail_new.Rows[i]["ORDER_DEPT"].ToString();
                string f_clinic_item_name = dw_bill_detail_new.Rows[i]["CLINIC_ITEM_NAME"].ToString();
                string f_clinic_item_code = dw_bill_detail_new.Rows[i]["CLINIC_ITEM_CODE"].ToString();
                string f_outp_item_no = dw_bill_detail_new.Rows[i]["OUTP_ITEM_NO"].ToString();
                string f_outp_sub_no = dw_bill_detail_new.Rows[i]["OUTP_SUB_NO"].ToString();
                string f_outp_item_sub_no = dw_bill_detail_new.Rows[i]["OUTP_ITEM_SUB_NO"].ToString();
                string f_outp_serial_no = dw_bill_detail_new.Rows[i]["OUTP_SERIAL_NO"].ToString();
                string f_outp_visit_date = dw_bill_detail_new.Rows[i]["OUTP_VISIT_DATE"].ToString();
                string ls_outp_visit_date;
                if (string.IsNullOrEmpty(f_outp_visit_date))
                {
                    ls_outp_visit_date = "null";
                }
                else
                {
                    ls_outp_visit_date = "to_date('" + f_outp_visit_date + "','yyyy-mm-dd hh24:mi:ss')";
                }
                string f_outp_visit_no = dw_bill_detail_new.Rows[i]["OUTP_VISIT_NO"].ToString();
                string f_rcptgroupid = dw_bill_detail_new.Rows[i]["RCPTGROUPID"].ToString();
                string f_rcptgroup = dw_bill_detail_new.Rows[i]["RCPTGROUP"].ToString();
                string f_trade_price = dw_bill_detail_new.Rows[i]["TRADE_PRICE"].ToString();
                string strBATCH_NO = dw_bill_detail_new.Rows[i]["BATCH_NO"].ToString();
                string strBATCH_CODE = dw_bill_detail_new.Rows[i]["BATCH_CODE"].ToString();
                string strGUID = dw_bill_detail_new.Rows[i]["GUID"].ToString();


                sql = "insert into outp_bill_items ( visit_date, visit_no, rcpt_no, item_no, item_class, class_on_rcpt, item_name, amount, units, performed_by, costs, charges,";
                sql = sql + " flag, invoice_no, repetition, class_on_reckoning, subj_code, price_quotiety, item_price, order_no, sub_order_no, item_code, item_spec, oper_code,ORDER_DOCTOR,ORDER_DEPT, CLINIC_ITEM_NAME,";
                sql = sql + "clinic_item_code,outp_item_no,outp_sub_no,outp_item_sub_no,outp_serial_no,outp_visit_date,outp_visit_no,rcptgroupid,rcptgroup,  ";
                sql = sql + " TRADE_PRICE ,BATCH_NO ,BATCH_CODE,GUID,his_unit_code )";
                sql = sql + "values ( to_date('" + ls_visit_date + "','yyyy-mm-dd hh24:mi:ss')," + ll_visit_no + ",'" + b_rcpt_no + "'," + ll_item_no + ",'" + f_item_class + "','" + f_class_on_rcpt + "','" + f_item_name + "'," + f_amount + ",'" + f_units + "','" + f_performed_by + "'," + f_costs + "," + f_charges + " ,";
                sql = sql + " '" + f_flag + "','" + f_invoice_no + "','" + f_repetition + "','" + f_class_on_reckoning + "','" + f_subj_code + "','" + f_price_quotiety + "','" + f_item_price + "','" + f_order_no + "','" + f_sub_order_no + "','" + f_item_code + "','" + f_item_spec + "','" + f_oper_code + "','" + f_order_doctor + "','" + f_order_dept + "','" + f_clinic_item_name + "',";
                sql = sql + "'" + f_clinic_item_code + "','" + f_outp_item_no + "','" + f_outp_sub_no + "','" + f_outp_item_sub_no + "','" + f_outp_serial_no + "'," + ls_outp_visit_date + ",'" + f_outp_visit_no + "','" + f_rcptgroupid + "','" + f_rcptgroup + "',";
                sql = sql + "'" + f_trade_price + "','" + strBATCH_NO + "','" + strBATCH_CODE + "','" + strGUID + "', '" + SystemParm.HisUnitCode + "')";
                idc.Add(sql, "费用明细保存失败！");
            }




            //打印帐户卡
            string ls_print_card = PlatCommon.SysBase.SystemParm.GetParameterValue("PRINT_CARD", "OBILLING", this.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
            if (ls_print_card.ToLower().Equals("yes"))
            {
                print_card = true;
            }
            else
            {
                print_card = false;
            }


            string ls_special_refund = "";
            if (barinsur.EditValue == "1")
            {
                ls_special_refund = "特殊";
            }
            //修改老收据
            if (ib_back != true)
            {
                sql = "Update outp_rcpt_master Set charge_indicator = 2,  refunded_rcpt_no ='" + rcpt_refund_no + "', bz ='" + ls_special_refund + "' Where rcpt_no ='" + rcpt_no + "' and his_unit_code='" + SystemParm.HisUnitCode + "'";
            }
            else
            {
                sql = "Update outp_rcpt_master_back Set charge_indicator = 2,  refunded_rcpt_no ='" + rcpt_refund_no + "'  Where rcpt_no ='" + rcpt_no + "'";
            }
            idc.Add(sql, "在退费处理时修改老收据记录失败");

            //删除待发药处方
            if (wf_del_presc(rcpt_no) < 0)
            {
                XtraMessageBox.Show("删除待发药处方失败1！", "提示");
                start_save = false;
                return;
            }

            //通用接口调用
            if (gs_insurance_chargetype.IndexOf(charge_type) >= 0 && !barinsur.EditValue.Equals("1"))
            {
                string strsql = string.Format("select clinic_master.*, '' as JYLX,''BARINSURCODE from clinic_master where patient_id = '{0}'and his_unit_code='{1}'", is_patientid, SystemParm.HisUnitCode);
                DataSet ds = new ServerPublicClient().GetDataBySql(strsql);
                ds.Tables[0].Rows[0]["DIRECTION"] = is_pwd;


                ////如果未读医保卡，自动触发读卡  zhj 屏蔽掉默认读医保卡
                //if (string.IsNullOrEmpty(labelControl5.Text))
                //{
                //    barInsuranceCard.PerformClick();
                //}
                ds.Tables[0].Rows[0]["BARINSURCODE"] = this.barinsurtype.EditValue.Equals("01");
                if (MedicalInterface.MedicalBusinessHandle("008", is_patientid, "", charge_type, PlatCommon.SysBase.SystemParm.LoginUser.ID, rcpt_no, "", ds) >= 0)
                {
                    // MessageBox.Show("门诊结算撤销成功", "提示");
                }
                else
                {
                    MessageBox.Show("门诊结算撤销失败,请查看日志了解失败原因", "提示");
                    return;
                }
            }
            // 判断海鹚
            decimal damt = 0;
            if (ls_app_source.Equals("海鹚"))
            {
                try
                {
                    string sqlhc = "select * from billservice.tran_rec_tjtg where tran_no = '" + ls_tranno + "'";
                    DataSet dshc = new ServerPublicClient().GetList(sqlhc);
                    if (dshc != null)
                    {
                        if (dshc.Tables[0].Rows.Count > 0)
                        {
                            string tamt = dshc.Tables[0].Rows[0]["AMT"].ToString();
                            if (!string.IsNullOrEmpty(tamt))
                            {
                                damt = decimal.Parse(tamt); //只做判断用,证明这笔流水号存在缴费金额,退还是要退his支付方式表的
                            }

                        }

                    }
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show("查询海鹚交易流水失败" + ex.Message, "错误提示");
                    return;
                }
            }
            string resultCode = string.Empty; //海鹚返回状态码 0 成功
            string resultDesc = string.Empty; //当返回状态码不成功时，该字段必须返回
            string refundStatus = string.Empty;//0：退款成功 -1：退款失败 1：退款处理中
            string refundStatusDes = string.Empty; //退款状态描述
            string hisRefundOrdNum = string.Empty; //医院退款订单号 
            string agtRefundOrdNum = string.Empty;//支付机构退款流水号 退款成功时返回的退款流水号，支付宝退款流水号与缴费流水号一致
            string refundAmout = string.Empty;//单位：分
            string refundTime = string.Empty; //退款时间
            //判断海鹚结束
            //保存支付方式记录
            for (int p = 0; p < dw_payments_money_new.Rows.Count; p++)
            {
                string ls_money_type = dw_payments_money_new.Rows[p]["MONEY_TYPE"].ToString();
                string ps_rcpt_no = dw_payments_money_new.Rows[p]["RCPT_NO"].ToString();
                decimal d_payment_amount = decimal.Parse(dw_payments_money_new.Rows[p]["PAYMENT_AMOUNT"].ToString());
                decimal d_refunded_amount = decimal.Parse(dw_payments_money_new.Rows[p]["REFUNDED_AMOUNT"].ToString());
                long ll_payment_no = long.Parse(dw_payments_money_new.Rows[p]["PAYMENT_NO"].ToString());
                #region 第三方公共接口退费
                List<string> is_rcpt_all = new List<string>();
                is_rcpt_all.Add(rcpt_no);
                if (isOpenThirdPay.Equals("1"))
                {
                    string errtext = "";
                    if (selPayMode.Equals("01"))
                    {
                        string agtOrdNum = string.Empty;
                        if (ls_app_source.Equals("微信") || ls_app_source.Equals("支付宝"))
                        {
                            string sqltg = string.Format("select  AMT,backcardno,banktradeno,systemreferno,PAYMENTMODE,TRANNO, wca_flag	from outp_rcpt_master  where rcpt_no='{0}' and his_unit_code='{1}'", rcpt_no, SystemParm.HisUnitCode);
                            DataTable dttg = new ServerPublicClient().GetDataBySql(sqltg).Tables[0];
                            if (dttg != null && dttg.Rows.Count > 0)
                            {
                                decimal trefundMoney = 0;
                                string tamt = "0";
                                try
                                { tamt = dttg.Rows[0]["AMT"].ToString(); }
                                catch
                                { tamt = "0"; }
                                if (string.IsNullOrEmpty(tamt))
                                {
                                    tamt = "0";
                                }
                                trefundMoney = decimal.Parse(tamt);
                                decimal payAmout = Math.Abs(d_payment_amount);
                                PAY_IN refIn = new PAY_IN();
                                WcaAliRefund_OUT_MODEL refOut = new WcaAliRefund_OUT_MODEL();
                                refIn.flag = selPayMode;
                                refIn.rcpt_all = is_rcpt_all;
                                refIn.patient_id = is_patientid;
                                refIn.name = sle_name.Text;
                                refIn.oldTradeNo = dttg.Rows[0]["TRANNO"].ToString();
                                refIn.ldc_money = payAmout;
                                refIn.refund_fee = trefundMoney;
                                refIn.scene_type = "退费";
                                refIn.opcode = SystemParm.LoginUser.ID;
                                refIn.payType = "窗口";
                                refIn.pay_mode = dttg.Rows[0]["PAYMENTMODE"].ToString();
                                int liret = PayModeComInterface.RefundInterface<WcaAliRefund_OUT_MODEL>(refIn, ref refOut);
                                if (liret != 0)
                                {
                                    XtraMessageBox.Show("调用微信支付宝退费失败请联系信息科！,错误原因:" + errtext, "提示错误");
                                }
                            }
                        }
                    }
                    if (selPayMode.Equals("04"))
                    {
                        if (ls_app_source.Equals("微信扫码") || ls_app_source.Equals("支付宝码") || ls_app_source.Equals("银联扫码") || ls_app_source.Equals("微信") || ls_app_source.Equals("支付宝") || ls_app_source.Equals("银联") || ls_app_source.Equals("信用卡")|| ls_app_source.Equals("BANKPOS") || ls_app_source.Equals("数字人民币"))
                        {
                            //string sqlstr = string.Format("select  AMT,backcardno,banktradeno,systemreferno,PAYMENTMODE,patient_id,visit_no	from outp_rcpt_master  where rcpt_no='{0}' and his_unit_code='{1}'", rcpt_no, SystemParm.HisUnitCode);
                            //DataTable dttemp = new ServerPublicClient().GetDataBySql(sqlstr).Tables[0];
                            //if (dttemp == null || dttemp.Rows.Count < 1)
                            //{
                            //    MessageBox.Show("取04合川荣军医院接口支付方式失败", "提示");
                            //    return;
                            //}
                            //string oldRefNo = dttemp.Rows[0]["SYSTEMREFERNO"].ToString();
                            PAY_IN pIn = new PAY_IN();
                            BankPos_OUT_MODEL pOut = new BankPos_OUT_MODEL();
                            pIn.flag = selPayMode;
                            pIn.patient_id = is_patientid;
                            pIn.visitNo = ls_visit_no;
                            pIn.kno = "001";// 款台号
                            pIn.opcode = SystemParm.LoginUser.ID;//BankPos操作员号
                            pIn.trans_type = "R"; //交易类型标志; 为 D，表示是负数（取消交易）；为 R，表示是负数（退货交易）
                            pIn.ldc_money = Math.Abs(d_payment_amount);
                            pIn.pay_mode = ls_paymentmode;
                            pIn.pay_way = ls_app_source;
                            pIn.oldRefNo = Systemreferno;//银行卡以参考号为唯一标识，扫码以扫码支付交易订单号为唯一标识
                            if (!string.IsNullOrEmpty(Systemreferno))
                            {
                                pIn.oldScanOrderId = Systemreferno;//银行卡以参考号为唯一标识，扫码以扫码支付交易订单号为唯一标识
                            }
                            int li_ret = PayModeComInterface.RefundInterface<BankPos_OUT_MODEL>(pIn, ref pOut);
                            if (li_ret != 0)
                            {
                                XtraMessageBox.Show("合川荣军医院接口撤销支付失败,错误原因:" + pOut.errorMsg, "提示错误");

                            }
                        }

                    }
                }

                #endregion
                #region 自助机退费开始
                if ((ls_app_source.Equals("自助机") || string.IsNullOrEmpty(ls_app_source)) && (ls_money_type.Equals("新微信") || ls_money_type.Equals("新支付宝") || ls_money_type.Equals("新银联")) && (oldoperatorno.Equals("5555")))
                {
                    sql = $@"select to_char(a.visit_date,'yyyy-MM-dd') TradeDate,a.transaction_id UnionBillNo from outp_rcpt_master a where a.rcpt_no='{rcpt_no}'";
                    dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql_Tran(sql).Tables[0];
                    if (dt == null || dt.Rows.Count < 1)
                    {
                        XtraMessageBox.Show("没有查到此就诊记录", "提示错误"); 
                        return ;
                    }
                    sql = $@"select sum(nvl(a.payment_amount-a.refunded_amount,0)) PayBackMoney from outp_payments_money a where a.rcpt_no = '{rcpt_no}' and a.money_type in ('新微信','新支付宝','新银联')";
                    dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql_Tran(sql).Tables[0];
                    if (dt == null || dt.Rows.Count < 1)
                    {
                        XtraMessageBox.Show("没有查到此就诊记录的自助机支付记录", "提示错误");
                        return ;
                    }
                    PAY_IN pIn = new PAY_IN();
                    ExcutePayBack_OUT_MODEL pOut = new ExcutePayBack_OUT_MODEL();
                    pIn.flag = "05";
                    pIn.payType = "2";//PayBackMethod 退款方式 2扫码支付退款
                    pIn.oldBillNo = dt.Rows[0]["UnionBillNo"].ToString();//UnionBillNo 账单号,支付到His时传递的银行账单号（交易流水号）
                    pIn.refund_fee = decimal.Parse(dt.Rows[0]["PayBackMoney"].ToString());//PayBackMoney 退款金额 单位：元
                    pIn.oldTradeDate = dt.Rows[0]["TradeDate"].ToString();//交易日期 传入His交易的日期，格式：yyyyy-MM-dd
                    pIn.trans_type = "2";// PayBackType 退款类型 1全部退款；2部分退款
                    pIn.scene_type = "门诊退费";//PayBackRemark	退款备注说明
                    pIn.patient_id =ls_patient_id ;
                    pIn.visitNo = ls_visit_no;
                    pIn.rcpt_no =rcpt_no;
                    int li_ret = PayModeComInterface.RefundInterface<ExcutePayBack_OUT_MODEL>(pIn, ref pOut);
                    if (li_ret != 0)
                    {
                        XtraMessageBox.Show("合川荣军医院自助机接口撤销支付失败,错误原因:" + pOut.ErrorMsg, "提示错误");
                        return;
                    }                   
                }
           
                #endregion
                if (ls_money_type.IndexOf("12320") >= 0)
                {
                    #region 20200915--桓仁12320退款接口--mchi
                    try
                    {
                        HIS12320.HIS gws_his12320 = new HIS12320.HIS();
                        ln12320.WsModel gws_ln12320 = new ln12320.WsModel();
                        NM_Service.NMService.ServerPublicClient spc12320 = new ServerPublicClient();
                        string ls_xml1 = "", out_result1 = "", ls_xml2 = "", out_msg = "", out_result2 = "";
                        string ls_hosid = "", ls_order_id = "", tHOSP_SEQUENCE = "", REFUND_DATE = "", ls_hosp_order_id = "";
                        string tPAY_CHANNEL_ID = "", tuser_id = "";
                        string ls_xxtd1 = "RefundPay";
                        string ls_info1 = "缴费退款接口";
                        string ls_xxtd2 = "QueryPayRefund";
                        string ls_info2 = "缴费退款查询接口";
                        string ll_money_12320 = Math.Abs(d_payment_amount).ToString();
                        string ls_sql = string.Format("select HEALTH_PLATFORM_12320_CODE  from hospital_config where  rownum =1 and his_unit_code='{0}'", SystemParm.HisUnitCode);
                        DataTable dt_hc = spc12320.GetDataBySql(ls_sql).Tables[0];
                        if (dt_hc.Rows.Count <= 0)
                        {
                            XtraMessageBox.Show("取12320医院信息失败！", "提示");
                            return;
                        }
                        ls_hosid = dt_hc.Rows[0][0].ToString();

                        ls_sql = " select id,order_id,HOSP_SEQUENCE,PAY_CHANNEL_ID,USER_ID ,PAY_TOTAL_FEE,PAY_BEHOOVE_FEE from billservicehealthy.payorder_12320 where   FH_RECEIPT_ID ='" + rcpt_no + "'";//*****没有找到该表
                        DataTable dt_o = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_sql).Tables[0];
                        if (dt_o.Rows.Count <= 0)
                        {
                            XtraMessageBox.Show("获取12320订单信息失败", "提示");
                            return;
                        }
                        ls_order_id = dt_o.Rows[0]["order_id"].ToString();
                        ls_hosp_order_id = dt_o.Rows[0]["id"].ToString();
                        tHOSP_SEQUENCE = dt_o.Rows[0]["HOSP_SEQUENCE"].ToString();
                        tPAY_CHANNEL_ID = dt_o.Rows[0]["PAY_CHANNEL_ID"].ToString();
                        tuser_id = dt_o.Rows[0]["user_id"].ToString();
                        string TOTAL_FEE = dt_o.Rows[0]["PAY_TOTAL_FEE"].ToString();
                        string REFUND_FEE = dt_o.Rows[0]["PAY_BEHOOVE_FEE"].ToString();
                        if (string.IsNullOrEmpty(ls_order_id))
                        {
                            XtraMessageBox.Show("获取12320订单号失败,payorder_12320是否存储正确？", "提示");
                            return;
                        }
                        REFUND_DATE = spc12320.GetSysDate().ToString("yyyy-MM-dd HH:mm:ss");
                        string REFUND_TYPE = "1"; //是否需要平台进行退款：0-否 1-是
                        if (string.IsNullOrEmpty(TOTAL_FEE) || string.IsNullOrEmpty(REFUND_FEE))
                        {
                            XtraMessageBox.Show("获取12320缴费总金额失败", "提示");
                            return;
                        }
                        ls_xml1 = "<REQ>";
                        ls_xml1 = ls_xml1 + "<HOS_ID><![CDATA[" + ls_hosid + "]]></HOS_ID>";
                        ls_xml1 = ls_xml1 + "<ORDER_ID><![CDATA[" + ls_order_id + "]]></ORDER_ID>";
                        ls_xml1 = ls_xml1 + "<HOSP_SEQUENCE><![CDATA[" + tHOSP_SEQUENCE + "]]></HOSP_SEQUENCE>";
                        ls_xml1 = ls_xml1 + "<HOSP_REFUND_ID><![CDATA[" + rcpt_refund_no + "]]></HOSP_REFUND_ID>";
                        ls_xml1 = ls_xml1 + "<REFUND_TYPE><![CDATA[" + REFUND_TYPE + "]]></REFUND_TYPE>";
                        ls_xml1 = ls_xml1 + "<REFUND_DATE><![CDATA[" + REFUND_DATE + "]]></REFUND_DATE>";
                        ls_xml1 = ls_xml1 + "<TOTAL_FEE><![CDATA[" + TOTAL_FEE + "]]></TOTAL_FEE>";
                        ls_xml1 = ls_xml1 + "<REFUND_FEE><![CDATA[" + REFUND_FEE + "]]></REFUND_FEE>";
                        ls_xml1 = ls_xml1 + "<REFUND_REMARK><![CDATA[]]></REFUND_REMARK>";
                        ls_xml1 = ls_xml1 + "</REQ>";
                        //调用HIS发布的webservice进行加密
                        if (string.IsNullOrEmpty(tuser_id))
                        {
                            tuser_id = "LN12320";
                        }
                        if (tuser_id.Equals("LN12320"))
                        {
                            out_result1 = gws_his12320.EncryptHis("6001", ls_xml1);
                        }
                        else
                        {
                            out_result1 = gws_his12320.EncryptHis_xin(tuser_id, "6001", ls_xml1);
                        }
                        ls_xml2 = gws_his12320.PayService(out_result1);//推送到12320

                        //调用his解密开始
                        string ls_xml3 = "", out_result3 = "", ls_xml4 = "", out_msg1 = "", tREFUND_STATUS = "", tREFUND_ID = "";
                        string v_zj_id = string.Empty;
                        int lsreturn = 0;
                        string out_encrypted = string.Empty;
                        string sqlc = "select sys_guid()  from dual";
                        //  v_zj_id = spc12320.GetSingleValue(sqlc).ToString();
                        DataTable dt123 = spc12320.GetDataBySql(sqlc).Tables[0];
                        Byte[] bstrs = (Byte[])(dt123.Rows[0][0]);
                        string bstr = "";
                        foreach (byte bt in bstrs)
                        {
                            bstr += bt.ToString("X2");
                        }
                        v_zj_id = bstr;
                        lsreturn = f_decryptxml(ls_xml2, ref out_msg, ref out_result2);
                        Dictionary<string, string> idclog = new Dictionary<string, string>();
                        string tFLAG = "1";
                        if (lsreturn != 0)
                        {
                            tFLAG = "-1";
                        }
                        else
                        {
                            tFLAG = "1";
                        }
                        string ls_xml2r = ls_xml2.Substring(ls_xml2.IndexOf(">"), ls_xml2.Length - ls_xml2.IndexOf(">") - 1);
                        string ls_insert = "INSERT INTO COMM.LN_12320_PBLOG( PATIENT_ID, CZSJ, FWID, HIS_SERIAL, INPUT_LOG, OUTPUT, FLAG, FUN, INFO, ID)";
                        ls_insert += "  VALUES ( '" + ls_order_id + "',to_date('" + REFUND_DATE + "','yyyy-mm-dd hh24:mi:ss'),'6001', '','" + ls_xml1 + "','" + ls_xml2r + "','" + tFLAG + "','" + ls_xxtd1 + "','" + ls_info1 + "','" + v_zj_id + "') ";
                        idclog.Add(ls_insert, "插入插入平台上传日志表数据失败！");
                        string ls_in = spc12320.SaveTable(idclog);
                        if (!string.IsNullOrEmpty(ls_in))
                        {
                            XtraMessageBox.Show(ls_in, "提示");
                        }
                        if (lsreturn != 0)
                        {
                            XtraMessageBox.Show(out_msg, "提示");
                            return;
                        }

                        ls_xml3 = "<REQ>";
                        ls_xml3 = ls_xml3 + "<HOS_ID><![CDATA[" + ls_hosid + "]]></HOS_ID>";
                        ls_xml3 = ls_xml3 + "<ORDER_ID><![CDATA[" + ls_order_id + "]]></ORDER_ID>";
                        ls_xml3 = ls_xml3 + "<REFUND_ID><![CDATA[]]></REFUND_ID>";
                        ls_xml3 = ls_xml3 + "<HOSP_REFUND_ID><![CDATA[" + rcpt_no + "]]></HOSP_REFUND_ID>";
                        ls_xml3 = ls_xml3 + "</REQ>";
                        if (tuser_id.Equals("LN12320"))
                        {
                            out_result3 = gws_his12320.EncryptHis("6002", ls_xml1);
                        }
                        else
                        {
                            out_result3 = gws_his12320.EncryptHis_xin(tuser_id, "6002", ls_xml1);
                        }
                        ls_xml4 = gws_ln12320.PayService(out_result3);//推送到12320

                        v_zj_id = spc12320.GetSingleValue(sqlc).ToString();
                        lsreturn = f_decryptxml(ls_xml4, ref out_msg1, ref out_result3);
                        if (lsreturn != 0)
                            tFLAG = "-1";
                        else
                            tFLAG = "1";
                        string ls_xml4r = ls_xml4.Substring(ls_xml4.IndexOf(">"), ls_xml4.Length - ls_xml4.IndexOf(">") - 1);
                        idclog.Clear(); //清除，日志已经单独保存
                        ls_insert = "INSERT INTO COMM.LN_12320_PBLOG( PATIENT_ID,  CZSJ, FWID,HIS_SERIAL,INPUT_LOG,OUTPUT,FLAG,FUN,INFO,ID)";
                        ls_insert += "  VALUES ( '" + ls_order_id + "',to_date('" + REFUND_DATE + "','yyyy-mm-dd hh24:mi:ss'),'6002', '','" + ls_xml3 + "','" + ls_xml4r + "','" + tFLAG + "','" + ls_xxtd2 + "','" + ls_info2 + "','" + v_zj_id + "') ";
                        idclog.Add(ls_insert, "插入插入平台上传日志表数据失败！");
                        ls_in = spc12320.SaveTable(idclog);
                        if (!string.IsNullOrEmpty(ls_in))
                        {
                            XtraMessageBox.Show(ls_in, "提示");
                        }
                        if (lsreturn != 0)
                        {
                            XtraMessageBox.Show(out_msg1, "提示");
                            return;
                        }
                        int ll_start_06, ll_end_06;
                        string RETURN_STATUS;
                        if (out_result3.IndexOf("<REFUND_STATUS>") >= 0)
                        {
                            ll_start_06 = out_result3.IndexOf("<REFUND_STATUS>");
                            ll_end_06 = out_result3.IndexOf("</REFUND_STATUS>");
                            RETURN_STATUS = out_result3.Substring(ll_start_06 + 15, ll_end_06 - ll_start_06 - 15);
                        }
                        else
                        {
                            ll_start_06 = out_result3.IndexOf("<REFUND_STATUS><![CDATA[");
                            ll_end_06 = out_result3.IndexOf("]]></REFUND_STATUS>");
                            RETURN_STATUS = out_result3.Substring(ll_start_06 + 24, ll_end_06 - ll_start_06 - 24);
                        }
                        if (RETURN_STATUS.Equals("2"))//1-退款成功 2-退款失败
                        {
                            XtraMessageBox.Show("挂号退款查询状态是退款失败" + RETURN_STATUS, "提示6002");
                            return;
                        }
                        //int ll_start_code = out_result3.IndexOf("<REFUND_ID><![CDATA[");
                        //int ll_end_code = out_result3.IndexOf("]]></REFUND_ID>");
                        //tREFUND_ID = out_result3.Substring(ll_start_code + 20, ll_end_code - ll_start_code - 20);

                        if (out_result3.IndexOf("<REFUND_ID>") >= 0)
                        {
                            ll_start_06 = out_result3.IndexOf("<REFUND_ID>");
                            ll_end_06 = out_result3.IndexOf("</REFUND_ID>");
                            tREFUND_ID = out_result3.Substring(ll_start_06 + 11, ll_end_06 - ll_start_06 - 11);
                        }
                        else
                        {
                            if (string.IsNullOrEmpty(tREFUND_ID)) tREFUND_ID = ls_order_id;
                            //ll_start_06 = out_result3.IndexOf("<REFUND_ID><![CDATA[");
                            //ll_end_06 = out_result3.IndexOf("]]></REFUND_ID>");
                            //tREFUND_ID = out_result3.Substring(ll_start_06 + 20, ll_end_06 - ll_start_06 - 20);
                        }

                        idclog.Clear(); //清除，日志已经单独保存

                        StringBuilder sblog = new StringBuilder();
                        sblog.Append("insert into  BILLSERVICEHEALTHY.RefundPay_12320 ");
                        sblog.Append("   (  ID    ,HOS_ID , ORDER_ID, HOSP_SEQUENCE , HOSP_REFUND_ID ,  REFUND_TYPE ,");
                        sblog.Append("	   REFUND_DATE ,  TOTAL_FEE  , REFUND_FEE , REFUND_REMARK ,REFUND_ID, CANCEL_DATE) ");
                        sblog.Append("    values ('" + ls_hosp_order_id + "','" + ls_hosid + "','" + ls_order_id + "','" + tHOSP_SEQUENCE + "',");
                        sblog.Append("'" + rcpt_refund_no + "','" + REFUND_TYPE + "','" + REFUND_DATE + "','" + TOTAL_FEE + "', ");
                        sblog.Append("'" + REFUND_FEE + "','','" + tREFUND_ID + "', ");
                        sblog.Append("to_date('" + REFUND_DATE + "','yyyy-mm-dd hh24:mi:ss'))");
                        idclog.Add(sblog.ToString(), "记录12320退号信息时,REFUND_12320数据库出错!,平台已经退款成功,请确定");
                        ls_in = spc12320.SaveTable(idclog);
                        if (!string.IsNullOrEmpty(ls_in))
                        {
                            XtraMessageBox.Show(ls_in, "提示");
                        }
                    }
                    catch (Exception ex)
                    {
                        XtraMessageBox.Show("12320退费失败！" + ex.Message, "提示");
                        return;
                    }
                    #endregion
                }

                sql = "insert into outp_payments_money ( money_type, rcpt_no, payment_amount, refunded_amount, payment_no )";
                sql = sql + " values( '" + ls_money_type + "','" + ps_rcpt_no + "'," + d_payment_amount + "," + d_refunded_amount + "," + ll_payment_no + ")";
                idc.Add(sql, "支付方式保存失败！");
            }

            if (string.IsNullOrEmpty(ls_visit_no))
            {
                ls_visit_no = "null";
            }
            //产生退费收据
            if (string.IsNullOrEmpty(refundTime)) // 退费时间refundTime = "to_date('" + refundTime + "','yyyy-mm-dd hh24:mi:ss')";
            {
                if (ib_is_medcard == true)
                {
                    sql = "Insert Into outp_rcpt_master( rcpt_no, patient_id, name, name_phonetic, Identity, charge_type,";
                    sql = sql + "unit_in_contract, visit_date, total_costs, total_charges, operator_no,";
                    sql = sql + "refunded_rcpt_no, charge_indicator, acct_no, card_flag, insurance_no, insurance_type,HIS_UNIT_CODE,visit_no,clinic_no,TRANNO,APP_SOURCE,PAYMENTMODE )";
                    sql = sql + "Values ( '" + rcpt_refund_no + "','" + is_patientid + "','" + Name + "','" + name_phonetic + "','" + identity + "','" + charge_type + "',";
                    sql = sql + "'" + contract_code + "',to_date('" + idt_visitdate.ToString() + "','yyyy-mm-dd hh24:mi:ss') , " + ldec_costs + ", " + ldec_charges + ", '" + PlatCommon.SysBase.SystemParm.LoginUser.ID + "',";
                    sql = sql + "'" + "" + "',2,'" + "" + "','Y','" + insurance_no + "','" + insurance_type + "','" + ls_his_unit_code + "'," + ls_visit_no + ",'" + is_clinic_no + "','" + agtRefundOrdNum + "','" + ls_app_source + "','" + ls_paymentmode + "')";

                }
                else
                {
                    sql = "Insert Into outp_rcpt_master( rcpt_no, patient_id, name, name_phonetic, Identity, charge_type,";
                    sql = sql + "unit_in_contract, visit_date, total_costs, total_charges, operator_no,";
                    sql = sql + "refunded_rcpt_no, charge_indicator, acct_no,  insurance_no, insurance_type,HIS_UNIT_CODE ,visit_no,clinic_no,TRANNO,APP_SOURCE,PAYMENTMODE )";
                    sql = sql + "Values ( '" + rcpt_refund_no + "','" + is_patientid + "','" + Name + "','" + name_phonetic + "','" + identity + "','" + charge_type + "',";
                    sql = sql + "'" + contract_code + "',to_date('" + idt_visitdate.ToString() + "','yyyy-mm-dd hh24:mi:ss') , " + ldec_costs + ", " + ldec_charges + ", '" + PlatCommon.SysBase.SystemParm.LoginUser.ID + "',";
                    sql = sql + "'" + "" + "',2,'" + "" + "','" + insurance_no + "','" + insurance_type + "','" + ls_his_unit_code + "'," + ls_visit_no + ",'" + is_clinic_no + "','" + agtRefundOrdNum + "','" + ls_app_source + "','" + ls_paymentmode + "')";
                }
                idc.Add(sql, "在退费处理时产生退费收据记录失败！");
            }
            else
            {
                if (ib_is_medcard == true)
                {
                    sql = "Insert Into outp_rcpt_master( rcpt_no, patient_id, name, name_phonetic, Identity, charge_type,";
                    sql = sql + "unit_in_contract, visit_date, total_costs, total_charges, operator_no,";
                    sql = sql + "refunded_rcpt_no, charge_indicator, acct_no, card_flag, insurance_no, insurance_type,HIS_UNIT_CODE,visit_no,clinic_no,TRANNO,APP_SOURCE,PAYMENTMODE,BANK_TRANS_DATE )";
                    sql = sql + "Values ( '" + rcpt_refund_no + "','" + is_patientid + "','" + Name + "','" + name_phonetic + "','" + identity + "','" + charge_type + "',";
                    sql = sql + "'" + contract_code + "',to_date('" + idt_visitdate.ToString() + "','yyyy-mm-dd hh24:mi:ss') , " + ldec_costs + ", " + ldec_charges + ", '" + PlatCommon.SysBase.SystemParm.LoginUser.ID + "',";
                    sql = sql + "'" + "" + "',2,'" + "" + "','Y','" + insurance_no + "','" + insurance_type + "','" + ls_his_unit_code + "'," + ls_visit_no + ",'" + is_clinic_no + "','" + agtRefundOrdNum + "','" + ls_app_source + "','" + ls_paymentmode + "'," + refundTime + ")";

                }
                else
                {
                    sql = "Insert Into outp_rcpt_master( rcpt_no, patient_id, name, name_phonetic, Identity, charge_type,";
                    sql = sql + "unit_in_contract, visit_date, total_costs, total_charges, operator_no,";
                    sql = sql + "refunded_rcpt_no, charge_indicator, acct_no,  insurance_no, insurance_type,HIS_UNIT_CODE ,visit_no,clinic_no,TRANNO,APP_SOURCE,PAYMENTMODE,BANK_TRANS_DATE )";
                    sql = sql + "Values ( '" + rcpt_refund_no + "','" + is_patientid + "','" + Name + "','" + name_phonetic + "','" + identity + "','" + charge_type + "',";
                    sql = sql + "'" + contract_code + "',to_date('" + idt_visitdate.ToString() + "','yyyy-mm-dd hh24:mi:ss') , " + ldec_costs + ", " + ldec_charges + ", '" + PlatCommon.SysBase.SystemParm.LoginUser.ID + "',";
                    sql = sql + "'" + "" + "',2,'" + "" + "','" + insurance_no + "','" + insurance_type + "','" + ls_his_unit_code + "'," + ls_visit_no + ",'" + is_clinic_no + "','" + agtRefundOrdNum + "','" + ls_app_source + "','" + ls_paymentmode + "'," + refundTime + ")";
                }
                idc.Add(sql, "在退费处理时产生退费收据记录失败！");
            }

            ///军队医改2022
            string sqlArmy = "select account_status from (select t.*,row_number() over(partition by patient_Id order by update_date_time desc) rn from insurance_accounts t where patient_Id = '" + is_patientid + "' and update_date_time is not null) where rn = 1 and rownum = 1";//是否保障0是保障
            string account_status = new NM_Service.NMService.ServerPublicClient().GetSingleValue(sqlArmy);
            string count = new NM_Service.NMService.ServerPublicClient().GetSingleValue("select count(1) from ARMY_SECURITY_RCPT where rcpt_no = '" + rcpt_no + "' ");
            try
            {
                if (!string.IsNullOrEmpty(account_status) && account_status.Equals("0"))
                {
                    /////////////军改退费发票插入
                    string sqlTf = "select * from insurance.outp_save_ptyb where rcpt_no = '" + rcpt_no + "'";//获取一包中间表
                    DataTable dtInsur = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sqlTf).Tables[0];
                    if (dtInsur.Rows.Count > 0 && int.Parse(count) > 0)
                    {
                        decimal fund_pay_sumamt_refund = 0 - decimal.Parse(dtInsur.Rows[0]["OUT_SETLINFO_FUND_PAY_SUMAMT"].ToString());//退医保统筹
                        decimal pay_first_fee_refund = 0 - decimal.Parse(dtInsur.Rows[0]["OUT_SETLINFO_ACT_PAY_DEDC"].ToString());//退医保结算起付线金额
                        decimal preselfpay_amt_refund = 0 - decimal.Parse(dtInsur.Rows[0]["OUT_SETLINFO_PRESELFPAY_AMT"].ToString());//退医保结算乙类自付金额
                        decimal fulamt_ownpay_amt_refund = 0 - decimal.Parse(dtInsur.Rows[0]["OUT_SETLINFO_FULAMT_OWNPAY_AMT"].ToString());//退医保结算全自费金额
                        decimal out_of_top_amt_refund = 0;// - decimal.Parse(dtInsur.Rows[0][""].ToString());//退医保结算超封顶金额
                        decimal acct_pay_refund = 0 - decimal.Parse(dtInsur.Rows[0]["OUT_SETLINFO_ACCT_PAY"].ToString());//退个人账户
                        decimal psn_cash_pay_refund = 0;
                        decimal ygfund_pay_sumamt_refund = 0;
                        decimal inygscp_amt_refund = 0;
                        Army_Security_Interface.of_security_refund(rcpt_refund_no, rcpt_no, 2, 1, fund_pay_sumamt_refund, pay_first_fee_refund, preselfpay_amt_refund, fulamt_ownpay_amt_refund, out_of_top_amt_refund, acct_pay_refund, ref psn_cash_pay_refund, ref ygfund_pay_sumamt_refund, ref inygscp_amt_refund, ref idc);
                    }
                    else
                    {
                        decimal fund_pay_sumamt_refund = 0;//退医保统筹
                        decimal pay_first_fee_refund = 0;//退医保结算起付线金额
                        decimal preselfpay_amt_refund = 0;//退医保结算乙类自付金额
                        decimal fulamt_ownpay_amt_refund = 0;//退医保结算全自费金额
                        decimal out_of_top_amt_refund = 0;// - decimal.Parse(dtInsur.Rows[0][""].ToString());//退医保结算超封顶金额
                        decimal acct_pay_refund = 0;//退个人账户
                        decimal psn_cash_pay_refund = 0;
                        decimal ygfund_pay_sumamt_refund = 0;
                        decimal inygscp_amt_refund = 0;
                        Army_Security_Interface.of_security_refund(rcpt_refund_no, rcpt_no, 2, 1, fund_pay_sumamt_refund, pay_first_fee_refund, preselfpay_amt_refund, fulamt_ownpay_amt_refund, out_of_top_amt_refund, acct_pay_refund, ref psn_cash_pay_refund, ref ygfund_pay_sumamt_refund, ref inygscp_amt_refund, ref idc);
                    }
                    /////////////
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("军保退费失败" + ex.Message, "错误提示");
            }

            //提交数据库 保存数据
            string result = new NM_Service.NMService.ServerPublicClient().SaveTable(idc);
            if (!string.IsNullOrEmpty(result))
            {
                //if (gs_insurance_chargetype.IndexOf(charge_type) >= 0)
                //{
                //    if (charge_type.Equals("医保患者") || charge_type.Equals("城镇居民"))
                //    {
                //        FuncRollbackTrans();
                //        wf_delete_insur_table(rcpt_no);
                //    }
                //}
                XtraMessageBox.Show(result, "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            else
            {
                //if (gs_insurance_chargetype.IndexOf(charge_type) >= 0)
                //{
                //    if (charge_type.Equals("医保患者") || charge_type.Equals("城镇居民"))
                //    {
                //        FuncCommitTrans();
                //    }
                //}
                #region 平台数据传输2021
                string strERROR_TEXT = string.Empty;
                //处理检查信息
                string strExamSQL = string.Format(@"select * from outp_bill_items where rcpt_no = '{0}' and his_unit_code='{1}' and item_class = 'D' ", rcpt_no, SystemParm.HisUnitCode);
                DataTable dataTableExamTEMP = new NM_Service.NMService.ServerPublicClient().GetDataBySql(strExamSQL).Tables[0];
                if (dataTableExamTEMP.Rows.Count > 0)
                {
                    DataTable[] examDataTables = new DataTable[] { dataTableExamTEMP };
                    Tjhis.Interface.Station.Interface_Common.InvokeInterface("Obilling_007", "UPDATE", this.AppCode, this.DeptCode, examDataTables, ref strERROR_TEXT);

                }
                //处理检验信息
                string strAppSQL = string.Format("select * from outp_bill_items where rcpt_no = '{0}' and his_unit_code='{1}' and item_class = 'C' ", rcpt_no, SystemParm.HisUnitCode);
                DataTable dataTableAppTEMP = new NM_Service.NMService.ServerPublicClient().GetDataBySql(strAppSQL).Tables[0];
                if (dataTableAppTEMP.Rows.Count > 0)
                {
                    DataTable[] appDataTables = new DataTable[] { dataTableAppTEMP };
                    Tjhis.Interface.Station.Interface_Common.InvokeInterface("Obilling_008", "UPDATE", this.AppCode, this.DeptCode, appDataTables, ref strERROR_TEXT);

                }
                //处理处方信息 
                string strPrescSQL = string.Format("select '" + is_clinic_no + "' as clinic_no , a.* from outp_bill_items a where rcpt_no = '{0}'and his_unit_code='{1}' and item_class IN ( 'A','B' ) ", rcpt_no, SystemParm.HisUnitCode);
                DataTable dataTablePrescTEMP = new NM_Service.NMService.ServerPublicClient().GetDataBySql(strPrescSQL).Tables[0];
                if (dataTablePrescTEMP.Rows.Count > 0)
                {
                    DataTable[] prescDataTables = new DataTable[] { dataTablePrescTEMP };
                    Tjhis.Interface.Station.Interface_Common.InvokeInterface("Obilling_009", "UPDATE", this.AppCode, this.DeptCode, prescDataTables, ref strERROR_TEXT);

                }

                //处理处置信息
                string strTrtSQL = string.Format("select * from outp_bill_items where rcpt_no = '{0}' and his_unit_code='{1}' and item_class  NOT IN ( 'A','B','C','D' ) ", rcpt_no, SystemParm.HisUnitCode);
                DataTable trtDataTableTEMP = new NM_Service.NMService.ServerPublicClient().GetDataBySql(strTrtSQL).Tables[0];
                if (trtDataTableTEMP.Rows.Count > 0)
                {
                    DataTable[] trtDataTables = new DataTable[] { trtDataTableTEMP };
                    Tjhis.Interface.Station.Interface_Common.InvokeInterface("Obilling_010", "UPDATE", this.AppCode, this.DeptCode, trtDataTables, ref strERROR_TEXT);

                }

                #endregion

                printRcpt(rcpt_refund_no);//zhj 打印退费单据

                //电子发票冲红调用                 
                eInvoiceCancel(rcpt_no);
                //电子发票冲红调用

                XtraMessageBox.Show("退费成功!", "提示信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                ue_cancel();
            }

            string return_rcpt_no = rcpt_no;
            if (part_refund != true)
            {
                rcpt_no = rcpt_refund_no;
            }
            else
            {
                rcpt_no = rcpt_curr_no;
            }

            if (part_refund == true) //部分退费
            {

            }
            else
            {
                if (gs_insurance_chargetype.IndexOf(charge_type) >= 0 && print_card == true)
                {
                    //  f_print_rcpt();
                }
            }

            completed = true; //
            start_save = false;


            #region 推送平台消息 by lions 2018-12-27
            string lis_use_interface = PlatCommon.SysBase.SystemParm.GetParameterValue("LIS_USE_INTERFACE", "OUTPDOCT", this.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
            if (string.IsNullOrEmpty(lis_use_interface))
            {
                lis_use_interface = PlatCommon.SysBase.SystemParm.GetParameterValue("LIS_USE_INTERFACE", "*", this.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
            }
            string msg = "";
            if ("1".Equals(lis_use_interface))
            {
                Tjhis.Obilling.Station.View.Tjpt tjpt = new Tjpt();
                string return_str = tjpt.PlatRefund_Outp(return_rcpt_no, ref msg);
                if (string.IsNullOrEmpty(return_str))
                {
                    //发送成功
                    Utility.LogFile.WriteLogAuto(msg, "门诊撤销检查检验");
                    return;
                }
                XtraMessageBox.Show("发送到平台失败：" + return_str, "错误提示");
                //取失败单据 by lions 
                string[] list = return_str.Split(new string[] { "||" }, StringSplitOptions.RemoveEmptyEntries);
                Encoding encode = Encoding.GetEncoding("GBK");
                if (encode.GetByteCount(return_str) > 1000)
                {
                    return_str = encode.GetString(encode.GetBytes(return_str), 0, 1000);
                }
                if (!string.IsNullOrEmpty(return_str))
                {
                    //发送错误，提示，保存日志
                    foreach (string str in list)
                    {
                        string billno = "";
                        if (str.IndexOf("E") == 0)
                        {
                            //失败检查
                            billno = str.Substring(1);
                            if (billno.LastIndexOf(",") == billno.Length - 1)
                            {
                                billno = billno.Remove(billno.Length - 1);
                            }
                        }
                        else if (str.IndexOf("T") == 0)
                        {
                            //失败检验
                            billno = str.Substring(1);
                            if (billno.LastIndexOf(",") == billno.Length - 1)
                            {
                                billno = billno.Remove(billno.Length - 1);
                            }
                        }

                        string sqlinsert = "INSERT INTO COMM.TJ_ESB_LOG (PATIENT_ID, VISIT_ID, CZSJ, FWID, HIS_SERIAL, CZBS, INPUT, OUTPUT, FLAG, FUN, INFO, RCPT_NO, EXAMLAB_NO) VALUES( '" + is_patientid + "', '0', sysdate, '撤销', '1', '1','" + return_str + "','" + msg + "', '-1', 'MZCX', '门诊撤销', '" + rcpt_refund_no + "', '" + billno + "') ";
                        Dictionary<string, string> idc = new Dictionary<string, string>();
                        idc.Add(sqlinsert, "日志错误");
                        string ret = new ServerPublicClient().SaveTable(idc);
                        if (!string.IsNullOrEmpty(ret))
                        {
                            Utility.LogFile.WriteLogAuto(ret + ",mess：" + sqlinsert, "门诊检查检验退费");
                        }

                    }
                }
            }
            #endregion
            //st_info.Text = "退费成功";

        }

        /// <summary>
        /// 退费单据打印
        /// </summary>
        /// <param name="rcptNo">收据号</param>
        private void printRcpt(string rcptNo)
        {
            if (XtraMessageBox.Show("是否打印门诊收据单？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                Hashtable hasParam = new Hashtable
                                {
                                    { "RCPT_NO", rcptNo }
                                };
                DataSet dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊退费单打印", hasParam, this.AppCode);

                string printname1 = Utility.ConfigHelper.GetConfigString("OBILLING_CHARGE_PRINT")?.ToString() ?? "";
                if (!string.IsNullOrEmpty(printname1))//
                {
                    //划价收费收据
                    class_public_setprint.print_load_printname(printname1);//设置默认打印机
                }
                XtraReportHelper.Print("门诊退费单打印", dsPrint, this.AppCode);
            }
        }

        /// <summary>
        /// 电子发票处理
        /// 门诊退费 发票冲红
        /// </summary>
        /// <param name="rcptNo">收据号</param>
        /// <returns>成功true，失败false</returns>
        private void eInvoiceCancel(string rcptNo)
        {
            if (EInvoiceHelper.IsEInvoiceEnabled_Obilling)
            {
                bool ret = new EInvoiceHelper().EInvoiceOutpatientCancel(rcptNo, out string errorMessage);//调用门诊收费冲红发票接口
                if (!ret)
                {
                    XtraMessageBox.Show($"电子票据冲红失败！{errorMessage}", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 解析12320xml串
        /// </summary>
        /// <param name="out_result"></param>
        /// <param name="out_msg"></param>
        /// <param name="out_encrypted"></param>
        /// <returns></returns>
        private int f_decryptxml(string out_result, ref string out_msg, ref string out_encrypted)
        {
            try
            {
                HIS12320.HIS gws_his12320 = new HIS12320.HIS();
                int ll_start_code, ll_end_code;
                int ll_start_encrypted, ll_end_encrypted;
                string RETURN_CODE, out_encrypted1;
                if (out_result.IndexOf("<RETURN_CODE><![CDATA[") < 0)//12320平台有时有转义字符，有时没转义字符，接口比较考虑不周
                {
                    ll_start_code = out_result.IndexOf("<RETURN_CODE>");
                    ll_end_code = out_result.IndexOf("</RETURN_CODE>");
                    RETURN_CODE = out_result.Substring(ll_start_code + 13, ll_end_code - ll_start_code - 13);
                }
                else
                {
                    ll_start_code = out_result.IndexOf("<RETURN_CODE><![CDATA[");
                    ll_end_code = out_result.IndexOf("]]></RETURN_CODE>");
                    RETURN_CODE = out_result.Substring(ll_start_code + 22, ll_end_code - ll_start_code - 22);

                }
                if (RETURN_CODE.Equals("0"))
                { out_msg = "推送成功"; }
                else if (RETURN_CODE.Equals("1"))
                {
                    out_msg = "推送失败";
                }
                else if (RETURN_CODE.Equals("500101"))
                {
                    out_msg = "500101没有需要处理停诊的订单";
                    return -1;
                }
                else if (RETURN_CODE.Equals("500301"))
                {
                    out_msg = "500301订单不存在";
                    return -1;
                }
                else if (RETURN_CODE.Equals("500302"))
                {
                    out_msg = "500302订单已关闭";
                    return -1;
                }
                else if (RETURN_CODE.Equals("500303"))
                {
                    out_msg = "500303退款金额不正确";
                    return -1;
                }
                else if (RETURN_CODE.Equals("500304"))
                {
                    out_msg = "500304业务类型不正确";
                    return -1;
                }
                else if (RETURN_CODE.Equals("500601"))
                {
                    out_msg = "500601挂号订单不存在";
                    return -1;
                }
                else if (RETURN_CODE.Equals("600101"))
                {
                    out_msg = "600101订单不存在";
                    return -1;
                }
                else if (RETURN_CODE.Equals("600102"))
                {
                    out_msg = "600102订单已关闭";
                    return -1;
                }
                else if (RETURN_CODE.Equals("600103"))
                {
                    out_msg = "600103退款金额不正确";
                    return -1;
                }
                else if (RETURN_CODE.Equals("600201"))
                {
                    out_msg = "600201订单不存在";
                    return -1;
                }

                if (out_result.IndexOf("<RES_ENCRYPTED><![CDATA[") < 0)//12320平台有时有转义字符，有时没转义字符，接口比较考虑不周
                {
                    ll_start_encrypted = out_result.IndexOf("<RES_ENCRYPTED>");
                    ll_end_encrypted = out_result.IndexOf("</RES_ENCRYPTED>");
                    out_encrypted1 = out_result.Substring(ll_start_encrypted + 15, ll_end_encrypted - ll_start_encrypted - 15);
                }
                else
                {
                    ll_start_encrypted = out_result.IndexOf("<RES_ENCRYPTED><![CDATA[");
                    ll_end_encrypted = out_result.IndexOf("]]></RES_ENCRYPTED>");
                    out_encrypted1 = out_result.Substring(ll_start_encrypted + 24, ll_end_encrypted - ll_start_encrypted - 24);
                }
                out_encrypted = gws_his12320.DecryptTest(out_encrypted1); //上传

                return 0;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
                return -1;
            }
        }

        //清空数据
        private void ue_cancel()
        {
            rcpt_no = "";
            rcpt_curr_no = "";
            rcpt_refund_no = "";
            name = "";
            name_phonetic = "";
            identity = "";
            charge_type = "";
            is_patientid = "";
            sle_patient_id.Text = "";
            sle_name.Text = "";
            sle_identity.Text = "";
            sle_charge_type.Text = "";
            sle_operator.Text = "";
            em_date.Text = "";
            total_costs = 0;
            total_charges = 0;
            em_costs.Text = "0.0000";
            em_charges.Text = "0.0000";
            em_pay_from_insured.Text = "0.0000";
            //st_info.Text="";
            start_save = false;
            completed = false;
            ib_back = false;


            sle_rcpt.Enabled = true;
            sle_no.Enabled = true;
            sle_rcpt.Text = new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString("yyyyMMdd");
            sle_invoice_no.Text = "";
            sle_no.Text = null;
            sle_rcpt.ReadOnly = false;
            sle_no.Focus();
            isInsurCardNo = "";
            insurance_no = "";
            insurance_type = "";
            tinsur_cardno.Text = "";
            if (dw_query_rcpt != null)
                dw_query_rcpt.Clear();
            if (dw_bill_detail != null)
                dw_bill_detail.Clear();
            if (dw_order != null)
                dw_order.Clear();
            if (dw_payments_money != null)
                dw_payments_money.Clear();
            if (dw_payment != null)
                dw_payment.Clear();
            if (dw_rcpt_detail != null)
                //dw_rcpt_detail = dw_rcpt_detail_init;
                //dw_rcpt_detail.Clear();
                dw_retrieve();//收费类别界面刷新
                              //gridControl1.DefaultView.ClearDocument();
                              //gridControl1.DataSource = dw_rcpt_detail;


        }

        private int wf_del_presc(string as_rcpt_no)
        {
            //删除待发药处方主记录
            string sql = "Delete From drug_presc_master_temp Where  rcpt_no = '" + as_rcpt_no + "' and his_unit_code='" + SystemParm.HisUnitCode + "'";
            idc.Add(sql, "删除待发药处方主记录失败！");
            sql = "  SELECT VISIT_DATE,VISIT_NO,PATIENT_ID,ORDERED_BY_DEPT,ORDERED_BY_DOCTOR,RCPT_NO,PRESC_INDICATOR,PRESC_ATTR,CLINIC_NO FROM OUTP_ORDER_DESC WHERE ( RCPT_NO ='" + as_rcpt_no + "' ) ORDER BY VISIT_DATE ASC,VISIT_NO ASC ";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count > 0)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    string ldt_visit_date = dt.Rows[i]["VISIT_DATE"].ToString();
                    long ll_visit_no = long.Parse(dt.Rows[i]["VISIT_NO"].ToString());

                    sql = "Delete From drug_presc_detail_temp Where  presc_date = to_date('" + ldt_visit_date + "','yyyy-mm-dd hh24:mi:ss') and his_unit_code='" + SystemParm.HisUnitCode + "' And presc_no = " + ll_visit_no + "";
                    idc.Add(sql, "删除待发药处方明细失败！");
                }
            }

            return 0;
        }

        private int wf_refund_prepayment(string as_patient_id, string as_rcpt_no)
        {
            decimal ldec_money;
            if (gs_payway_prepayment.Length > 0 || prepayment_flag == true)
            {
                string sql = "  Select nvl(sum(PAYMENT_AMOUNT - REFUNDED_AMOUNT),0) ldec_money From outp_payments_money Where rcpt_no ='" + as_rcpt_no + "' And money_type ='" + gs_payway_prepayment + "'";
                DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
                ldec_money = decimal.Parse(dt.Rows[0]["LDEC_MONEY"].ToString());
                if (ldec_money > 0)
                {
                    if (ucc2_his21_prepayment.uf_outp_repay(is_patientid, ldec_money, as_rcpt_no, PlatCommon.SysBase.SystemParm.LoginUser.ID, ref idc) < 0)
                    {
                        return -1;
                    }
                }

            }


            return 0;
        }

        //暂留
        private void wf_back_set()
        {
        }
        //判断检验类是否可退费
        bool IsLabReturn(string testNo)
        {
            string sql = string.Format("select result_status from lab_test_master where test_no = '{0}' and his_unit_code='{1}'", testNo, SystemParm.HisUnitCode);
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            string status = "";
            if (dt.Rows.Count > 0 && dt.Rows[0][0].ToString().Length > 0)
            {
                status = dt.Rows[0][0].ToString();
                // if (!status.Equals("1")&& !status.Equals("5"))
                if (!status.Equals("1"))
                {
                    return false;
                }
            }
            return true;
        }
        //判断检查类是否可退费
        bool IsExamReturn(string testNo)
        {

            string sql = string.Format("select cancel_oper from exam_master where exam_no = '{0}'and his_unit_code='{1}'", testNo, SystemParm.HisUnitCode);
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            string status = "";
            if (dt.Rows.Count > 0)
            {
                status = dt.Rows[0][0].ToString();
                //if (!status.Equals("1"))
                if (!string.IsNullOrEmpty(status))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 判断是否可退费
        /// </summary>
        /// <param name="rcptNo"></param>
        /// <returns>true-可退费；false-不能退费</returns>
        private bool IsRcptReturn(string rcptNo)
        {
            string sql = string.Format("SELECT A.RCPT_NO FROM OUTP_RCPT_MASTER A WHERE A.RCPT_NO='{0}' and A.his_unit_code='{1}' AND (A.CHARGE_INDICATOR='2' OR A.REFUNDED_RCPT_NO IS NOT NULL)", rcptNo, SystemParm.HisUnitCode);
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt.Rows.Count > 0)
            {
                return false;
            }
            return true;
        }
        private int wf_save_doct()
        {
            #region 新版门诊医生站另走退费模式
            //long ll_prescno, ll_itemno;
            //string ls_opercode, ls_serialno, ls_orderclass, ls_prescno, ls_itemno, ls_appointno;
            //string sql = "";
            //Int32 li_appoint_no; //申请号
            //if (string.IsNullOrEmpty(is_opercode)) return 0;
            //// 循环逐个处理操作码
            //string[] sArray = is_opercode.Split(',');
            //foreach (string i in sArray)
            //{
            //    ls_opercode = i;
            //    if (string.IsNullOrEmpty(ls_opercode)) continue;

            //    // 分解操作码
            //    ls_serialno = ls_opercode.Substring(0, 10);
            //    ls_serialno = ls_serialno.TrimStart(new char[] { '0' });//去掉前面的0；
            //    ls_orderclass = ls_opercode.Substring(10, 1);
            //    if (ls_orderclass.Equals("A") || ls_orderclass.Equals("B"))
            //    {
            //        ls_prescno = ls_opercode.Substring(11);
            //        ll_prescno = long.Parse(ls_prescno);
            //        // 若是药品，则更新处方收费标志
            //        sql = "Update outp_orders_standard Set charge_indicator = 2 Where outp_serial_no ='" + ls_serialno + "' And rcpt_no ='" + rcpt_no + "'";
            //        idc.Add(sql, "更新医生站处方药品退费标记错误");

            //        sql = "Update outp_orders_costs_standard Set charge_indicator = 2 Where outp_serial_no ='" + ls_serialno + "' and  rcpt_no ='" + rcpt_no + "'";
            //        idc.Add(sql, "更新医生站处方药品计价退费标记错误");
            //    }
            //    else
            //    {
            //        ls_itemno = ls_opercode.Substring(11, 3);
            //        if (ls_itemno.Equals("000"))
            //        {
            //            ls_appointno = ls_opercode.Substring(14);
            //            ll_itemno = 0;
            //        }
            //        else
            //        {
            //            ls_appointno = "";
            //            ll_itemno = long.Parse(ls_itemno);
            //        }

            //        // 若是非药品，则更新处置收费标志
            //        if (string.IsNullOrEmpty(ls_appointno))
            //        {
            //            sql = "Update outp_orders_standard Set charge_indicator = 2 Where outp_serial_no ='" + ls_serialno + "' And rcpt_no ='" + rcpt_no + "'";
            //            idc.Add(sql, "更新医生站非药品退费标记错误");

            //            sql = "Update outp_orders_costs_standard Set charge_indicator = 2 Where outp_serial_no ='" + ls_serialno + "' And  order_class ='" + ls_orderclass + "' and  rcpt_no ='" + rcpt_no + "'";
            //            idc.Add(sql, "更新医生站非药品计价退费标记错误");
            //        }
            //        else
            //        {
            //            if (ls_orderclass.Equals("C") || ls_orderclass.Equals("D") || ls_orderclass.Equals("F"))
            //            {
            //                sql = "Update outp_orders_standard Set charge_indicator = 2 Where outp_serial_no ='" + ls_serialno + "' And appoint_no ='" + ls_appointno + "'";
            //                idc.Add(sql, "更新医生站非药品退费标记错误(有申请号的)");

            //                sql = "Update outp_orders_costs_standard Set charge_indicator = 2 Where outp_serial_no ='" + ls_serialno + "' And  order_class ='" + ls_orderclass + "' and  rcpt_no ='" + rcpt_no + "'";
            //                idc.Add(sql, "更新医生站非药品计价退费标记错误(有申请号的)");
            //            }

            //            if (ls_orderclass.Equals("C"))  // 检验
            //            {
            //                sql = "Update lab_test_master Set billing_indicator = 2 Where test_no ='" + ls_appointno + "'";
            //                idc.Add(sql, "更新检验申请主记录失败！");

            //                sql = "Update lab_test_items Set billing_indicator = 2 Where test_no = '" + ls_appointno + "'";
            //                idc.Add(sql, "更新检验申请明细记录失败！");

            //            }
            //            else if (ls_orderclass.Equals("D")) // 检查
            //            {
            //                sql = "Update exam_appoints Set billing_indicator = 2 Where exam_no ='" + ls_appointno + "'";
            //                idc.Add(sql, "更新检查预约申请主记录失败！");

            //                sql = "Update exam_items Set billing_indicator = 2 Where exam_no ='" + ls_appointno + "'";
            //                idc.Add(sql, "更新检查预约申请明细记录失败！");

            //            }
            //            else if (ls_orderclass.Equals("F"))// 手术
            //            {
            //                li_appoint_no = int.Parse(ls_appointno);
            //                sql = "Update operation_schedule Set Ack_indicator = 3 Where patient_id ='" + is_patientid + "' And visit_no = 0 And schedule_id =" + li_appoint_no + "";
            //                idc.Add(sql, "更新手术申请记录失败！");
            //            }
            //        }
            //    }
            //}
            #endregion
            string OutpSerialNo = string.Empty;
            string outpSNo = string.Empty;
            string OrderNo = string.Empty;
            string OrderSubno = string.Empty;
            string itemNo = string.Empty;
            string clinicItemCode = string.Empty;
            string clinicNo = string.Empty;
            string ls_appointno = string.Empty;
            string ls_appointno1 = string.Empty;
            for (int i = 0; i < dw_bill_detail.Rows.Count; i++)
            {
                OutpSerialNo = dw_bill_detail.Rows[i]["outp_serial_no"].ToString();
                OrderNo = dw_bill_detail.Rows[i]["outp_item_no"].ToString();
                OrderSubno = dw_bill_detail.Rows[i]["outp_sub_no"].ToString();
                itemNo = dw_bill_detail.Rows[i]["outp_item_sub_no"].ToString();
                clinicItemCode = dw_bill_detail.Rows[i]["CLINIC_ITEM_CODE"].ToString();
                ls_appointno = dw_bill_detail.Rows[i]["APPOINT_NO"].ToString();
                // clinicNo = dw_bill_detail.Rows[i]["CLINIC_NO"].ToString();
                if (!outpSNo.Equals(OutpSerialNo))
                {
                    string sqla = "Update outp_orders_costs_standard Set charge_indicator = 2 Where outp_serial_no ='" + OutpSerialNo + "' and  rcpt_no ='" + rcpt_no + "'  and his_unit_code='" + SystemParm.HisUnitCode + "'";
                    if (!idc.ContainsKey(sqla))
                    { idc.Add(sqla, "更新医生站计价表计价退费标记错误"); }
                    string sqlb = "Update outp_orders_standard Set charge_indicator = 2 Where outp_serial_no ='" + OutpSerialNo + "' And rcpt_no ='" + rcpt_no + "'  and his_unit_code='" + SystemParm.HisUnitCode + "'";
                    if (!idc.ContainsKey(sqlb))
                    { idc.Add(sqlb, "更新医生站医嘱表计价退费标记错误"); }
                    string sqlc = "Update ind_outp_orders_costs Set charge_indicator = 2 Where outp_serial_no ='" + OutpSerialNo + "' And rcpt_no ='" + rcpt_no + "'  and his_unit_code='" + SystemParm.HisUnitCode + "'";
                    if (!idc.ContainsKey(sqlc))
                    { idc.Add(sqlc, "更新医生站开单计价表计价退费标记错误"); }
                }
                if (!string.IsNullOrEmpty(ls_appointno))
                {
                    if (!ls_appointno1.Equals(ls_appointno))
                    {
                        StringBuilder sbc = new StringBuilder();
                        sbc.Append(" select order_class  from outp_orders_costs_standard ooc  ");
                        sbc.Append("where ooc.RCPT_NO = '" + rcpt_no + "' and ");
                        sbc.Append("ooc.order_no ='" + OrderNo + "' and ");
                        sbc.Append("ooc.order_sub_no ='" + OrderSubno + "' and ");
                        sbc.Append("ooc.item_no ='" + itemNo + "' and ");
                        sbc.AppendFormat("ooc.his_unit_code='{0}'", SystemParm.HisUnitCode);
                        // sbc.Append(" AND ooc.rcpt_no = '" + rcpt_no + "'");
                        DataTable dt = new ServerPublicClient().GetList(sbc.ToString()).Tables[0];
                        if (dt != null && dt.Rows.Count > 0)
                        {
                            string ls_orderclass = dt.Rows[0][0].ToString();
                            if (ls_orderclass.Equals("C"))
                            {
                                //桓仁肖亮要求屏掉20190813
                                //判断是否可退费
                                //if (!IsLabReturn(ls_appointno))
                                //{
                                //    XtraMessageBox.Show("检验项目申请号【" + ls_appointno + "】已由执行科室确认，不能退费", "提示信息");
                                //    return -1;
                                //}
                                string sqlc1 = string.Format("Update lab_test_master Set billing_indicator = 2 Where test_no ='{0}' and his_unit_code='{1}'", ls_appointno, SystemParm.HisUnitCode);
                                if (!idc.ContainsKey(sqlc1))
                                    idc.Add(sqlc1, "更新检验申请主记录失败！");

                                string sqlc2 = "Update lab_test_items Set billing_indicator = 2 Where test_no = '" + ls_appointno + "'";
                                if (!idc.ContainsKey(sqlc2))
                                    idc.Add(sqlc2, "更新检验申请明细记录失败！");
                            }
                            else if (ls_orderclass.Equals("D")) // 检查 ee
                            {
                                //判断是否可退费
                                //if (!IsExamReturn(ls_appointno))
                                //{
                                //    XtraMessageBox.Show("检查项目申请号【" + ls_appointno + "】已经由执行科室确认，不能退费", "提示信息");
                                //    return -1;
                                //}
                                //string sqld1 = "DELETE FROM exam_appoints Where exam_no ='" + ls_appointno + "'";
                                //if (!idc.ContainsKey(sqld1))
                                //idc.Add(sqld1, "更新检查预约申请主记录失败！");

                                //string sqld2 = "DELETE  FROM  exam_items  Where exam_no ='" + ls_appointno + "'";
                                //if (!idc.ContainsKey(sqld2))
                                //idc.Add(sqld2, "更新检查预约申请明细记录失败！");
                                string sql1 = string.Format("Update exam_appoints Set billing_indicator = 2 Where exam_no ='{0}' and his_unit_code='{1}'", ls_appointno, SystemParm.HisUnitCode);
                                idc.Add(sql1, "更新检查预约申请主记录失败！");

                                sql1 = "Update exam_items Set billing_indicator = 2 Where exam_no ='" + ls_appointno + "'";
                                idc.Add(sql1, "更新检查预约申请明细记录失败！");
                                sql1 = "UPDATE exam_master set CANCEL_OPER='" + PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME + "',CANCEL_DATETIME=to_date('" + PlatCommon.Common.PublicFunction.GetSysDate() + "','yyyy-mm-dd HH24:mi:ss'),RESULT_STATUS='0'  Where exam_no = '" + ls_appointno + "'and his_unit_code='" + SystemParm.HisUnitCode + "'";
                                idc.Add(sql1, "撤销申请信息出错!");
                            }
                            else if (ls_orderclass.Equals("F"))// 手术
                            {

                                string sqlf = "Update operation_schedule Set Ack_indicator = 3 Where patient_id ='" + is_patientid + "' And visit_no = 0 And schedule_id ='" + ls_appointno + "'";
                                if (!idc.ContainsKey(sqlf))
                                    idc.Add(sqlf, "更新手术申请记录失败！");
                            }
                        }
                    }
                    ls_appointno1 = ls_appointno;
                }
                outpSNo = OutpSerialNo;
            }
            return 0;
        }

        private int ue_refund()
        {

            string ls_opercode, ls_opercode1;
            long ll_visit_no;
            idt_visitdate = new ServerPublicClient().GetSysDate();
            // 记录退费项目的门诊操作码
            is_opercode = "";
            ls_opercode = "";
            for (int i = 0; i < dw_bill_detail.Rows.Count; i++)
            {
                ls_opercode1 = dw_bill_detail.Rows[i]["OPER_CODE"].ToString();
                if (ls_opercode.Equals(ls_opercode1)) continue;
                ls_opercode = ls_opercode1;
                if (string.IsNullOrEmpty(ls_opercode)) continue;
                ls_opercode = ls_opercode + ",";
                if (!string.IsNullOrEmpty(is_opercode))
                {
                    if (is_opercode.IndexOf(ls_opercode) >= 0) continue;
                }
                is_opercode = is_opercode + ls_opercode;
            }

            //重新产生开单记录 (因为6.6里没写部分退费的所以记录产生只产生退费用的)
            dw_order_new = dw_order.Copy();
            DataTable dw_order_filter;
            //产生并更新就诊序号和就诊日期 和收据号
            for (int ii = 0; ii < dw_order.Rows.Count; ii++)
            {
                //重新产生开单序号
                //string sql = "Select visit_no.nextval il_visit_no From dual";
                //try
                //{
                //    DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
                //    ll_visit_no = long.Parse(dt.Rows[0]["IL_VISIT_NO"].ToString());
                //}
                //catch(Exception ex)
                //{
                //    throw new Exception("就诊序号发生器出错" + ex.Message);
                //}
                //2018-06-22 新的序列号
                string visit_no = "-1";
                PlatCommon.Common.PublicFunction.GetSequeceFromAuto("就诊序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref visit_no);
                ll_visit_no = Convert.ToInt32(visit_no);
                if (ll_visit_no < 0)
                {
                    //DevExpress.XtraEditors.XtraMessageBox.Show("就诊序号发生器出错!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    throw new Exception("就诊序号发生器出错");
                }
                dw_order_new.Rows[ii]["VISIT_NO"] = ll_visit_no;
                dw_order_new.Rows[ii]["VISIT_DATE"] = idt_visitdate;
                dw_order_new.Rows[ii]["RCPT_NO"] = rcpt_refund_no;
            }

            //重新产生诊疗费用项目
            dw_bill_detail_new = dw_bill_detail.Copy();
            for (int b = 0; b < dw_bill_detail.Rows.Count; b++)
            {
                dw_order_filter = dw_order;
                //更新收据号
                dw_bill_detail_new.Rows[b]["RCPT_NO"] = rcpt_refund_no;
                //更新就诊日期
                dw_bill_detail_new.Rows[b]["VISIT_DATE"] = idt_visitdate;
                //更新费用
                dw_bill_detail_new.Rows[b]["AMOUNT"] = 0 - decimal.Parse(dw_bill_detail_new.Rows[b]["AMOUNT"].ToString());
                dw_bill_detail_new.Rows[b]["COSTS"] = 0 - decimal.Parse(dw_bill_detail_new.Rows[b]["COSTS"].ToString());
                dw_bill_detail_new.Rows[b]["CHARGES"] = 0 - decimal.Parse(dw_bill_detail_new.Rows[b]["CHARGES"].ToString());
                //更新就诊序号
                string tmp_visit = dw_bill_detail.Rows[b]["VISIT_NO"].ToString();
                DataRow[] dr = dw_order_filter.Select(" VISIT_NO=" + tmp_visit + "");
                if (dr.Length > 0)
                {
                    dw_bill_detail_new.Rows[b]["VISIT_NO"] = dw_order_new.Rows[dw_order_filter.Rows.IndexOf(dr[0])]["VISIT_NO"];
                }

            }

            //重新生成支付方式数据
            dw_payments_money_new = dw_payments_money;
            for (int p = 0; p < dw_payments_money.Rows.Count; p++)
            {
                dw_payments_money_new.Rows[p]["RCPT_NO"] = rcpt_refund_no;
                dw_payments_money_new.Rows[p]["PAYMENT_AMOUNT"] = 0 - decimal.Parse(dw_payments_money_new.Rows[p]["PAYMENT_AMOUNT"].ToString());
                dw_payments_money_new.Rows[p]["REFUNDED_AMOUNT"] = 0 - decimal.Parse(dw_payments_money_new.Rows[p]["REFUNDED_AMOUNT"].ToString());
                // 若存在银联支付，则根据选择银联支付退款方式，动态重置银联支付的退款方式
                //这个地方改成app参数 可以配置那些支付方式退费走现金
                string ls_money_type = dw_payments_money_new.Rows[p]["MONEY_TYPE"].ToString();
                if (is_bankrefundmode.Equals("0") && gs_refund_payway.IndexOf(ls_money_type) >= 0)
                {
                    dw_payments_money_new.Rows[p]["MONEY_TYPE"] = "现金";
                }

            }

            return 0;
        }

        //取得rcpt_no： 8位日期,6八位来自序列sq_rcpt_number
        private int wf_get_rcpt_no(ref string as_rcpt_no)
        {
            string ls_rcpt_no = "";
            DateTime ldt_today;
            int li_rtn = 0;
            try
            {
                ldt_today = new ServerPublicClient().GetSysDate();
            }
            catch (Exception ex)
            {
                li_rtn = -1;
                throw new Exception("获取系统时间失败！" + ex.Message);
            }
            ls_rcpt_no = ldt_today.ToString("yyyyMMdd");
            if (li_rtn == 0)
            {
                string sql = "select outpbill.sq_rcpt_number.nextval ll_sq_number from dual";
                DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
                if (dt.Rows.Count <= 0)
                {
                    XtraMessageBox.Show("sq_rcpt_number序列执行失败！", "提示");
                    li_rtn = -1;
                }
                else
                {
                    string ls_sq_number = dt.Rows[0]["LL_SQ_NUMBER"].ToString();
                    ls_rcpt_no = ls_rcpt_no + ls_sq_number.PadLeft(5, '0');
                    as_rcpt_no = ls_rcpt_no;
                }

                //by lions 改为从autosettingid获取序列号 2018-06-22
                //string ls_sq_number = "";
                //if (!PlatCommon.Common.PublicFunction.GetSequeceFromAuto("收据序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ls_sq_number) || string.IsNullOrEmpty(ls_sq_number))
                //{
                //    XtraMessageBox.Show("收据序列执行失败！", "提示");
                //    li_rtn = -1;
                //}
                //else
                //{
                //    ls_rcpt_no = ls_rcpt_no + ls_sq_number;
                //    as_rcpt_no = ls_rcpt_no;
                //}
            }

            return li_rtn;
        }

        //
        private void ue_settle()
        {

        }

        //费用查询F1
        private void ue_retrieve()
        {
            string operator_no, money_type;
            string ls_print_rcpt_no, ls_printed_operator_no, ls_card_flag = "";
            string ls_invoice_no;
            string indicator;
            decimal payment, pay_from_insured;
            is_opercode = "";
            //st_info.Text = "正在查询收据信息,请稍候......";
            string sql = "";
            //查询收据记录  
            if (ib_back == false)
            {
                sql = "Select acct_no, patient_id, name, name_phonetic, Identity, charge_type, unit_in_contract,";
                sql = sql + " visit_date, total_costs, total_charges, operator_no, printed_rcpt_no,";
                sql = sql + " printed_operator_no, card_flag, invoice_no, insurance_no, insurance_type, register_type,HIS_UNIT_CODE,clinic_no ";
                sql = sql + " From outp_rcpt_master    Where rcpt_no ='" + rcpt_no + "' And charge_indicator = 0 and his_unit_code='" + SystemParm.HisUnitCode + "'";
            }
            else
            {
                sql = "Select acct_no, patient_id, name, name_phonetic, Identity, charge_type, unit_in_contract,";
                sql = sql + " visit_date, total_costs, total_charges, operator_no, printed_rcpt_no,printed_operator_no, HIS_UNIT_CODE ,clinic_no";
                sql = sql + " From outp_rcpt_master    Where rcpt_no ='" + rcpt_no + "' And charge_indicator = 0  and his_unit_code='" + SystemParm.HisUnitCode + "'";
            }

            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count <= 0)
            {
                XtraMessageBox.Show("未查到交费收据信息", "提示");
                //SetPointer(arrow!);
                //st_info.Text = "";
                sle_no.Focus();
                return;
            }
            is_patientid = dt.Rows[0]["PATIENT_ID"].ToString();
            Name = dt.Rows[0]["NAME"].ToString();
            name_phonetic = dt.Rows[0]["NAME_PHONETIC"].ToString();
            identity = dt.Rows[0]["IDENTITY"].ToString();
            charge_type = dt.Rows[0]["CHARGE_TYPE"].ToString();
            contract_code = dt.Rows[0]["UNIT_IN_CONTRACT"].ToString();
            idt_visitdate = DateTime.Parse(dt.Rows[0]["visit_date"].ToString());
            total_costs = decimal.Parse(dt.Rows[0]["total_costs"].ToString());
            total_charges = decimal.Parse(dt.Rows[0]["total_charges"].ToString());
            operator_no = dt.Rows[0]["OPERATOR_NO"].ToString();
            ls_print_rcpt_no = dt.Rows[0]["PRINTED_RCPT_NO"].ToString();
            ls_printed_operator_no = dt.Rows[0]["PRINTED_OPERATOR_NO"].ToString();
            string ls_his_unit_code = dt.Rows[0]["HIS_UNIT_CODE"].ToString();
            acctNo = dt.Rows[0]["ACCT_NO"].ToString();//结账号
            if (ib_back == false)
            {
                ls_card_flag = dt.Rows[0]["CARD_FLAG"].ToString();
                ls_invoice_no = dt.Rows[0]["INVOICE_NO"].ToString();
                insurance_no = dt.Rows[0]["INSURANCE_NO"].ToString();
                insurance_type = dt.Rows[0]["INSURANCE_TYPE"].ToString();
                is_type = dt.Rows[0]["REGISTER_TYPE"].ToString();
            }

            if (ls_print_rcpt_no.Length > 0 && ls_card_flag.Equals("y"))
            {
                //此处有调用读卡的东西 但是pb里没写全 也就放弃了 看以后项目需不需要吧
                ib_is_medcard = true;
            }
            else
            {
                ib_is_medcard = true;
            }

            //----------------获取收费系数 
            //sql = "select charge_type,price_coeff_numerator,price_coeff_denominator,charge_special_indicator from charge_price_schedule where charge_type='" + charge_type + "' and HIS_UNIT_CODE='" + ls_his_unit_code + "' order by charge_type asc";
            sql = "select charge_type,NUMERATOR_OUTP,DENOMINATOR_OUTP,charge_special_indicator from charge_price_schedule where charge_type='" + charge_type + "' and  HIS_UNIT_CODE='" + ls_his_unit_code + "' order by charge_type asc";//修改住院的收费系数分子分母为门诊的
            DataTable dt1 = new ServerPublicClient().GetList(sql).Tables[0];
            if (dt1.Rows.Count <= 0)
            {
                special_indicator = false;
                charge_numerator = 1;
                charge_denominator = 1;
            }
            else
            {
                charge_numerator = Int32.Parse(dt1.Rows[0]["NUMERATOR_OUTP"].ToString());
                charge_denominator = Int32.Parse(dt1.Rows[0]["DENOMINATOR_OUTP"].ToString());
                indicator = dt1.Rows[0]["CHARGE_SPECIAL_INDICATOR"].ToString();
                if (string.IsNullOrEmpty(indicator) || !indicator.Equals("1"))
                {
                    special_indicator = false;
                }
                else
                {
                    special_indicator = true;
                }
            }

            string ls_operator_name;
            sql = string.Format("Select user_id  From users Where user_id ='{0}' and his_unit_code='{1}'", operator_no, SystemParm.HisUnitCode);
            DataTable uu = new ServerPublicClient().GetList(sql).Tables[0];
            if (uu.Rows.Count <= 0)
            {
                XtraMessageBox.Show("查询用户记录出错! user", "提示");
                //st_info.Text = "";
                sle_no.Focus();
                return;
            }
            ls_operator_name = uu.Rows[0]["user_id"].ToString();

            sle_patient_id.Text = is_patientid;
            sle_name.Text = Name;
            sle_identity.Text = identity;
            sle_charge_type.Text = charge_type;
            em_date.Text = idt_visitdate.ToString("yyyy-MM-dd");

            sle_operator.Text = ls_operator_name;

            em_costs.Text = total_costs.ToString("F2");
            em_charges.Text = total_charges.ToString("F2");

            sql = "SELECT CHARGE_TYPE_NAME,CHARGE_PRICE_INDICATOR FROM CHARGE_TYPE_DICT where CHARGE_TYPE_NAME='" + charge_type + "' and HIS_UNIT_CODE='" + ls_his_unit_code + "' ORDER BY SERIAL_NO ASC";
            DataTable dt2 = new ServerPublicClient().GetList(sql).Tables[0];
            if (dt2.Rows.Count <= 0)
            {
                XtraMessageBox.Show("查询费别字典出错1", "提示");
                //st_info.Text = "";
                sle_no.Focus();
                return;
            }
            else
            {
                string ls_price_type = dt2.Rows[0]["CHARGE_PRICE_INDICATOR"].ToString();
                if (string.IsNullOrEmpty(ls_price_type)) ls_price_type = "0";
                price_type = Int32.Parse(ls_price_type);
            }

            //if (contract_code.Length>0)
            //{
            //    sql="Select UNIT_NAME  From unit_in_contract Where UNIT_CODE ='"+contract_code+"' and HIS_UNIT_CODE='"+ ls_his_unit_code+"'";
            //    DataTable dt3 = new ServerPublicClient().GetList(sql).Tables[0];
            //    if (dt3.Rows.Count < 0)
            //    {
            //        XtraMessageBox.Show("查询费别字典出错2", "提示");
            //        //st_info.Text = "";
            //        sle_no.Focus();
            //        return;
            //    }
            //    else
            //    {
            //        contract_name = dt3.Rows[0]["UNIT_NAME"].ToString();
            //    }
            //}

            string sql_dw_query_rcpt = "";
            string sql_dw_bill_detail = "";
            string sql_dw_order = "";
            string sql_dw_payments_money = "";
            if (ib_back == false)
            {
                sql_dw_query_rcpt = "select b.fee_class_name fee_class_name,b.fee_class_code fee_class_code,  sum(charges)charges  from outp_bill_items a,outp_rcpt_fee_dict b where a.class_on_rcpt=b.fee_class_code(+)  and a.his_unit_code=b.his_unit_code and  rcpt_no ='" + rcpt_no + "' and a.his_unit_code='" + SystemParm.HisUnitCode + "' group by b.fee_class_code, b.fee_class_name";
                sql_dw_bill_detail = "   SELECT VISIT_DATE,VISIT_NO,RCPT_NO,ITEM_NO,ITEM_CLASS,CLASS_ON_RCPT,ITEM_NAME,AMOUNT,UNITS,PERFORMED_BY,COSTS,CHARGES, 0,0,0,'ABCDEFGH',FLAG,INVOICE_NO,REPETITION,CLASS_ON_RECKONING, ";
                sql_dw_bill_detail = sql_dw_bill_detail + "SUBJ_CODE,PRICE_QUOTIETY,ITEM_PRICE,ORDER_NO,SUB_ORDER_NO,ITEM_CODE,ITEM_SPEC,OPER_CODE ,ORDER_DOCTOR,ORDER_DEPT,CLINIC_ITEM_NAME,clinic_item_code, rcptgroupid ,rcptgroup ,outp_item_no,outp_sub_no ,outp_item_sub_no,outp_serial_no,outp_visit_date,outp_visit_no,trade_price,appoint_no " +
                    " , BATCH_NO , BATCH_CODE , GUID" +
                    " FROM OUTP_BILL_ITEMS WHERE RCPT_NO ='" + rcpt_no + "' and his_unit_code='" + SystemParm.HisUnitCode + "' ORDER BY VISIT_NO ASC,ITEM_NO ASC ";
                sql_dw_order = "SELECT VISIT_DATE,VISIT_NO,PATIENT_ID,ORDERED_BY_DEPT,ORDERED_BY_DOCTOR,RCPT_NO,PRESC_INDICATOR,PRESC_ATTR,CLINIC_NO FROM OUTP_ORDER_DESC WHERE RCPT_NO ='" + rcpt_no + "' ORDER BY VISIT_NO ASC";
                sql_dw_payments_money = "SELECT MONEY_TYPE,  PAYMENT_AMOUNT - REFUNDED_AMOUNT AMOUNT , RCPT_NO, PAYMENT_AMOUNT, REFUNDED_AMOUNT, PAYMENT_NO FROM OUTP_PAYMENTS_MONEY WHERE RCPT_NO = '" + rcpt_no + "' ORDER BY PAYMENT_NO ASC ";

            }
            else
            {
                //备份表部分放弃了 表结构都没有 以后有需要可以加

            }

            dw_query_rcpt = new ServerPublicClient().GetList(sql_dw_query_rcpt).Tables[0];
            dw_bill_detail = new ServerPublicClient().GetList(sql_dw_bill_detail).Tables[0];
            dw_order = new ServerPublicClient().GetList(sql_dw_order).Tables[0];
            dw_payments_money = new ServerPublicClient().GetList(sql_dw_payments_money).Tables[0];

            string sql_dw_payment = "select rcpt_no , payment_no , money_type , payment_amount - refunded_amount  as amount from  outp_payments_money where rcpt_no ='" + rcpt_no + "' order by payment_no ";
            dw_payment = new ServerPublicClient().GetList(sql_dw_payment).Tables[0];

            gridControl2.DataSource = dw_payment;
            gridControl3.DataSource = dw_bill_detail;
            part_refund = false;
            money_rows = dw_payments_money.Rows.Count;
            pay_from_insured = 0;
            for (int i = 0; i < money_rows; i++)
            {
                money_type = dw_payments_money.Rows[i]["MONEY_TYPE"].ToString();
                payment = decimal.Parse(dw_payments_money.Rows[i]["AMOUNT"].ToString());
                if (gs_real_payways.IndexOf(money_type) >= 0 || money_type.Equals(gs_payway_prepayment))
                {
                    prior_money_type = money_type;
                    pay_from_insured += payment;
                }
            }


            //循环赋值
            for (int i = 0; i < dw_rcpt_detail.Rows.Count; i++)
            {
                string fee_code = dw_rcpt_detail.Rows[i]["FEE_CLASS_CODE"].ToString();
                for (int j = 0; j < dw_query_rcpt.Rows.Count; j++)
                {
                    string fee_code1 = dw_query_rcpt.Rows[j]["FEE_CLASS_CODE"].ToString();
                    if (fee_code.Equals(fee_code1))
                    {
                        dw_rcpt_detail.Rows[i]["AMOUNT"] = dw_query_rcpt.Rows[j]["CHARGES"].ToString();
                        break;
                    }
                }

            }


            em_pay_from_insured.Text = pay_from_insured.ToString("F2");
            sle_rcpt.ReadOnly = true;
            #region
            int noCash = 0;
            foreach (DataRow dr in dw_payment.Rows)
            {
                if (!dr["money_type"].ToString().Equals("现金") && (dr["money_type"].ToString().IndexOf("舍入") < 0))
                {
                    noCash++;
                }
            }
            if (noCash > 0)
            {
                MessageBox.Show("有非现金支付方式,注意不要找错钱了!", "提示");
            }
            #endregion
            cardView1.Focus();
            //gridControl1.DataSource = dw_rcpt_detail;
            //st_info.Text = "查询完毕";

            //if (gs_insurance_chargetype.IndexOf(charge_type) >= 0) 
            //{
            //    insurancecard();
            //}


            //如果已经结账，则给出提示
            //IsAlreadyAccted(acctNo, out bool bAlreadyAcct);            
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            frm_refund_KeyDown(sender, e);
        }

        private void gridControl1_KeyDown(object sender, KeyEventArgs e)
        {
            frm_refund_KeyDown(sender, e);
        }

        private void repositoryItemTextEdit1_QueryProcessKey(object sender, DevExpress.XtraEditors.Controls.QueryProcessKeyEventArgs e)
        {
            if (sle_rcpt.Text != "")
            {
                if (e.KeyData == Keys.F1 || e.KeyData == Keys.F2 || e.KeyData == Keys.F3 || e.KeyData == Keys.F4 || e.KeyData == Keys.F5 || e.KeyData == Keys.F6 || e.KeyData == Keys.F7 || e.KeyData == Keys.F8)
                {
                    KeyEventArgs key = new KeyEventArgs(e.KeyData);

                    frm_refund_KeyDown(repositoryItemTextEdit1, key);
                    Message m = new Message();
                    base.WndProc(ref m);
                }
            }
        }

        private void repositoryItemTextEdit6_QueryProcessKey(object sender, DevExpress.XtraEditors.Controls.QueryProcessKeyEventArgs e)
        {
            if (sle_no.Text != "")
            {
                if (e.KeyData == Keys.F1 || e.KeyData == Keys.F2 || e.KeyData == Keys.F3 || e.KeyData == Keys.F4 || e.KeyData == Keys.F5 || e.KeyData == Keys.F6 || e.KeyData == Keys.F7 || e.KeyData == Keys.F8 || e.KeyData == Keys.Enter)
                {

                    KeyEventArgs key = new KeyEventArgs(e.KeyData);
                    frm_refund_KeyDown(null, key);
                }
            }
        }

        private void cardView1_DoubleClick(object sender, EventArgs e)
        {
            gridControl1.Visible = false;
            gridControl3.Visible = true;
        }

        private void gridViewBillDetail_DoubleClick(object sender, EventArgs e)
        {
            gridControl1.Visible = true;
            gridControl3.Visible = false;

        }

        private void repositoryItemTextEdit3_QueryProcessKey(object sender, DevExpress.XtraEditors.Controls.QueryProcessKeyEventArgs e)
        {
            if (sle_invoice_no.Text != "")
            {
                if (e.KeyData == Keys.F1 || e.KeyData == Keys.F2 || e.KeyData == Keys.F3 || e.KeyData == Keys.F4 || e.KeyData == Keys.F5 || e.KeyData == Keys.F6 || e.KeyData == Keys.F7 || e.KeyData == Keys.F8 || e.KeyData == Keys.Enter)
                {

                    KeyEventArgs key = new KeyEventArgs(e.KeyData);
                    frm_refund_KeyDown(null, key);
                }
            }
        }

        private void repositoryItemTextEdit1_KeyDown(object sender, KeyEventArgs e)
        {
            KeyEventArgs key = new KeyEventArgs(e.KeyData);
            frm_refund_KeyDown(null, key);
        }

        /// <summary>
        /// 判断是否已经结账
        /// 如果已经结账了，就不让退号
        /// </summary>
        /// <param name="acctNo">结账号</param>
        /// <param name="bAlreadyAcct">true已经结账，false未结账</param>
        /// <returns>true成功，false失败 </returns>
        private bool IsAlreadyAccted(string acctNo, out bool bAlreadyAcct)
        {
            bAlreadyAcct = false;
            try
            {
                if (string.IsNullOrEmpty(acctNo))
                {
                    bAlreadyAcct = false;//未结账
                    return true;
                }
                string strSQL = $"select b.name, a.* from OUTP_ACCT_MASTER a left join staff_dict b on a.his_unit_code=b.his_unit_code and a.operator_no=b.user_name where a.acct_no='{acctNo}'";
                DataTable dt = new ServerPublicClient().GetDataBySql(strSQL).Tables[0];
                if (dt.Rows.Count == 0)
                {
                    bAlreadyAcct = false; //未找到对应的结账主记录，也认为未结账
                    return true;
                }

                DateTime acctTime = Convert.ToDateTime(dt.Rows[0]["ACCT_DATE"]);// 结账时间
                string operatorNo = dt.Rows[0]["OPERATOR_NO"].ToString();//结账人工号
                string operatorName = dt.Rows[0]["NAME"].ToString();//结账人姓名
                bAlreadyAcct = true;//已经结账
                XtraMessageBox.Show($"该笔费用已经由{operatorName}({operatorNo})于 {acctTime.ToString("yyyy-MM-dd HH:mm:ss")} 进行过结账操作，结账后的费用禁止退费！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return true;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"查询结账信息时发生错误！ {ex.Message}", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void blbtnSelect_ItemClick(object sender, ItemClickEventArgs e)
        {
            string ls_invoice_no, ls_rcpt_operator, ls_charge_type;
            //sle_rcpt.Text = rcpt;
            //sle_no.Text = no;

            rcpt_no = sle_rcpt.Text.ToString().Trim() + sle_no.Text.ToString().Trim();
            ls_invoice_no = sle_invoice_no.Text.ToString();

            if (sle_rcpt.Text == null || sle_rcpt.Text.ToString() == "")
            {
                XtraMessageBox.Show("输入收据号！");
                return;
            }
            if (completed == true) return;
            string sql = "Select acct_no, operator_no, charge_type, rcpt_no, invoice_no,his_unit_code,clinic_no From outp_rcpt_master Where ( rcpt_no ='" + rcpt_no + "' Or invoice_no ='" + ls_invoice_no + "') and his_unit_code='" + SystemParm.HisUnitCode + "' ";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count <= 0)
            {
                XtraMessageBox.Show("查询收费信息，无相关收费信息！", "提示");
                //sle_no.Links[0].Focus();
                return;
            }
            sle_rcpt.Enabled = false;
            sle_no.Enabled = false;

            ls_rcpt_operator = dt.Rows[0]["OPERATOR_NO"].ToString();
            ls_charge_type = dt.Rows[0]["CHARGE_TYPE"].ToString();
            rcpt_no = dt.Rows[0]["RCPT_NO"].ToString();
            ls_invoice_no = dt.Rows[0]["INVOICE_NO"].ToString();
            string clinicNo = dt.Rows[0]["CLINIC_NO"].ToString();
            if (rcpt_no.Length > 8)
            {
                sle_rcpt.Text = rcpt_no.Substring(0, 8);
                sle_no.Text = rcpt_no.Substring(8, rcpt_no.Length - 8);
                sle_invoice_no.Text = ls_invoice_no;
                //rcpt = sle_rcpt.Text.ToString();
                //no = sle_no.Text.ToString();
            }

            if (part_refund == true) //合计费用
            {

            }
            else  //收据查询
            {
                if (decimal.Parse(em_costs.Text) > 0) return;
                ib_back = false;
                ue_retrieve();
            }

            isInsurCardNo = "";
            if ("市医保;城镇居民;城乡居民".IndexOf(ls_charge_type) >= 0)
            {
                sql = "select distinct a.performed_by from outp_bill_items a where rcpt_no='" + rcpt_no + "' and his_unit_code='" + SystemParm.HisUnitCode + "'";
                DataTable dtDetail = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
                if (dtDetail.Rows.Count <= 0)
                {
                    XtraMessageBox.Show("查询收费信息，无相关收费信息！", "提示");

                    return;
                }
                else if (dtDetail.Rows.Count > 1)
                {
                    XtraMessageBox.Show("该收据号打印多张收据，如果退费，请全部收回！", "提示");

                    //return;
                }

                DataTable dtClinicMaster = new NM_Service.NMService.ServerPublicClient().GetList("select * from clinic_master where clinic_no='" + clinicNo + "'and his_unit_code='" + SystemParm.HisUnitCode + "'").Tables[0];

                isInsurCardNo = dtClinicMaster.Rows[0]["insur_cardno"].ToString();
                tinsur_cardno.Text = isInsurCardNo;
            }

            cardView1.Focus();
            GetItem_Input();
        }
        /// <summary>
        /// 备份查询
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void blbtnCoptSelect_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (completed == true) return;
            if (part_refund == true) return;
            if (decimal.Parse(em_costs.Text) > 0) return;
            ib_back = true;
            rcpt_refund_no = sle_rcpt.Text.ToString().Trim();

            ue_retrieve();
        }
        /// <summary>
        /// 全部退费
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void blbtnAllReturn_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (completed == true) return;
            //idc.Clear();//清空待保存记录
            idc = new Dictionary<string, string>();
            if (start_save != true)
            {
                start_save = true;
                ue_complete();
            }
        }
        /// <summary>
        /// 清屏
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void blbtnClean_ItemClick(object sender, ItemClickEventArgs e)
        {
            ue_cancel();
        }
        /// <summary>
        /// 接口读卡
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void blbtnReadCard_ItemClick(object sender, ItemClickEventArgs e)
        {
            insurancecard();
        }
        /// <summary>
        /// 退出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void blbtnExit_ItemClick(object sender, ItemClickEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 医保读卡方法
        /// </summary>
        private void insurancecard()
        {
            string ls_user_id = SystemParm.LoginUser.ID;
            string ls_ctkh = "";
            is_pwd = "";
            if (string.IsNullOrEmpty(charge_type)) return;
            DataTable dt = new DataTable();
            if (charge_type.Equals("沈阳干诊")) return;

            dt.Columns.Add("NHZH");//农合证号
            dt.Columns.Add("CHARGE_TYPE");//费别
            dt.Columns.Add("GRBH");//费别

            dt.Columns.Add("JYLX");//交易类型
            dt.Columns.Add("BUSINESSTYPE");//电子凭证-用码业务类型
            dt.Columns.Add("CARD_TYPE");//lxm20210727新医保
            dt.Columns.Add("SFZH");//身份证号

            if (string.IsNullOrEmpty(charge_type)) return;
            //if (charge_type.Equals("新农合"))
            //{
            //    string sql = "select * from insurance.pats_dict_tlxnh where grbh='" + insurance_no + "'";
            //    DataTable pat = new ServerPublicClient().GetList(sql).Tables[0];
            //    if (pat.Rows.Count < 0)
            //    {
            //        XtraMessageBox.Show("新农合查询信息失败" + insurance_no, "提示");
            //        return;
            //    }
            //    DataRow dr = dt.NewRow();
            //    dr["JYLX"] = "11202";
            //    dr["NHZH"] = pat.Rows[0]["NHZH"].ToString();
            //    dr["CHARGE_TYPE"] = charge_type;
            //    dr["GRBH"] = pat.Rows[0]["GRBH"].ToString();
            //    dt.Rows.Add(dr);
            //}

            if (checkinsur.EditValue.Equals("1"))
            {
                DataRow dr = dt.NewRow();

                ls_ctkh = tinsur_cardno.EditValue.ToString();
                if (string.IsNullOrEmpty(ls_ctkh))
                {
                    XtraMessageBox.Show("选择了刷卡方式请刷卡！", "提示");
                    return;
                }
            }

            if (charge_type.Equals("市医保") || charge_type.Equals("城镇居民") || charge_type.Equals("城乡居民"))
            {
                is_pwd = "";
                DataRow dr1 = dt.NewRow();
                dr1["JYLX"] = "";
                if (charge_type.Equals("城乡居民") || charge_type.Equals("市医保"))
                {
                    dr1["JYLX"] = "2200";
                }
                dr1["NHZH"] = "";
                dr1["CHARGE_TYPE"] = charge_type;
                dr1["GRBH"] = ls_ctkh;
                dt.Rows.Add(dr1);
                //弹出密码输入框
                //frm_insur_inputpwd fiip = new frm_insur_inputpwd();
                //fiip.ShowDialog();
                //is_pwd = fiip.is_pwd;
            }

            // lxm20210727新医保
            if (dt.Rows.Count > 0)
            {
                dt.Rows[0]["CARD_TYPE"] = barinsurtype.EditValue.ToString();
                dt.Rows[0]["SFZH"] = "";
            }
            else
            {
                DataRow dr = dt.NewRow();
                dr["SFZH"] = "";
                dr["CARD_TYPE"] = barinsurtype.EditValue.ToString();
                dt.Rows.Add(dr);
            }
            DataSet ds = new DataSet();
            ds.Tables.Add(dt);
            if (MedicalInterface.MedicalBusinessHandle("001", "", "", charge_type, ls_user_id, "", "", ds) >= 0)
            {
                //账户余额
                labelControl5.Text = MedicalInterface.medicalCardInfos.AccountBalance;
                //性别
            }
            else
            {
                MessageBox.Show("读卡失败,请查看日志了解失败原因", "提示");
                return;
            }
        }

        //事物提交
        private int FuncCommitTrans()
        {
            string rcptNo = "", remark = "";
            DataSet ds = new DataSet();
            if (MedicalInterface.MedicalBusinessHandle("022", is_patientid, "", charge_type, PlatCommon.SysBase.SystemParm.LoginUser.ID, rcptNo, remark, ds) >= 0)
            {
            }
            else
            {
                MessageBox.Show("事物提交失败", "提示");
                return -1;
            }
            return 1;
        }

        //事物回滚
        private int FuncRollbackTrans()
        {
            string rcptNo = "", remark = "";
            DataSet ds = new DataSet();
            if (MedicalInterface.MedicalBusinessHandle("023", is_patientid, "", charge_type, PlatCommon.SysBase.SystemParm.LoginUser.ID, rcptNo, remark, ds) >= 0)
            {
            }
            else
            {
                MessageBox.Show("事物回滚失败！", "提示");
                return -1;
            }
            return 1;
        }

        private void barLargeButtonItem1_ItemClick(object sender, ItemClickEventArgs e)
        {
            IdentityCardMessage icm = new IdentityCardMessage();
            if (ReadIdentityCardBusiness.ReadIdentityCard(dic["READ_CARD_NO_TYPE"], ref icm) < 0) return;
            sle_name.Text = icm.Name;
        }

        private void barLargeButtonItem2_ItemClick(object sender, ItemClickEventArgs e)
        {
            IdentityCardMessage icm = new IdentityCardMessage();
            if (ReadIdentityCardBusiness.ReadIdentityCard(dic["READ_ID_NO_TYPE"], ref icm) < 0) return;
            sle_name.Text = icm.Name;
        }

        private void btn_LST_ItemClick(object sender, ItemClickEventArgs e)
        {
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            DataTable dt = new DataTable();
            dt.Columns.Add("OUT_VERIFY_NO");//外部验证流水号
            dt.Columns.Add("OUT_VERIFY_TIME");//
            dt.Columns.Add("OPERATOR_ID");//用卡验证操作员编号
            dt.Columns.Add("OPERATOR_NAME");//用卡验证操作员姓名
            dt.Columns.Add("CERTIFICATE_MODE");//认证方式
            dt.Columns.Add("IP_ADDR");//ip 地址 
            dt.Columns.Add("TREATMENT_CODE");//诊疗环节 
            dt.Columns.Add("office_code");//科室
            string datetimenow = DateTime.Now.ToString("yyyyMMddHHmmss");
            //string lst = SystemParm.GetParaValue("DZJKM_URL","*", "*", "*", "");
            DataRow dr2 = dt.NewRow();
            dr2["OUT_VERIFY_NO"] = datetimenow;
            dr2["OUT_VERIFY_TIME"] = datetimenow;
            dr2["OPERATOR_ID"] = SystemParm.LoginUser.ID;
            dr2["OPERATOR_NAME"] = SystemParm.LoginUser.NAME;
            dr2["CERTIFICATE_MODE"] = "";
            dr2["IP_ADDR"] = "";
            dr2["TREATMENT_CODE"] = "当日挂号";
            dr2["office_code"] = "挂号处";
            dt.Rows.Add(dr2);
            string term_id = "35020010001";
            string out_jsons = "";

            DataTable out_dt = new DataTable();
            LstVirtualCardChk lvcc = new LstVirtualCardChk();

            string out_jsons1 = lvcc.YzDzjkkEwm(spc, dt, term_id, ref out_jsons, ref out_dt);
            string id_no = string.Empty;
            if (out_dt.Columns.Contains("ID_NO"))
            {
                id_no = out_dt.Rows[0]["ID_NO"].ToString();
                string patient_id = new NM_Service.NMService.ServerPublicClient().GetSingleValue("select patient_id from pat_master_index where id_no='" + id_no + "'");
                //tpatient_id_KeyDown(null, new KeyEventArgs(Keys.F9));
            }
        }

        //集中处理挂号中间表数据和明细以及结算表记录
        private int wf_delete_insur_table(string rcpt_no)
        {
            //处理医保中间表数据
            Dictionary<string, string> insur_delete = new Dictionary<string, string>();
            insur_delete.Clear();
            string sql = "update  insurance.outp_save_hldyb  a set   a.refund_date='' ,a.refund_oper='' where a.rcpt_no='" + rcpt_no + "'";
            insur_delete.Add(sql, "恢复医保结算中间表状态失败！");

            sql = "delete from insurance.outp_save_undo_hldyb a where  a.rcpt_no='" + rcpt_no + "'";
            insur_delete.Add(sql, "删除医保退结算中间表记录失败！");

            string resultstr = new ServerPublicClient().SaveTable(insur_delete);
            if (!string.IsNullOrEmpty(resultstr))
            {
                XtraMessageBox.Show("医保中间表数据处理失败！请联系信息中心", "提示");
                return -1;
            }

            return 1;
        }
        //  -------------------------------------------------------------------------------
    }
}
