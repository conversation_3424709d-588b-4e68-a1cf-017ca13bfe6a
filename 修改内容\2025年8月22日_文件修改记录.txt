=== 2025年8月22日 文件修改记录 ===

1. 文件路径：D:\Code\TjhisPlatSource\Tjhis_Outpdoct_station\Business\OrderBusiness.cs
   修改内容：修复门诊医生站费用重复计算问题

2. 文件路径：D:\Code\门诊医生站费用计算错误分析与修复方案_20250821.txt
   修改内容：创建费用计算错误分析与修复方案文档

=== 修改详情 ===

【文件1详细修改】
文件：D:\Code\TjhisPlatSource\Tjhis_Outpdoct_station\Business\OrderBusiness.cs
位置：第1281-1293行 SetAmount方法
修改类型：逻辑修复
问题描述：中药处方费用计算中CHARGES = COSTS × AMOUNT导致费用重复计算
修改前：
    costs.AMOUNT = costs.ChargeAmount * amount;
    costs.CHARGES = costs.CHARGE_PRICE * costs.AMOUNT;
    costs.COSTS = costs.ITEM_PRICE * costs.AMOUNT;

修改后：
    // 修复费用重复计算问题 - 2025-08-21
    costs.AMOUNT = amount;
    costs.COSTS = costs.ITEM_PRICE * costs.AMOUNT;
    
    // 应收费用：有系数时乘系数，无系数时等于计价费用
    if (costs.PRICE_QUOTIETY > 0 && costs.PRICE_QUOTIETY != 1)
    {
        costs.CHARGES = costs.COSTS * costs.PRICE_QUOTIETY;
    }
    else
    {
        costs.CHARGES = costs.COSTS;
    }

影响范围：门诊医生站中药处方数量修改时的费用重新计算
业务影响：解决患者费用被重复计算的问题（如麦冬45克费用从870.75元修正为19.35元）

【文件2详细修改】
文件：D:\Code\门诊医生站费用计算错误分析与修复方案_20250821.txt
修改类型：新建文档
内容概要：
- 问题描述：CHARGES = COSTS × AMOUNT的错误计算模式
- 根源分析：定位到OrderBusiness.cs的SetAmount方法
- 修复方案：提供代码修复和数据修复方案
- 实施步骤：分阶段的实施计划
- 注意事项：兼容性、风险控制、监控要点
- 相关文件清单：需要修改和关注的文件列表

=== 技术总结 ===

修改原因：
用户发现门诊医生站中药处方费用计算异常，CHARGES字段值异常放大，
经分析发现是SetAmount方法中费用计算逻辑错误导致。

核心问题：
1. 数量计算错误：costs.AMOUNT = costs.ChargeAmount * amount
2. 费用重复计算：把已计算的总费用当作单价再乘数量

解决方案：
1. 直接使用传入数量：costs.AMOUNT = amount
2. 正确计算应收费用：考虑计价系数，无系数时CHARGES = COSTS

验证方法：
修复前：麦冬45克，COSTS=19.35元，CHARGES=870.75元（错误）
修复后：麦冬45克，COSTS=19.35元，CHARGES=19.35元（正确）

=== 风险评估 ===

风险等级：中等
影响范围：门诊医生站中药处方费用计算
回滚方案：保留原始代码备份，可快速回滚
测试建议：在测试环境验证各种中药处方的费用计算正确性

=== 后续工作 ===

1. 在测试环境部署并验证修复效果
2. 识别和修复历史错误数据
3. 监控生产环境费用计算的准确性
4. 收集用户反馈，确保无新问题引入

=== 文件状态 ===

修改状态：已完成
测试状态：待测试
部署状态：待部署
文档状态：已完成

=== 备注 ===

本次修改基于用户提供的实际数据分析，通过代码审查定位到具体问题位置。
修改遵循最小化风险原则，保持向后兼容性，只修复错误逻辑不改变整体架构。
建议在正式部署前进行充分的回归测试，特别是中药处方相关的业务流程。
