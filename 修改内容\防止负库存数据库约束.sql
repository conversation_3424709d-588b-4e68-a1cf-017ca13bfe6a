-- =====================================================
-- 防止药品库存为负数的数据库约束脚本
-- 创建日期：2025-01-17
-- 目的：在数据库层面防止药品库存变为负数
-- =====================================================

-- 1. 检查当前负库存情况
SELECT 
    '当前负库存统计' as 检查项目,
    COUNT(*) as 负库存记录数,
    SUM(QUANTITY) as 负库存总量
FROM DRUG_STOCK 
WHERE QUANTITY < 0;

-- 2. 查看负库存详细信息（前10条）
SELECT 
    STORAGE as 药房,
    DRUG_CODE as 药品代码,
    DRUG_NAME as 药品名称,
    DRUG_SPEC as 规格,
    PACKAGE_SPEC as 包装规格,
    FIRM_ID as 厂家,
    BATCH_NO as 批号,
    QUANTITY as 库存数量,
    LAST_UPDATETIME as 最后更新时间
FROM DRUG_STOCK 
WHERE QUANTITY < 0 
ORDER BY LAST_UPDATETIME DESC
FETCH FIRST 10 ROWS ONLY;

-- 3. 创建检查约束防止负库存（谨慎执行）
-- 注意：此约束会阻止所有导致负库存的操作
-- 建议先在测试环境验证，确认不会影响正常业务流程

/*
-- 添加检查约束（请根据实际情况决定是否执行）
ALTER TABLE DRUG_STOCK 
ADD CONSTRAINT CHK_DRUG_STOCK_QUANTITY_NON_NEGATIVE 
CHECK (QUANTITY >= 0);

-- 如果需要删除约束，使用以下语句：
-- ALTER TABLE DRUG_STOCK DROP CONSTRAINT CHK_DRUG_STOCK_QUANTITY_NON_NEGATIVE;
*/

-- 4. 创建触发器记录负库存操作（推荐方案）
-- 这个触发器不会阻止操作，但会记录所有导致负库存的操作，便于追踪问题

CREATE OR REPLACE TRIGGER TRG_DRUG_STOCK_NEGATIVE_LOG
AFTER UPDATE ON DRUG_STOCK
FOR EACH ROW
WHEN (NEW.QUANTITY < 0)
DECLARE
    v_log_message VARCHAR2(4000);
BEGIN
    -- 记录负库存操作到日志表
    v_log_message := '负库存警告: ' || 
                    '药房=' || :NEW.STORAGE || 
                    ', 药品=' || :NEW.DRUG_CODE || '(' || :NEW.DRUG_NAME || ')' ||
                    ', 规格=' || :NEW.DRUG_SPEC ||
                    ', 批号=' || :NEW.BATCH_NO ||
                    ', 原库存=' || :OLD.QUANTITY ||
                    ', 新库存=' || :NEW.QUANTITY ||
                    ', 操作时间=' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS');
    
    -- 插入到系统日志表（如果存在）
    BEGIN
        INSERT INTO SYSTEM_LOG (
            LOG_TIME,
            LOG_LEVEL,
            LOG_MODULE,
            LOG_MESSAGE,
            HIS_UNIT_CODE
        ) VALUES (
            SYSDATE,
            'WARNING',
            '药品库存管理',
            v_log_message,
            :NEW.HIS_UNIT_CODE
        );
    EXCEPTION
        WHEN OTHERS THEN
            -- 如果日志表不存在或插入失败，不影响主要操作
            NULL;
    END;
    
    -- 也可以写入到专门的负库存日志表
    BEGIN
        INSERT INTO DRUG_STOCK_NEGATIVE_LOG (
            LOG_TIME,
            STORAGE,
            DRUG_CODE,
            DRUG_NAME,
            DRUG_SPEC,
            BATCH_NO,
            OLD_QUANTITY,
            NEW_QUANTITY,
            HIS_UNIT_CODE
        ) VALUES (
            SYSDATE,
            :NEW.STORAGE,
            :NEW.DRUG_CODE,
            :NEW.DRUG_NAME,
            :NEW.DRUG_SPEC,
            :NEW.BATCH_NO,
            :OLD.QUANTITY,
            :NEW.QUANTITY,
            :NEW.HIS_UNIT_CODE
        );
    EXCEPTION
        WHEN OTHERS THEN
            -- 如果专门的日志表不存在，忽略错误
            NULL;
    END;
END;
/

-- 5. 创建负库存日志表（可选）
CREATE TABLE DRUG_STOCK_NEGATIVE_LOG (
    LOG_ID NUMBER GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    LOG_TIME DATE NOT NULL,
    STORAGE VARCHAR2(50),
    DRUG_CODE VARCHAR2(50),
    DRUG_NAME VARCHAR2(200),
    DRUG_SPEC VARCHAR2(100),
    BATCH_NO VARCHAR2(50),
    OLD_QUANTITY NUMBER(15,2),
    NEW_QUANTITY NUMBER(15,2),
    HIS_UNIT_CODE VARCHAR2(50)
);

-- 添加索引提高查询性能
CREATE INDEX IDX_DRUG_STOCK_NEG_LOG_TIME ON DRUG_STOCK_NEGATIVE_LOG(LOG_TIME);
CREATE INDEX IDX_DRUG_STOCK_NEG_LOG_DRUG ON DRUG_STOCK_NEGATIVE_LOG(DRUG_CODE, STORAGE);

-- 6. 修复现有负库存的建议SQL（谨慎执行）
/*
-- 方案A：将负库存设置为0（简单但可能丢失数据）
UPDATE DRUG_STOCK 
SET QUANTITY = 0, 
    LAST_UPDATETIME = SYSDATE 
WHERE QUANTITY < 0;

-- 方案B：将负库存记录移动到历史表，然后删除（推荐）
-- 1. 先备份负库存记录
CREATE TABLE DRUG_STOCK_NEGATIVE_BACKUP AS 
SELECT *, SYSDATE as BACKUP_TIME 
FROM DRUG_STOCK 
WHERE QUANTITY < 0;

-- 2. 删除负库存记录
DELETE FROM DRUG_STOCK WHERE QUANTITY < 0;
*/

-- 7. 监控查询：定期检查负库存情况
SELECT 
    TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') as 检查时间,
    COUNT(*) as 负库存记录数,
    MIN(QUANTITY) as 最小库存值,
    MAX(LAST_UPDATETIME) as 最新负库存时间
FROM DRUG_STOCK 
WHERE QUANTITY < 0;
