-- =====================================================
-- 启用统一库存验证参数配置脚本
-- 创建日期：2025-01-17
-- 目的：启用处方发药站统一库存验证功能，防止负库存
-- =====================================================

-- 1. 查看当前参数配置
SELECT 
    APP_NAME,
    DEPT_CODE,
    EMP_NO,
    PARAMETER_NAME,
    PARAMETER_VALUE,
    POSITION as 说明,
    HIS_UNIT_CODE
FROM COMM.APP_CONFIGER_PARAMETER
WHERE PARAMETER_NAME = 'ENABLE_UNIFIED_STOCK_VALIDATION'
  AND APP_NAME = 'PRESDISP';

-- 2. 如果参数不存在，则创建参数
INSERT INTO COMM.APP_CONFIGER_PARAMETER (
    APP_NAME,
    DEPT_CODE,
    EMP_NO,
    PARAMETER_NAME,
    PARAMETER_VALUE,
    POSITION,
    MEMO,
    HIS_UNIT_CODE
)
SELECT
    'PRESDISP',
    '*',
    '*',
    'ENABLE_UNIFIED_STOCK_VALIDATION',
    '1',  -- 启用统一库存验证
    '统一库存验证开关：1=启用（与门诊医生站逻辑一致），0=禁用（保持原有逻辑）',
    '防止负库存的重要参数',
    '45038900950011711A6001'  -- 请根据实际情况修改医院代码
FROM DUAL
WHERE NOT EXISTS (
    SELECT 1 FROM COMM.APP_CONFIGER_PARAMETER
    WHERE PARAMETER_NAME = 'ENABLE_UNIFIED_STOCK_VALIDATION'
      AND APP_NAME = 'PRESDISP'
      AND HIS_UNIT_CODE = '45038900950011711A6001'
);

-- 3. 如果参数已存在但未启用，则启用它
UPDATE COMM.APP_CONFIGER_PARAMETER 
SET PARAMETER_VALUE = '1',
    MEMO = '防止负库存 - 已启用统一库存验证'
WHERE PARAMETER_NAME = 'ENABLE_UNIFIED_STOCK_VALIDATION' 
  AND APP_NAME = 'PRESDISP'
  AND PARAMETER_VALUE != '1';

-- 4. 验证参数设置结果
SELECT 
    '参数设置结果' as 检查项目,
    APP_NAME,
    PARAMETER_NAME,
    PARAMETER_VALUE,
    CASE 
        WHEN PARAMETER_VALUE = '1' THEN '已启用'
        ELSE '未启用'
    END as 状态,
    HIS_UNIT_CODE
FROM COMM.APP_CONFIGER_PARAMETER
WHERE PARAMETER_NAME = 'ENABLE_UNIFIED_STOCK_VALIDATION'
  AND APP_NAME = 'PRESDISP';

-- 5. 提交更改
COMMIT;

-- 6. 重要提示
SELECT '重要提示：' as 提示,
       '参数修改后需要重启处方发药站程序才能生效！' as 说明
FROM DUAL;
