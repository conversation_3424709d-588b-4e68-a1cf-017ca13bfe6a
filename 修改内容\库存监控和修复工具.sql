-- =====================================================
-- 药品库存监控和修复工具
-- 创建日期：2025-01-17
-- 目的：监控和修复药品库存异常问题
-- =====================================================

-- 1. 当前负库存详细分析
WITH negative_stock_analysis AS (
    SELECT 
        STORAGE,
        DRUG_CODE,
        DRUG_NAME,
        DRUG_SPEC,
        PACKAGE_SPEC,
        FIRM_ID,
        COUNT(*) as 负库存批次数,
        SUM(QUANTITY) as 总负库存量,
        MIN(QUANTITY) as 最小库存值,
        MAX(LAST_UPDATETIME) as 最新更新时间
    FROM DRUG_STOCK 
    WHERE QUANTITY < 0
    GROUP BY STORAGE, DRUG_CODE, DRUG_NAME, DRUG_SPEC, PACKAGE_SPEC, FIRM_ID
)
SELECT 
    '负库存统计' as 分析项目,
    STORAGE as 药房,
    DRUG_NAME as 药品名称,
    DRUG_SPEC as 规格,
    负库存批次数,
    总负库存量,
    最小库存值,
    TO_CHAR(最新更新时间, 'YYYY-MM-DD HH24:MI:SS') as 最新更新时间
FROM negative_stock_analysis
ORDER BY 总负库存量 ASC, 最新更新时间 DESC;

-- 2. 查找可能的库存来源（同药品的正库存）
WITH negative_drugs AS (
    SELECT DISTINCT 
        STORAGE, DRUG_CODE, DRUG_SPEC, PACKAGE_SPEC, FIRM_ID
    FROM DRUG_STOCK 
    WHERE QUANTITY < 0
),
positive_stock AS (
    SELECT 
        n.STORAGE,
        n.DRUG_CODE,
        n.DRUG_SPEC,
        n.PACKAGE_SPEC,
        n.FIRM_ID,
        p.BATCH_NO,
        p.QUANTITY as 正库存量,
        p.EXPIRE_DATE
    FROM negative_drugs n
    JOIN DRUG_STOCK p ON (
        p.DRUG_CODE = n.DRUG_CODE 
        AND p.DRUG_SPEC = n.DRUG_SPEC
        AND p.PACKAGE_SPEC = n.PACKAGE_SPEC
        AND p.FIRM_ID = n.FIRM_ID
        AND p.STORAGE = n.STORAGE
        AND p.QUANTITY > 0
    )
)
SELECT 
    '可用于调整的正库存' as 分析项目,
    STORAGE as 药房,
    DRUG_CODE as 药品代码,
    DRUG_SPEC as 规格,
    COUNT(*) as 正库存批次数,
    SUM(正库存量) as 总正库存量,
    MIN(EXPIRE_DATE) as 最早失效期
FROM positive_stock
GROUP BY STORAGE, DRUG_CODE, DRUG_SPEC, PACKAGE_SPEC, FIRM_ID
ORDER BY 总正库存量 DESC;

-- 3. 库存异常检查（发现可能的数据问题）
SELECT 
    '库存异常检查' as 检查项目,
    STORAGE as 药房,
    DRUG_CODE as 药品代码,
    DRUG_NAME as 药品名称,
    COUNT(*) as 总批次数,
    COUNT(CASE WHEN QUANTITY > 0 THEN 1 END) as 正库存批次,
    COUNT(CASE WHEN QUANTITY < 0 THEN 1 END) as 负库存批次,
    COUNT(CASE WHEN QUANTITY = 0 THEN 1 END) as 零库存批次,
    SUM(QUANTITY) as 净库存量,
    CASE 
        WHEN SUM(QUANTITY) < 0 THEN '净库存为负'
        WHEN COUNT(CASE WHEN QUANTITY < 0 THEN 1 END) > 0 THEN '存在负库存批次'
        ELSE '正常'
    END as 状态
FROM DRUG_STOCK
GROUP BY STORAGE, DRUG_CODE, DRUG_NAME, DRUG_SPEC, PACKAGE_SPEC, FIRM_ID
HAVING COUNT(CASE WHEN QUANTITY < 0 THEN 1 END) > 0
   OR SUM(QUANTITY) < 0
ORDER BY SUM(QUANTITY) ASC;

-- 4. 最近的库存变动记录（如果有审计表）
/*
-- 如果系统有库存变动审计表，可以使用类似查询
SELECT 
    '最近库存变动' as 分析项目,
    OPERATION_TIME,
    STORAGE,
    DRUG_CODE,
    DRUG_NAME,
    BATCH_NO,
    OLD_QUANTITY,
    NEW_QUANTITY,
    QUANTITY_CHANGE,
    OPERATION_TYPE,
    OPERATOR
FROM DRUG_STOCK_AUDIT 
WHERE DRUG_CODE IN (
    SELECT DISTINCT DRUG_CODE 
    FROM DRUG_STOCK 
    WHERE QUANTITY < 0
)
AND OPERATION_TIME >= SYSDATE - 7  -- 最近7天
ORDER BY OPERATION_TIME DESC;
*/

-- 5. 生成修复建议
WITH repair_suggestions AS (
    SELECT 
        STORAGE,
        DRUG_CODE,
        DRUG_NAME,
        DRUG_SPEC,
        PACKAGE_SPEC,
        FIRM_ID,
        SUM(CASE WHEN QUANTITY < 0 THEN QUANTITY ELSE 0 END) as 负库存总量,
        SUM(CASE WHEN QUANTITY > 0 THEN QUANTITY ELSE 0 END) as 正库存总量,
        COUNT(CASE WHEN QUANTITY < 0 THEN 1 END) as 负库存批次数
    FROM DRUG_STOCK
    GROUP BY STORAGE, DRUG_CODE, DRUG_NAME, DRUG_SPEC, PACKAGE_SPEC, FIRM_ID
    HAVING SUM(CASE WHEN QUANTITY < 0 THEN QUANTITY ELSE 0 END) < 0
)
SELECT 
    '修复建议' as 建议类型,
    STORAGE as 药房,
    DRUG_NAME as 药品名称,
    负库存总量,
    正库存总量,
    负库存批次数,
    CASE 
        WHEN 正库存总量 + 负库存总量 >= 0 THEN '可通过批次调整修复'
        WHEN 正库存总量 > 0 THEN '需要盘点确认实际库存'
        ELSE '需要重新入库或调账'
    END as 修复建议,
    CASE 
        WHEN 正库存总量 + 负库存总量 >= 0 THEN 
            '建议：删除负库存记录，保留正库存记录'
        ELSE 
            '警告：该药品净库存为负，需要紧急处理'
    END as 具体操作
FROM repair_suggestions
ORDER BY 负库存总量 ASC;

-- 6. 生成修复SQL（谨慎执行）
/*
-- 修复方案A：对于净库存为正的药品，删除负库存记录
DELETE FROM DRUG_STOCK 
WHERE QUANTITY < 0
  AND (STORAGE, DRUG_CODE, DRUG_SPEC, PACKAGE_SPEC, FIRM_ID) IN (
    SELECT STORAGE, DRUG_CODE, DRUG_SPEC, PACKAGE_SPEC, FIRM_ID
    FROM DRUG_STOCK
    GROUP BY STORAGE, DRUG_CODE, DRUG_SPEC, PACKAGE_SPEC, FIRM_ID
    HAVING SUM(QUANTITY) >= 0
  );

-- 修复方案B：对于净库存为负的药品，将所有库存设为0
UPDATE DRUG_STOCK 
SET QUANTITY = 0, LAST_UPDATETIME = SYSDATE
WHERE (STORAGE, DRUG_CODE, DRUG_SPEC, PACKAGE_SPEC, FIRM_ID) IN (
    SELECT STORAGE, DRUG_CODE, DRUG_SPEC, PACKAGE_SPEC, FIRM_ID
    FROM DRUG_STOCK
    GROUP BY STORAGE, DRUG_CODE, DRUG_SPEC, PACKAGE_SPEC, FIRM_ID
    HAVING SUM(QUANTITY) < 0
  );
*/

-- 7. 创建定期监控视图
CREATE OR REPLACE VIEW V_DRUG_STOCK_MONITOR AS
SELECT 
    SYSDATE as 检查时间,
    '负库存监控' as 监控项目,
    COUNT(*) as 负库存记录数,
    COUNT(DISTINCT STORAGE || DRUG_CODE) as 涉及药品数,
    SUM(QUANTITY) as 负库存总量,
    MIN(QUANTITY) as 最小库存值
FROM DRUG_STOCK 
WHERE QUANTITY < 0
UNION ALL
SELECT 
    SYSDATE as 检查时间,
    '零库存监控' as 监控项目,
    COUNT(*) as 零库存记录数,
    COUNT(DISTINCT STORAGE || DRUG_CODE) as 涉及药品数,
    0 as 库存总量,
    0 as 最小库存值
FROM DRUG_STOCK 
WHERE QUANTITY = 0;

-- 8. 使用监控视图
SELECT * FROM V_DRUG_STOCK_MONITOR;
