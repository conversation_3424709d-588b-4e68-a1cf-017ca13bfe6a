﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Script.Serialization;
using System.Windows.Forms;
using DevExpress.XtraEditors; 
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PlatCommon.SysBase;
using TjhisInterfaceInsurance;

namespace Tjhis.InsurManager.Station.Insur
{
    public partial class FrmInsur1312 : ParentForm
    {
        public FrmInsur1312()
        {
            InitializeComponent();
        }

        private void barLargeButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                DataTable dt_data = new DataTable();
                SetTable(ref dt_data);
                DataRow drdata = dt_data.NewRow();
                string ls_query = "";
                try
                {
                    ls_query = barEditItem1.EditValue.ToString();
                }
                catch (Exception ex)
                { }
                if (!string.IsNullOrEmpty(ls_query))
                {
                    ls_query = DateTime.Parse(ls_query).ToString("yyyy-MM-dd HH:mm:ss");
                }
                else
                {
                    // 如果没有输入时间，使用当前时间
                    ls_query = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                }
                drdata["query_date"] = ls_query;
                drdata["fixmedins_code"] = "H50011702621";
                drdata["medins_list_codg"] = "";
                drdata["medins_list_name"] = "";
                drdata["insu_admdvs"] = ls_query;
                drdata["list_type"] = barEditItem2.EditValue.ToString();
                drdata["med_list_codg"] = "";
                drdata["begndate"] = "";
                drdata["vali_flag"] = "";
                drdata["updt_time"] = ls_query;
                drdata["page_num"] = barEditItem3.EditValue.ToString();
                drdata["page_size"] = barEditItem4.EditValue.ToString();
                drdata["JYLX"] = "1317";

                // 添加日志记录，用于调试
                System.IO.File.AppendAllText(@"..\Client\LOG\exLOG\医保1312查询_" + DateTime.Now.ToString("yyyyMMdd") + ".log",
                    $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [INFO] [医保1312查询] updt_time参数值: {ls_query}" + Environment.NewLine);

                dt_data.Rows.Add(drdata);
                DataSet ds = new DataSet();
                ds.Tables.Add(dt_data);
                string remark = "";

                string strCHARGE_TYPE = "平台医保";

                if (MedicalInterface.MedicalBusinessHandle("021", "", "", strCHARGE_TYPE, PlatCommon.SysBase.SystemParm.LoginUser.ID, "", remark, ds) >= 0)
                {
                    // MessageBox.Show("医保住院登记撤销成功", "提示");
                    string outp_put = MedicalInterface.gs_output;
                    JObject jo = (JObject)JsonConvert.DeserializeObject(outp_put);
                    string data = jo["data"].ToString();
                    DataTable dt_da = ToDataTable(data);
                    gridControl1.DataSource = dt_da;

                }
                else
                {
                    //MessageBox.Show("下载数据失败！", "提示");
                    return;
                }
            }
            catch (Exception ex)
            {
            }
        }
        public DataTable ToDataTable(string json)
        {
            DataTable dataTable = new DataTable();  //实例化
            DataTable result;
            JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
            javaScriptSerializer.MaxJsonLength = Int32.MaxValue; //取得最大数值
            ArrayList arrayList = javaScriptSerializer.Deserialize<ArrayList>(json);
            if (arrayList.Count > 0)
            {
                foreach (Dictionary<string, object> dictionary in arrayList)
                {
                    //if (dictionary.Keys.Count<string>() == 0)
                    if (dictionary.Keys.Count == 0)
                    {
                        result = dataTable;
                        return result;
                    }
                    if (dataTable.Columns.Count == 0)
                    {
                        foreach (string current in dictionary.Keys)
                        {
                            dataTable.Columns.Add(current, dictionary[current].GetType());
                        }
                    }
                    DataRow dataRow = dataTable.NewRow();
                    foreach (string current in dictionary.Keys)
                    {
                        dataRow[current] = dictionary[current];
                    }

                    dataTable.Rows.Add(dataRow);
                }
            }
            result = dataTable;
            return result;
        }
        private void AddColumns_grid(ref DataTable listdt)
        {
            listdt.Columns.Add("A1");//
            listdt.Columns.Add("A2");//
            listdt.Columns.Add("A3");//
            listdt.Columns.Add("A4");//
            listdt.Columns.Add("A5");//
            listdt.Columns.Add("A6");//
            listdt.Columns.Add("A7");//
            listdt.Columns.Add("A8");//
            listdt.Columns.Add("A9");//
            listdt.Columns.Add("A10");//
            listdt.Columns.Add("A11");//
            listdt.Columns.Add("A12");//
            listdt.Columns.Add("A13");//
            listdt.Columns.Add("A14");//
            listdt.Columns.Add("A15");//
            listdt.Columns.Add("A16");//
            listdt.Columns.Add("A17");//
            listdt.Columns.Add("A18");//
            listdt.Columns.Add("A19");//
            listdt.Columns.Add("A20");//
            listdt.Columns.Add("A21");//
            listdt.Columns.Add("A22");//
            listdt.Columns.Add("A23");//
            listdt.Columns.Add("A24");//
        }
        private void SetTable(ref DataTable dt_data)
        {
            dt_data.Columns.Add("query_date");
            dt_data.Columns.Add("fixmedins_code");
            dt_data.Columns.Add("medins_list_codg");
            dt_data.Columns.Add("medins_list_name");
            dt_data.Columns.Add("insu_admdvs");
            dt_data.Columns.Add("list_type");
            dt_data.Columns.Add("med_list_codg");
            dt_data.Columns.Add("begndate");
            dt_data.Columns.Add("vali_flag");
            dt_data.Columns.Add("updt_time");
            dt_data.Columns.Add("page_num");
            dt_data.Columns.Add("page_size");
            dt_data.Columns.Add("JYLX");

        }

        private void frmInsurVsDownPt_Load(object sender, EventArgs e)
        {
            //目录类别
            DataTable dt_mllx = ClassInsurPublic.GetTjInsuranceDict("ptyb", "list_type");
            DataTable dt_mllx1 = dt_mllx.DefaultView.ToTable(false, new string[] { "CODE", "NAME" });
            lupListType.Properties.DataSource = dt_mllx1;
            lupListType.Properties.ValueMember = "CODE";
            lupListType.Properties.DisplayMember = "NAME";

            lupListType1.Properties.DataSource = dt_mllx1;
            lupListType1.Properties.ValueMember = "CODE";
            lupListType1.Properties.DisplayMember = "NAME";

            //医保目录限价类型
            DataTable dt_mlxjlx = ClassInsurPublic.GetTjInsuranceDict("ptyb", "hilist_lmtpric_type");
            DataTable dt_mlxjlx1 = dt_mlxjlx.DefaultView.ToTable(false, new string[] { "CODE", "NAME" });
            lupybmuxjType.Properties.DataSource = dt_mlxjlx1;
            lupybmuxjType.Properties.ValueMember = "CODE";
            lupybmuxjType.Properties.DisplayMember = "NAME";

            //医保目录自付比例人员类别 lupYBMLZFBLRY
            DataTable dt_mlzfblrylb = ClassInsurPublic.GetTjInsuranceDict("ptyb", "selfpay_prop_psn_type");
            DataTable dt_mlzfblrylb1 = dt_mlzfblrylb.DefaultView.ToTable(false, new string[] { "CODE", "NAME" });
            lupYBMLZFBLRY.Properties.DataSource = dt_mlzfblrylb1;
            lupYBMLZFBLRY.Properties.ValueMember = "CODE";
            lupYBMLZFBLRY.Properties.DisplayMember = "NAME";

            //目录自付比例类别 lupMlzfbl
            DataTable dt_mlzfbl = ClassInsurPublic.GetTjInsuranceDict("ptyb", "selfpay_prop_type");
            DataTable dt_mlzfbl1 = dt_mlzfbl.DefaultView.ToTable(false, new string[] { "CODE", "NAME" });
            lupMlzfbl.Properties.DataSource = dt_mlzfbl1;
            lupMlzfbl.Properties.ValueMember = "CODE";
            lupMlzfbl.Properties.DisplayMember = "NAME";

            //lupYLSFXMType 医疗收费项目类别
            DataTable dt_ylsfxmlb = ClassInsurPublic.GetTjInsuranceDict("ptyb", "med_chrgitm_type");
            DataTable dt_ylsfxmlb1 = dt_ylsfxmlb.DefaultView.ToTable(false, new string[] { "CODE", "NAME" });
            lupYLSFXMType.Properties.DataSource = dt_ylsfxmlb1;
            lupYLSFXMType.Properties.ValueMember = "CODE";
            lupYLSFXMType.Properties.DisplayMember = "NAME";


            //lupSFXMDJ 医疗收费项目类别
            DataTable dt_sfxmdj = ClassInsurPublic.GetTjInsuranceDict("ptyb", "chrgitm_lv");
            DataTable dt_sfxmdj1 = dt_sfxmdj.DefaultView.ToTable(false, new string[] { "CODE", "NAME" });
            lupSFXMDJ.Properties.DataSource = dt_sfxmdj1;
            lupSFXMDJ.Properties.ValueMember = "CODE";
            lupSFXMDJ.Properties.DisplayMember = "NAME";

            //
            DataTable dt_1901_type = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select distinct a.insur_type from insurance.tj_insurance_dict a  where a.interfacecode='ptyb'").Tables[0];
            slu1901Type.Properties.DataSource = dt_1901_type;
            slu1901Type.Properties.ValueMember = "INSUR_TYPE";
            slu1901Type.Properties.DisplayMember = "INSUR_TYPE";

            //comboBox1.EditValue = "平台医保";
            comboBox1.EditValue = "市医保";
        }

        /// <summary>
        /// 1317医药机构目录匹配信息查询
        /// date:2021-08-27  author:sxr
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn1317_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable dt_data = new DataTable();
                SetTable(ref dt_data);
                DataRow drdata = dt_data.NewRow();
                string ls_query = "";
                try
                {
                    ls_query = barEditItem1.EditValue == null ? "" : barEditItem1.EditValue.ToString();
                }
                catch (Exception ex)
                { }
                if (!string.IsNullOrEmpty(ls_query))
                {
                    ls_query = DateTime.Parse(ls_query).ToString("yyyy-MM-dd HH:mm:ss");
                }
                drdata["query_date"] = "";//查询时间点
                drdata["fixmedins_code"] = "H50011702621";// "荣军医院";//定点医药机构编号
                drdata["medins_list_codg"] = "";//定点医药机构目录编号
                drdata["medins_list_name"] = "";//定点医药机构目录名称
                drdata["insu_admdvs"] = "";//参保机构医保区划
                drdata["list_type"] = this.lupListType.EditValue != null ? this.lupListType.EditValue.ToString() : "";//目录类别
                drdata["med_list_codg"] = "";//医疗目录编码
                drdata["begndate"] = "";//开始日期
                drdata["vali_flag"] = "";//有效标志
                drdata["updt_time"] = ls_query;//更新时间
                drdata["page_num"] = barEditItem3.EditValue.ToString();//当前页数
                drdata["page_size"] = barEditItem4.EditValue.ToString();//本页数据量
                drdata["JYLX"] = "1317";

                dt_data.Rows.Add(drdata);
                DataSet ds = new DataSet();
                ds.Tables.Add(dt_data);
                string remark = "";

                string strCHARGE_TYPE = "平台医保";

                if (MedicalInterface.MedicalBusinessHandle("021", "", "", strCHARGE_TYPE, PlatCommon.SysBase.SystemParm.LoginUser.ID, "", remark, ds) >= 0)
                {
                    string outp_put = MedicalInterface.gs_output;
                    JObject jo = (JObject)JsonConvert.DeserializeObject(outp_put);
                    string data = jo["data"].ToString();
                    string strLastPage = jo["lastPage"].ToString();//是否是最后一页

                    DataTable dtdata = ToDataTableTwo(data); //JsonToDT(output);//转换为datatalbe 
                    gridControl1.DataSource = dtdata;
                    gridView1.BestFitColumns();
                    if (strLastPage.ToLower().Equals("true"))
                    {
                        MessageBox.Show("已经是医保返回的最后一页！");
                        return;
                    }
                }
                else
                {
                    //MessageBox.Show("下载数据失败！", "提示");
                    return;
                }
            }
            catch (Exception ex)
            {
            }
        }



        /// <summary>
        /// Json 字符串 转换为 DataTable数据集合
        /// [{"mac":"20:f1:7c:c5:cd:80","rssi":"-86","ch":"9"},{"mac":"20:f1:7c:c5:cd:85","rssi":"-91","ch":"9"}]
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public static DataTable ToDataTableTwo(string json)
        {
            DataTable dataTable = new DataTable();  //实例化
            DataTable result;
            try
            {
                JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
                javaScriptSerializer.MaxJsonLength = Int32.MaxValue; //取得最大数值
                ArrayList arrayList = javaScriptSerializer.Deserialize<ArrayList>(json);
                if (arrayList.Count > 0)
                {
                    foreach (Dictionary<string, object> dictionary in arrayList)
                    {
                        if (dictionary.Keys.Count<string>() == 0)
                        {
                            result = dataTable;
                            return result;
                        }
                        //Columns
                        //if (dataTable.Columns.Count == 0)
                        //{
                            foreach (string current in dictionary.Keys)
                            {
                            if (!dataTable.Columns.Contains(current))
                            {
                                dataTable.Columns.Add(current.ToUpper(), typeof(string));
                            }
                            }
                        //}
                        //Rows
                        DataRow dataRow = dataTable.NewRow();
                        foreach (string current in dictionary.Keys)
                        {
                            dataRow[current] = dictionary[current];
                        }
                        dataTable.Rows.Add(dataRow); //循环添加行到DataTable中
                    }
                }
            }
            catch(Exception e)
            {
                MessageBox.Show("下载数据失败！", e.Message);
            }
            result = dataTable;
            return result;
        }


        private void SetTable1316(ref DataTable dt_data)
        {
            dt_data.Columns.Add("query_date");
            dt_data.Columns.Add("med_list_codg");
            dt_data.Columns.Add("hilist_code");
            dt_data.Columns.Add("list_type");
            dt_data.Columns.Add("insu_admdvs");
            dt_data.Columns.Add("begndate");
            dt_data.Columns.Add("vali_flag");
            dt_data.Columns.Add("enddate");
            dt_data.Columns.Add("updt_time");
            dt_data.Columns.Add("page_num");
            dt_data.Columns.Add("page_size");
            dt_data.Columns.Add("JYLX");

        }


        private void SetTable1318(ref DataTable dt_data)
        {
            dt_data.Columns.Add("query_date");
            //dt_data.Columns.Add("med_list_codg");
            dt_data.Columns.Add("hilist_code");//医保目录编码
            dt_data.Columns.Add("hilist_lmtpric_type");//医保目录限价类型
            dt_data.Columns.Add("overlmt_dspo_way");//医保目录超限处理方式
            dt_data.Columns.Add("insu_admdvs");//参保机构医保区划
            dt_data.Columns.Add("begndate");
            dt_data.Columns.Add("vali_flag");
            dt_data.Columns.Add("enddate");
            dt_data.Columns.Add("rid");
            dt_data.Columns.Add("tabname");
            dt_data.Columns.Add("poolarea_no");
            dt_data.Columns.Add("updt_time");
            dt_data.Columns.Add("page_num");
            dt_data.Columns.Add("page_size");
            dt_data.Columns.Add("JYLX");

        }

        private void SetTable1319(ref DataTable dt_data)
        {
            dt_data.Columns.Add("query_date");
            dt_data.Columns.Add("hilist_code");//医保目录编码
            dt_data.Columns.Add("selfpay_prop_psn_type");//医保目录自付比例人员类别
            dt_data.Columns.Add("selfpay_prop_type");//目录自付比例类别
            dt_data.Columns.Add("insu_admdvs");//参保机构医保区划
            dt_data.Columns.Add("begndate");
            dt_data.Columns.Add("vali_flag");
            dt_data.Columns.Add("enddate");
            dt_data.Columns.Add("rid");
            dt_data.Columns.Add("tabname");
            dt_data.Columns.Add("poolarea_no");
            dt_data.Columns.Add("updt_time");
            dt_data.Columns.Add("page_num");
            dt_data.Columns.Add("page_size");
            dt_data.Columns.Add("JYLX");

        }

        private void SetTable1312(ref DataTable dt_data)
        {
            dt_data.Columns.Add("query_date");
            dt_data.Columns.Add("hilist_code");//医保目录编码
            dt_data.Columns.Add("insu_admdvs");//参保机构医保区划
            dt_data.Columns.Add("begndate");
            dt_data.Columns.Add("hilist_name");//医保目录名称
            dt_data.Columns.Add("wubi");//五笔助记码
            dt_data.Columns.Add("pinyin");//拼音助记码
            dt_data.Columns.Add("med_chrgitm_type");//医疗收费项目类别
            dt_data.Columns.Add("chrgitm_lv");//收费项目等级
            dt_data.Columns.Add("lmt_used_flag");//限制使用标志
            dt_data.Columns.Add("list_type");//目录类别
            dt_data.Columns.Add("med_use_flag");//医疗使用标志
            dt_data.Columns.Add("matn_used_flag");//生育使用标志
            dt_data.Columns.Add("hilist_use_type");//医保目录使用类别
            dt_data.Columns.Add("lmt_cpnd_type");//限复方使用类型
            dt_data.Columns.Add("vali_flag");
            dt_data.Columns.Add("updt_time");
            dt_data.Columns.Add("page_num");
            dt_data.Columns.Add("page_size");
            dt_data.Columns.Add("JYLX");

        }
        private void SetTable1901(ref DataTable dt_data)
        {
            dt_data.Columns.Add("type");
            dt_data.Columns.Add("parent_value");
            dt_data.Columns.Add("admdvs");
            dt_data.Columns.Add("date");
            dt_data.Columns.Add("vali_flag");
            dt_data.Columns.Add("JYLX");
        }

        /// <summary>
        /// 1316 医疗目录与医保目录匹配信息查询 
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn1316_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable dt_data = new DataTable();
                SetTable1316(ref dt_data);
                DataRow drdata = dt_data.NewRow();
                string ls_query = "";
                try
                {
                    ls_query = barEditItem1.EditValue.ToString();
                }
                catch (Exception ex)
                { }
                if (!string.IsNullOrEmpty(ls_query))
                {
                    ls_query = DateTime.Parse(ls_query).ToString("yyyy-MM-dd HH:mm:ss");
                }
                drdata["query_date"] = "";//查询时间点
                drdata["med_list_codg"] = ""; //定点医药机构目录编号
                drdata["hilist_code"] = barEditItem8.EditValue== null ? "" : barEditItem8.EditValue;//医保目录编码
                drdata["list_type"] = this.lupListType1.EditValue != null ? this.lupListType1.EditValue.ToString() : "";//目录类别
                drdata["insu_admdvs"] = "";//参保机构医保区划
                drdata["begndate"] = "";//开始日期
                drdata["vali_flag"] = "";//有效标志
                drdata["updt_time"] = ls_query;//更新时间
                drdata["page_num"] = barEditItem3.EditValue.ToString();//当前页数
                drdata["page_size"] = barEditItem4.EditValue.ToString();//本页数据量
                drdata["JYLX"] = "1316";

                dt_data.Rows.Add(drdata);
                DataSet ds = new DataSet();
                ds.Tables.Add(dt_data);
                string remark = "";

                string strCHARGE_TYPE = "平台医保";

                if (MedicalInterface.MedicalBusinessHandle("021", "", "", strCHARGE_TYPE, PlatCommon.SysBase.SystemParm.LoginUser.ID, "", remark, ds) >= 0)
                {
                    string outp_put = MedicalInterface.gs_output;
                    JObject jo = (JObject)JsonConvert.DeserializeObject(outp_put);
                    string data = jo["data"].ToString();
                    string strLastPage = jo["lastPage"].ToString();//是否是最后一页

                    DataTable dtdata = ToDataTableTwo(data); //JsonToDT(output);//转换为datatalbe 
                    gridControl2.DataSource = dtdata;
                    gridView2.BestFitColumns();
                    if (strLastPage.ToLower().Equals("true"))
                    {
                        MessageBox.Show("已经是医保返回的最后一页！");
                        return;
                    }
                }
                else
                {
                    //MessageBox.Show("下载数据失败！", "提示");
                    return;
                }
            }
            catch (Exception ex)
            {
            }
        }

        DataTable dt_dict1318;
        DataTable dt_dict1319;
        /// <summary>
        /// 医保目录限价信息查询
        /// date：2021-08-28   author：sxr
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn1318_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable dt_data = new DataTable();
                SetTable1318(ref dt_data);
                DataRow drdata = dt_data.NewRow();
                string ls_query = "";
                try
                {
                    ls_query = barEditItem1.EditValue.ToString();
                }
                catch (Exception ex)
                { }
                if (!string.IsNullOrEmpty(ls_query))
                {
                    ls_query = DateTime.Parse(ls_query).ToString("yyyy-MM-dd HH:mm:ss");
                }


                string ls_savetable = "INSURANCE.insur_Dict1318_PTYB" ;
                string sql = $"select * from {ls_savetable} where 1 = 0";
                dt_dict1318 = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];

                drdata["query_date"] = "";//查询时间点
                drdata["hilist_code"] = barEditItem8.EditValue == null ? "" : barEditItem8.EditValue;//医保目录编码
                // hilist_lmtpric_type  医保目录限价类型  
                drdata["hilist_lmtpric_type"] = this.lupybmuxjType.EditValue != null ? this.lupybmuxjType.EditValue.ToString() : ""; ; //医保目录限价类型

                drdata["overlmt_dspo_way"] = "";//医保目录超限处理方式
                drdata["insu_admdvs"] = "";//参保机构医保区划

                drdata["begndate"] = "";//开始日期
                drdata["enddate"] = "";//结束日期
                drdata["vali_flag"] = "";//有效标志
                drdata["rid"] = "";//唯一记录号
                drdata["tabname"] = "";//表名
                drdata["poolarea_no"] = "";//统筹区
                drdata["updt_time"] = ls_query;//更新时间
                drdata["page_num"] = barEditItem3.EditValue.ToString();//当前页数
                drdata["page_size"] = barEditItem4.EditValue.ToString();//本页数据量
                drdata["JYLX"] = "1318";

                dt_data.Rows.Add(drdata);
                DataSet ds = new DataSet();
                ds.Tables.Add(dt_data);
                string remark = "";

                string strCHARGE_TYPE = $"{comboBox1.EditValue}";

                if (MedicalInterface.MedicalBusinessHandle("021", "", "", strCHARGE_TYPE, PlatCommon.SysBase.SystemParm.LoginUser.ID, "", remark, ds) >= 0)
                {
                    string outp_put = MedicalInterface.gs_output;
                    JObject jo = (JObject)JsonConvert.DeserializeObject(outp_put);
                    string data = jo["data"].ToString();
                    string strLastPage = jo["lastPage"].ToString();//是否是最后一页


                    if (!string.IsNullOrEmpty(data))
                    {
                        DataTable dtdata = ToDataTableTwo(data);
                        foreach (DataRow item in dtdata.Rows)
                        {
                            DataRow dr = dt_dict1318.NewRow();
                            dr["HILIST_CODE"] = item["HILIST_CODE"];
                            dr["HILIST_LMTPRIC_TYPE"] = item["HILIST_LMTPRIC_TYPE"];
                            dr["OVERLMT_DSPO_WAY"] = item["OVERLMT_DSPO_WAY"];
                            dr["INSU_ADMDVS"] = item["INSU_ADMDVS"];
                            dr["BEGNDATE"] = item["BEGNDATE"];
                            dr["ENDDATE"] = item["ENDDATE"];
                            dr["HILIST_PRIC_UPLMT_AMT"] = item["HILIST_PRIC_UPLMT_AMT"];
                            dr["VALI_FLAG"] = item["VALI_FLAG"];
                            dr["RID"] = item["RID"];
                            dr["UPDT_TIME"] = item["UPDT_TIME"];
                            dr["CRTER_ID"] = item["CRTER_ID"];
                            dr["CRTER_NAME"] = item["CRTER_NAME"];
                            dr["CRTE_TIME"] = item["CRTE_TIME"];
                            dr["CRTE_OPTINS_NO"] = item["CRTE_OPTINS_NO"];
                            dr["OPTER_ID"] = item["OPTER_ID"];
                            dr["OPTER_NAME"] = item["OPTER_NAME"];
                            dr["OPT_TIME"] = item["OPT_TIME"];
                            dr["OPTINS_NO"] = item["OPTINS_NO"];
                            dr["TABNAME"] = item["TABNAME"];
                            dr["POOLAREA_NO"] = item["POOLAREA_NO"];
                            dt_dict1318.Rows.Add(dr);
                        }
                        gridControl3.DataSource = dt_dict1318;
                    }
                    
                    if (strLastPage.ToLower().Equals("true"))
                    {
                        MessageBox.Show("已经是医保返回的最后一页！");
                        return;
                    }
                }
                else
                {
                    //MessageBox.Show("下载数据失败！", "提示");
                    return;
                }
            }
            catch (Exception ex)
            {
            }
        }

        /// <summary>
        /// 1319 医保目录先自付比例信息查询
        /// date:2021-08-30   author:sxr
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton1_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable dt_data = new DataTable();
                SetTable1319(ref dt_data);
                DataRow drdata = dt_data.NewRow();
                string ls_query = "";
                try
                {
                    ls_query = barEditItem1.EditValue.ToString();
                }
                catch (Exception ex)
                { }
                if (!string.IsNullOrEmpty(ls_query))
                {
                    ls_query = DateTime.Parse(ls_query).ToString("yyyy-MM-dd HH:mm:ss");
                }

                string ls_savetable ="INSURANCE.insur_Dict1319_PTYB" ;
                string sql = $"select * from {ls_savetable} where 1 = 0";
                dt_dict1319 = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];

                drdata["query_date"] = "";//查询时间点
                drdata["hilist_code"] = barEditItem8.EditValue == null ? "" : barEditItem8.EditValue;//医保目录编码
                drdata["selfpay_prop_psn_type"] = this.lupYBMLZFBLRY.EditValue != null ? this.lupYBMLZFBLRY.EditValue.ToString() : "";//医保目录自付比例人员类别
                drdata["selfpay_prop_type"] = this.lupMlzfbl.EditValue != null ? this.lupMlzfbl.EditValue.ToString() : "";//目录自付比例类别
                drdata["insu_admdvs"] = "";//参保机构医保区划
                drdata["begndate"] = "";//开始日期
                drdata["enddate"] = "";//结束日期
                drdata["vali_flag"] = "";//有效标志
                drdata["rid"] = "";//唯一记录号
                drdata["tabname"] = "";//表名
                drdata["poolarea_no"] = "";//统筹区
                drdata["updt_time"] = ls_query;//更新时间
                drdata["page_num"] = barEditItem3.EditValue.ToString();//当前页数
                drdata["page_size"] = barEditItem4.EditValue.ToString();//本页数据量
                drdata["JYLX"] = "1319";

                dt_data.Rows.Add(drdata);
                DataSet ds = new DataSet();
                ds.Tables.Add(dt_data);
                string remark = "";

                string strCHARGE_TYPE = $"{comboBox1.EditValue}";

                if (MedicalInterface.MedicalBusinessHandle("021", "", "", strCHARGE_TYPE, PlatCommon.SysBase.SystemParm.LoginUser.ID, "", remark, ds) >= 0)
                {
                    string outp_put = MedicalInterface.gs_output;
                    JObject jo = (JObject)JsonConvert.DeserializeObject(outp_put);
                    string data = jo["data"].ToString();
                    string strLastPage = jo["lastPage"].ToString();//是否是最后一页


                    if (!string.IsNullOrEmpty(data))
                    {
                        DataTable dtdata = ToDataTableTwo(data);
                        foreach (DataRow item in dtdata.Rows)
                        {
                            DataRow dr = dt_dict1319.NewRow();
                            dr["HILIST_CODE"] = item["HILIST_CODE"];
                            dr["SELFPAY_PROP_PSN_TYPE"] = item["SELFPAY_PROP_PSN_TYPE"];
                            dr["SELFPAY_PROP_TYPE"] = item["SELFPAY_PROP_TYPE"];
                            dr["INSU_ADMDVS"] = item["INSU_ADMDVS"];
                            dr["BEGNDATE"] = item["BEGNDATE"];
                            dr["ENDDATE"] = item["ENDDATE"];
                            dr["SELFPAY_PROP"] = item["SELFPAY_PROP"];
                            dr["VALI_FLAG"] = item["VALI_FLAG"];
                            dr["RID"] = item["RID"];
                            dr["UPDT_TIME"] = item["UPDT_TIME"];
                            dr["CRTER_ID"] = item["CRTER_ID"];
                            dr["CRTER_NAME"] = item["CRTER_NAME"];
                            dr["CRTE_TIME"] = item["CRTE_TIME"];
                            dr["CRTE_OPTINS_NO"] = item["CRTE_OPTINS_NO"];
                            dr["OPTER_ID"] = item["OPTER_ID"];
                            dr["OPTER_NAME"] = item["OPTER_NAME"];
                            dr["OPT_TIME"] = item["OPT_TIME"];
                            dr["OPTINS_NO"] = item["OPTINS_NO"];
                            dr["TABNAME"] = item["TABNAME"];
                            dr["POOLAREA_NO"] = item["POOLAREA_NO"];
                            dt_dict1319.Rows.Add(dr);
                        }
                        gridControl4.DataSource = dt_dict1319;
                    }


                    //DataTable dtdata = ToDataTableTwo(data); //JsonToDT(output);//转换为datatalbe 
                    //gridControl4.DataSource = dtdata;
                    //gridView4.BestFitColumns();
                    if (strLastPage.ToLower().Equals("true"))
                    {
                        MessageBox.Show("已经是医保返回的最后一页！");
                        return;
                    }
                    else
                    {
                        MessageBox.Show("不是医保返回的最后一页！");
                        return;
                    }
                }
                else
                {
                    //MessageBox.Show("下载数据失败！", "提示");
                    return;
                }
            }
            catch (Exception ex)
            {
            }
        }

        private void btn1312_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable dt_data = new DataTable();
                SetTable1312(ref dt_data);
                DataRow drdata = dt_data.NewRow();
                string ls_query = "";
                try
                {
                    ls_query = barEditItem1.EditValue == null ? "" : barEditItem1.EditValue.ToString();
                }
                catch (Exception ex)
                { }
                if (!string.IsNullOrEmpty(ls_query))
                {
                    ls_query = DateTime.Parse(ls_query).ToString("yyyy-MM-dd HH:mm:ss");
                   // ls_query = DateTime.Parse(ls_query).ToString("yyyy-MM-dd");
                }
                else
                {
                    // 如果没有输入时间，使用当前时间
                    ls_query = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                }
                //drdata["query_date"] = ls_query;//查询时间点
                //drdata["fixmedins_code"] = "H50011702621";// "荣军医院";//定点医药机构编号
                drdata["hilist_code"] = barEditItem8.EditValue == null ? "" : barEditItem8.EditValue;//医保目录编码
                drdata["insu_admdvs"] = "";//参保机构医保区划
                drdata["begndate"] = "";//开始日期
                drdata["hilist_name"] = mlmc.EditValue == null ? "" : mlmc.EditValue.ToString();//医保目录名称
                drdata["wubi"] = "";//五笔助记码
                drdata["pinyin"] = "";//拼音助记码
                drdata["med_chrgitm_type"] = this.lupYLSFXMType.EditValue != null ? this.lupYLSFXMType.EditValue.ToString() : "";//医疗收费项目类别
                drdata["chrgitm_lv"] = this.lupSFXMDJ.EditValue != null ? this.lupSFXMDJ.EditValue.ToString() : "";//收费项目等级

                drdata["lmt_used_flag"] = "";//限制使用标志
                drdata["list_type"] = "";//目录类别
                drdata["med_use_flag"] = "";//医疗使用标志
                drdata["matn_used_flag"] = "";//生育使用标志
                drdata["hilist_use_type"] = "";//医保目录使用类别
                drdata["lmt_cpnd_type"] = "";//限复方使用类型
                drdata["vali_flag"] = "1";//有效标志
                drdata["updt_time"] = ls_query;//更新时间
                drdata["page_num"] = barEditItem3.EditValue.ToString();//当前页数
                drdata["page_size"] = barEditItem4.EditValue.ToString();//本页数据量
                drdata["JYLX"] = "1312";

                dt_data.Rows.Add(drdata);
                DataSet ds = new DataSet();
                ds.Tables.Add(dt_data);
                string remark = "";

                string strCHARGE_TYPE = $"{comboBox1.EditValue}";
                if (barCheckItem2.Checked) //循环下载
                {
                    bool flag = true;
                    DevExpress.Utils.WaitDialogForm frm = new DevExpress.Utils.WaitDialogForm();
                    while (flag)
                    {
                        if (MedicalInterface.MedicalBusinessHandle("021", "", "", strCHARGE_TYPE, PlatCommon.SysBase.SystemParm.LoginUser.ID, "", remark, ds) >= 0)
                        {
                            string outp_put = MedicalInterface.gs_output;
                            JObject jo = (JObject)JsonConvert.DeserializeObject(outp_put);
                            string data = jo["data"].ToString();
                            if (data.Equals("[]"))
                            {
                                MessageBox.Show("返回数据集为空退出下载！"+ outp_put);
                                frm.Visible = false;
                                frm.Close();
                                return;
                            }
                            string strLastPage = jo["lastPage"].ToString();//是否是最后一页
                            string pageNum = jo["pageNum"].ToString();
                            string pages = jo["pages"].ToString();
                            this.Invoke(new Action(() =>
                            {
                                frm.SetCaption("正在处理数据!共" + pages + "页, 当前第" + pageNum + "页");                               
                            }));
                            DataTable dtdata = ToDataTableTwo(data); //JsonToDT(output);//转换为datatalbe 
                            gridControl5.DataSource = dtdata;
                            gridView5.BestFitColumns();
                            int page = (drdata["page_num"] == DBNull.Value ? 0 : Convert.ToInt32(drdata["page_num"])) + 1;
                            drdata["page_num"] = page;
                            barEditItem3.EditValue = pageNum;
                            save1312();
                            if (strLastPage.ToLower().Equals("true"))
                            {
                                MessageBox.Show("已经是医保返回的最后一页！");
                                frm.Visible = false;
                                frm.Close();
                                return;
                            }
                        }
                        else
                        {
                            if (!barCheckItem3.Checked)
                            {
                                MessageBox.Show("下载异常，无法继续循环下载！");
                                frm.Visible = false;
                                frm.Close();
                                return;
                            }
                        }
                    }
                    frm.Visible = false;
                    frm.Close();
                }
                {
                    if (MedicalInterface.MedicalBusinessHandle("021", "", "", strCHARGE_TYPE, PlatCommon.SysBase.SystemParm.LoginUser.ID, "", remark, ds) >= 0)
                    {
                        string outp_put = MedicalInterface.gs_output;
                        JObject jo = (JObject)JsonConvert.DeserializeObject(outp_put);
                        string data = jo["data"].ToString();
                        string strLastPage = jo["lastPage"].ToString();//是否是最后一页

                        DataTable dtdata = ToDataTableTwo(data); //JsonToDT(output);//转换为datatalbe 
                        gridControl5.DataSource = dtdata;
                        gridView5.BestFitColumns();
                        if (strLastPage.ToLower().Equals("true"))
                        {
                            MessageBox.Show("已经是医保返回的最后一页！");
                            return;
                        }
                        else
                        {
                            MessageBox.Show("不是医保返回的最后一页！");
                            return;
                        }
                    }
                    else
                    {
                        //MessageBox.Show("下载数据失败！", "提示");
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("下载出错！", ex.Message);
            }
        }
       
        private void save1312()
        {
            List<string> dels;
            List<string> sqls;
            string dt_name = "INSUR_DICT1312_PTYB";
            DataTable dt = gridControl5.DataSource as DataTable;

            if (null == dt) return;

            dels = new List<string>(dt.Rows.Count);
            sqls = new List<string>(dt.Rows.Count);

            List<string> list = new List<string>(28);

            foreach (DataRow dr in dt.Rows)
            {
                list.Clear();
                if (dr.Table.Columns.Contains("HILIST_CODE"))
                {
                    list.Add($"'{dr["HILIST_CODE"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("HILIST_NAME"))
                {
                    list.Add($"'{$"{dr["HILIST_NAME"]}".Replace("'", "''")}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("INSU_ADMDVS"))
                {
                    list.Add($"'{dr["INSU_ADMDVS"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("BEGNDATE"))
                {
                    if ($"{dr["BEGNDATE"]}".Length > 0)
                    {
                        dels.Add($"DELETE INSURANCE.{dt_name} WHERE HILIST_CODE = '{dr["HILIST_CODE"]}' AND BEGNDATE = to_date('{dr["BEGNDATE"]}','yyyy-MM-dd HH24:mi:ss')");
                        list.Add($"to_date('{dr["BEGNDATE"]}','yyyy-MM-dd HH24:mi:ss')");
                    }
                    else
                    {
                        dels.Add($"DELETE INSURANCE.{dt_name} WHERE HILIST_CODE = '{dr["HILIST_CODE"]}' AND BEGNDATE = NULL");
                        list.Add("NULL");
                    }
                }
                else
                {
                    list.Add("NULL");
                }

                if (dr.Table.Columns.Contains("ENDDATE"))
                {
                    if ($"{dr["ENDDATE"]}".Length > 0)
                    {

                        list.Add($"to_date('{dr["ENDDATE"]}','yyyy-MM-dd HH24:mi:ss')");
                    }
                    else
                    {
                        list.Add("NULL");
                    }
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("MED_CHRGITM_TYPE"))
                {
                    list.Add($"'{dr["MED_CHRGITM_TYPE"]}'");                
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("CHRGITM_LV"))
                {
                    list.Add($"'{dr["CHRGITM_LV"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("LMT_USED_FLAG"))
                {
                    list.Add($"'{dr["LMT_USED_FLAG"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("LIST_TYPE"))
                {
                    list.Add($"'{dr["LIST_TYPE"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("MED_USE_FLAG"))
                {
                    list.Add($"'{dr["MED_USE_FLAG"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("MATN_USED_FLAG"))
                {
                    list.Add($"'{dr["MATN_USED_FLAG"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("HILIST_USE_TYPE"))
                {
                    list.Add($"'{dr["HILIST_USE_TYPE"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("LMT_CPND_TYPE"))
                {
                    list.Add($"'{dr["LMT_CPND_TYPE"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("WUBI"))
                {
                    list.Add($"'{$"{dr["WUBI"]}".Replace("'", "''")}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("MEMO"))
                {
                    list.Add($"'{$"{dr["MEMO"]}".Replace("'", "''")}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("VALI_FLAG"))
                {
                    list.Add($"'{dr["VALI_FLAG"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("RID"))
                {
                    list.Add($"'{dr["RID"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("UPDT_TIME"))
                {
                    if ($"{dr["UPDT_TIME"]}".Length > 0)
                    {
                        list.Add($"to_date('{dr["UPDT_TIME"]}','yyyy-MM-dd HH24:mi:ss')");
                    }
                    else
                    {
                        list.Add("NULL");
                    }
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("CRTER_ID"))
                {
                    list.Add($"'{dr["CRTER_ID"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("CRTER_NAME"))
                {
                    list.Add($"'{dr["CRTER_NAME"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("CRTE_TIME"))
                {
                    if ($"{dr["CRTE_TIME"]}".Length > 0)
                    {
                        list.Add($"to_date('{dr["CRTE_TIME"]}','yyyy-MM-dd HH24:mi:ss')");
                    }
                    else
                    {
                        list.Add("NULL");
                    }
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("CRTE_OPTINS_NO"))
                {
                    list.Add($"'{dr["CRTE_OPTINS_NO"]}'");
                }
                else
                {
                    list.Add("NULL");
                }

                if (dr.Table.Columns.Contains("OPTER_ID"))
                {
                    list.Add($"'{dr["OPTER_ID"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("OPTER_NAME"))
                {
                    list.Add($"'{dr["OPTER_NAME"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("OPT_TIME"))
                {
                    if ($"{dr["OPT_TIME"]}".Length > 0)
                    {
                        list.Add($"to_date('{dr["OPT_TIME"]}','yyyy-MM-dd HH24:mi:ss')");
                    }
                    else
                    {
                        list.Add("NULL");
                    }
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("OPTINS_NO"))
                {
                    list.Add($"'{dr["OPTINS_NO"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("POOLAREA_NO"))
                {
                    list.Add($"'{dr["POOLAREA_NO"]}'");
                }
                else
                {
                    list.Add("NULL");
                }
                if (dr.Table.Columns.Contains("PINYIN"))
                {
                    list.Add($"'{$"{dr["PINYIN"]}".Replace("'", "''")}'");
                }
                else
                {
                    list.Add("NULL");
                }

                sqls.Add($"INSERT INTO INSURANCE.{dt_name}(HILIST_CODE, HILIST_NAME, INSU_ADMDVS, BEGNDATE, ENDDATE, MED_CHRGITM_TYP, CHRGITM_LV, LMT_USED_FLAG, LIST_TYPE, MED_USE_FLAG, MATN_USED_FLAG, HILIST_USE_TYPE, LMT_CPND_TYPE, WUBI, MEMO, VALI_FLAG, RID, UPDT_TIME, CRTER_ID, CRTER_NAME, CRTE_TIME, CRTE_OPTINS_NO, OPTER_ID, OPTER_NAME, OPT_TIME, OPTINS_NO, POOLAREA_NO, PINYIN) VALUES({string.Join(" ,", list.ToArray())})");
            }

            using (NM_Service.NMService.ServerPublicClient client = new NM_Service.NMService.ServerPublicClient())
            {
                Dictionary<string, string> dic = dels.ToDictionary(p => p, p => p);

                string err = client.SaveTable(dic);

                if ($"{err}".Length > 0)
                {
                    MessageBox.Show($"{err}");
                    return;
                }

                dic = sqls.ToDictionary(p => p, p => p);

                err = client.SaveTable(dic);

                if ($"{err}".Length > 0)
                {
                    MessageBox.Show($"{err}");
                    return;
                }
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            save1312();
            MessageBox.Show("保存成功");
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            dt_dict1319.AcceptChanges();
            string ls_savetable =  "INSURANCE.insur_Dict1319_PTYB";
            string ls_sql = "select * from " + ls_savetable + " ";//where rownum = 0
            DataTable dt_dict = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_sql).Tables[0];

            string ls_Columns = dt_dict1319.Columns[8].ToString();
            string ls_Columns1 = dt_dict1319.Columns[9].ToString();
            string ls_Columns2 = dt_dict1319.Columns[4].ToString();
            for (int i = 0; i < dt_dict1319.Rows.Count; i++)
            {
                string insur_code = dt_dict1319.Rows[i][8].ToString();
                string stDate = dt_dict1319.Rows[i][9].ToString();
                string endDate = dt_dict1319.Rows[i][4].ToString();
                DataRow[] dr_find = dt_dict.Select(ls_Columns + " = '" + insur_code + "' and " + ls_Columns1 + " = '" + stDate + "'");
                if (dr_find.Length > 0)
                {
                    dt_dict1319.Rows[i].SetModified();
                    //if (dt_dict1319.Rows[i]["ID"] == DBNull.Value)
                    //    dt_dict1319.Rows[i]["ID"] = System.Guid.NewGuid().ToString("N");
                }
                else
                {
                    dt_dict1319.Rows[i].SetAdded();
                    //dt_dict1319.Rows[i]["ID"] = System.Guid.NewGuid().ToString("N");
                }
            }
            DataSet ds = new DataSet();
            ds.Tables.Add(dt_dict1319.Copy());
            int ret = new NM_Service.NMService.ServerPublicClient().SaveDataSet(ds);
            if (ret < 0)
            {
                XtraMessageBox.Show("保存失败！", "提示");
            }
            else
            {
                XtraMessageBox.Show("保存完成！", "提示");
            }

            string sql = @"Delete From " + ls_savetable + @"
 Where rid In (Select rid
                 From (Select Row_Number() Over(Partition By hilist_code, selfpay_prop_psn_type, insu_admdvs, begndate, enddate, selfpay_prop,rid,updt_time order By rid,updt_time) As RowNumber,
                              t.*
                         From " + ls_savetable + @" t)
                Where RowNumber > 1)";
            Dictionary<string, string> idc = new Dictionary<string, string>();
            idc.Add(sql, "去重失败！");
            string rev = new NM_Service.NMService.ServerPublicClient().SaveTable(idc);
            if (!string.IsNullOrEmpty(rev))
            {
                return;
            }

        }

        private void simpleButton3_Click(object sender, EventArgs e)
        {
            dt_dict1318.AcceptChanges();
            string ls_savetable = "INSURANCE.insur_Dict1318_PTYB" ;
            string ls_sql = "select * from " + ls_savetable + " ";//where rownum = 0
            DataTable dt_dict = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_sql).Tables[0];

            string ls_Columns = dt_dict1318.Columns[8].ToString();
            string ls_Columns1 = dt_dict1318.Columns[9].ToString();
            string ls_Columns2 = dt_dict1318.Columns[18].ToString();
            for (int i = 0; i < dt_dict1318.Rows.Count; i++)
            {
                string insur_code = dt_dict1318.Rows[i][8].ToString();
                string stDate = dt_dict1318.Rows[i][9].ToString();
                string endDate = dt_dict1318.Rows[i][18].ToString();
                DataRow[] dr_find = dt_dict.Select(ls_Columns + " = '" + insur_code + "' and " + ls_Columns1 + " = '" + stDate + "' and " + ls_Columns2 + " = '" + endDate + "'");
                if (dr_find.Length > 0)
                {
                    dt_dict1318.Rows[i].SetModified();
                    //if (dt_dict1319.Rows[i]["ID"] == DBNull.Value)
                    //    dt_dict1319.Rows[i]["ID"] = System.Guid.NewGuid().ToString("N");
                }
                else
                {
                    dt_dict1318.Rows[i].SetAdded();
                    //dt_dict1319.Rows[i]["ID"] = System.Guid.NewGuid().ToString("N");
                }
            }
            DataSet ds = new DataSet();
            ds.Tables.Add(dt_dict1318.Copy());
            int ret = new NM_Service.NMService.ServerPublicClient().SaveDataSet(ds);
            if (ret < 0)
            {
                XtraMessageBox.Show("保存失败！", "提示");
            }
            else
            {
                XtraMessageBox.Show("保存完成！", "提示");
            }

            string sql = @"Delete From " + ls_savetable + @"
 Where rid In (Select rid
                 From (Select Row_Number() Over(Partition By HILIST_CODE, HILIST_LMTPRIC_TYPE,OVERLMT_DSPO_WAY, INSU_ADMDVS, BEGNDATE, ENDDATE, HILIST_PRIC_UPLMT_AMT, RID, UPDT_TIME order By RID, UPDT_TIME) As RowNumber,
                              t.*
                         From " + ls_savetable + @" t)
                Where RowNumber > 1)";
            Dictionary<string, string> idc = new Dictionary<string, string>();
            idc.Add(sql, "去重失败！");
            string rev = new NM_Service.NMService.ServerPublicClient().SaveTable(idc);
            if (!string.IsNullOrEmpty(rev))
            {
                return;
            }

        }

        private void btnQuery1901_Click(object sender, EventArgs e)
        {
            if(slu1901Type.EditValue==null|| string.IsNullOrEmpty(slu1901Type.EditValue.ToString()))
            {
                slu1901Type.EditValue = "";
            }
            if (slu1901Parent.EditValue == null || string.IsNullOrEmpty(slu1901Parent.EditValue.ToString()))
            {
                slu1901Parent.EditValue = "";
            }
            if (dt1901Date.EditValue == null || string.IsNullOrEmpty(dt1901Date.EditValue.ToString()))
            {
                XtraMessageBox.Show("查询日期不可为空！");
                return;
            }
            try
            {
                DataTable dt_data = new DataTable();
                SetTable1901(ref dt_data);
                DataRow drdata = dt_data.NewRow();
                string ls_query = "";
                try
                {
                    ls_query = barEditItem1.EditValue.ToString();
                }
                catch (Exception ex)
                { }
                if (!string.IsNullOrEmpty(ls_query))
                {
                    ls_query = DateTime.Parse(ls_query).ToString("yyyy-MM-dd HH:mm:ss");
                }
                drdata["type"] = slu1901Type.EditValue;//字典类型 可为空 与文档不符
                drdata["parent_value"] = slu1901Parent.EditValue;//父字典键值 可为空
                drdata["admdvs"] = "";//行政区划 示例 没传
                drdata["date"] = Convert.ToDateTime(dt1901Date.EditValue).ToString("yyyy-MM-dd");//查询日期
                drdata["vali_flag"] = "1";//医保目录名称
                drdata["JYLX"] = "1901";

                dt_data.Rows.Add(drdata);
                DataSet ds = new DataSet();
                ds.Tables.Add(dt_data);
                string remark = "";

                string strCHARGE_TYPE = $"{comboBox1.EditValue}";

                if (MedicalInterface.MedicalBusinessHandle("021", "", "", strCHARGE_TYPE, PlatCommon.SysBase.SystemParm.LoginUser.ID, "", remark, ds) >= 0)
                {
                    //{"cainfo":null,"err_msg":"","inf_refmsgid":"null202111101613080000808029","infcode":0,"output":{"list":[{"vali_flag":"1","create_user":"1","label":"西药中成药","sort":1,"create_date":1624877371000,"type":"LIST_TYPE","parent_value":"","value":"101","version":2},{"vali_flag":"1","create_user":"1","label":"中药饮片","sort":2,"create_date":1624877371000,"type":"LIST_TYPE","parent_value":"","value":"102","version":2},{"vali_flag":"1","create_user":"1","label":"自制剂","sort":3,"create_date":1624877371000,"type":"LIST_TYPE","parent_value":"","value":"103","version":2},{"vali_flag":"1","create_user":"1","label":"民族药","sort":4,"create_date":1624877371000,"type":"LIST_TYPE","parent_value":"","value":"104","version":2},{"vali_flag":"1","create_user":"1","label":"医疗服务项目","sort":5,"create_date":1624877371000,"type":"LIST_TYPE","parent_value":"","value":"201","version":2},{"vali_flag":"1","create_user":"1","label":"医用耗材","sort":6,"create_date":1624877371000,"type":"LIST_TYPE","parent_value":"","value":"301","version":2}]},"refmsg_time":"20211110161308029","respond_time":"20211110161308062","signtype":null,"warn_msg":null}
                    string outp_put = MedicalInterface.gs_output;
                    JObject jo = (JObject)JsonConvert.DeserializeObject(outp_put);

                    string data = jo["list"].ToString();
                    DataTable dtdata = ToDataTableTwo(data); //JsonToDT(output);//转换为datatalbe 
                    gridControl6.DataSource = dtdata;
                    gridView6.BestFitColumns();
                    
                }
                else
                {
                    //MessageBox.Show("下载数据失败！", "提示");
                    return;
                }
            }
            catch (Exception ex)
            {
            }
        }

        private void btnSave1901_Click(object sender, EventArgs e)
        {
            DataTable dt_ori = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select a.* from insurance.tj_insurance_dict a  where a.interfacecode='ptyb'").Tables[0];
            DataTable dt_now = gridControl6.DataSource as DataTable;
            foreach(DataRow dr_now in dt_now.Rows)
            {
                DataRow[] drs_type = dt_ori.Select("INSUR_TYPE='"+ dr_now["type"].ToString().ToLower() + "'");
                if (drs_type.Length > 0)
                {
                    //存在当前类型 则
                    DataRow[] drs_data = drs_type.CopyToDataTable().Select("CODE='" + dr_now["value"].ToString() + "'");
                    if (drs_data.Length == 0)
                    {
                        //没有 则新增
                        DataRow dr_insert = dt_ori.NewRow();
                        dr_insert["interfacecode"] = "ptyb";
                        dr_insert["insur_type"] = dr_now["type"].ToString().ToLower();
                        dr_insert["code"] = dr_now["value"].ToString();
                        dr_insert["name"]= dr_now["label"].ToString();
                        dr_insert["description"] = drs_type[0]["description"].ToString();
                        dr_insert["flag"] = 1;
                        dt_ori.Rows.Add(dr_insert);
                    }
                    else 
                    {
                        //有  则对比 有变化 则更新 否则 跳过
                        string name_ori = drs_data[0]["name"].ToString();
                        string name_now = dr_now["label"].ToString();
                        if(name_ori.Trim()== name_now.Trim())
                        {
                            continue;
                        }
                        else
                        {
                            drs_data[0]["name"] = dr_now["label"];
                        }
                    }
                }
                else
                {
                    //没有 则新增
                    DataRow dr_insert = dt_ori.NewRow();
                    dr_insert["interfacecode"] = "ptyb";
                    dr_insert["insur_type"] = dr_now["type"].ToString().ToLower();
                    dr_insert["code"] = dr_now["value"].ToString();
                    dr_insert["name"] = dr_now["label"].ToString();
                    dr_insert["flag"] = 1;
                    dt_ori.Rows.Add(dr_insert);
                }
            }
            int ret = new NM_Service.NMService.ServerPublicClient().SaveDataSet(dt_ori.DataSet);
            if (ret < 0)
            {
                XtraMessageBox.Show("保存失败!");
            }
            else
            {
                XtraMessageBox.Show("保存成功!");
            }
        }

        private void btn1304Up_Click(object sender, EventArgs e)
        {
            TjhisInterfaceInsurance.Model.Input1304 aa = new TjhisInterfaceInsurance.Model.Input1304();
            aa.data = new TjhisInterfaceInsurance.Model.Data1304();
            aa.data.med_list_codg = txt_med_list_codg.Text;
            aa.data.genname_codg = txt_genname_codg.Text;
            aa.data.drug_genname = txt_drug_genname.Text;
            aa.data.drug_prodname = txt_drug_prodname.Text;
            aa.data.reg_name = txt_reg_name.Text;
            aa.data.tcmherb_name = txt_mlms_name.Text;
            aa.data.mlms_name = txt_mlms_name.Text;
            aa.data.vali_flag = txt_med_list_codg.Text;
            aa.data.rid = txt_rid.Text;
            aa.data.ver = txt_ver.Text;
            aa.data.ver_name = txt_ver_name.Text;
            aa.data.opt_begn_time = dtp_opt_begn_time.Value.ToString();
            aa.data.opt_end_time = dtp_opt_end_time.Value.ToString();
            aa.data.updt_time = dtp_updt_time.Value.ToString();
            aa.data.page_num = txt_page_num.Text;
            aa.data.page_size = txt_page_size.Text;
            string input = JsonConvert.SerializeObject(aa); //入参拼串传进去
            string remark = input;
            string ls_charge_type = "医保职工";
            DataTable dt024 = new DataTable();
            dt024.Columns.Add("JYLX");//交易类型
            dt024.Columns.Add("PSN_NO");//交易类型
            DataRow dr = dt024.NewRow();
            dr["JYLX"] = "1304";
            dr["PSN_NO"] = "";
            dt024.Rows.Add(dr);
            DataSet ds024 = new DataSet();
            ds024.Tables.Add(dt024);
            if (MedicalInterface.MedicalBusinessHandle("021", "", "", ls_charge_type, PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME, "", remark, ds024) >= 0)
            {
                string outp_put = MedicalInterface.gs_output;
                JObject jo = (JObject)JsonConvert.DeserializeObject(outp_put);

                string data = jo["data"].ToString();
                DataTable dtdata = ToDataTableTwo(data); //JsonToDT(output);//转换为datatalbe 
                gc1304.DataSource = dtdata;
                gridView6.BestFitColumns();
            }
        }

        private void barButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void xtraTabControl1_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (xtraTabControl1.SelectedTabPage.Name == "xtraTabPage5")
            {
                string ls_sql = "select decode(max(a.updt_time),null,null,to_char(max(a.updt_time),'yyyy-mm-dd')) from INSURANCE.INSUR_DICT1312_PTYB a";
                string updt_time = new NM_Service.NMService.ServerPublicClient().GetSingleValue(ls_sql);
                barEditItem1.EditValue = updt_time;
            }
        }
    }
}
