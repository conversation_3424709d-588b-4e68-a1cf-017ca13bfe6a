﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraReports.UI;
using NM_Service.NMService;
using PlatCommon.SysBase;
using Tjhis.Presdisp.Station.Comm;
using PlatCommon.Common;
using Tjhis.Presdisp.Station.Report;

namespace Tjhis.Presdisp.Station.View
{
    public partial class FrmSurgeryMorePresdispDeliver : PlatCommon.SysBase.ParentForm
    {

        #region 变量
        string chargetypes = "";
        DataRow masterdr;//处方基础信息
        string prescdateprint = "";//重打时调用，发药完成时复制
        string prescnoprint = "";//重打时调用，发药完成时复制
        DataSet ds_print = null;
        DataTable dt_print = new DataTable();//打印数据集
        int ii_item_no = 0;

        Dictionary<string, string> list_item;
        #endregion

        #region 事件
        public FrmSurgeryMorePresdispDeliver()
        {
            InitializeComponent();
        }

        private void FrmSurgeryMorePresdispDeliver_Load(object sender, EventArgs e)
        {
            Getdt_printClone();
            SetDept();
            SetPrescAttr();
        }

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BbtnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                #region 选择处方
                gv1.CloseEditor();
                gv1.UpdateCurrentRow();
                ii_item_no = 0;
                list_item = new Dictionary<string, string>();
                DataTable batchnodt = GetBatchProvideNo();
                string batchno = "";
                if (batchnodt.Rows.Count > 0)
                {
                    batchno = batchnodt.Rows[0][0].ToString();
                }
                else
                {
                    batchno = GetSystemTime().ToString("yyyyMMdd") + "000001";
                }

                DataTable masterlist = (DataTable)gc1.DataSource;
                DataRow[] selectmaster = masterlist.Select("AS_COMFIRM=1");//获取处方主列表选中行
                if (selectmaster.Length == 0)
                {
                    XtraMessageBox.Show("请选择要批量发药的处方!", "提示");
                    return;
                }
                #endregion

                #region 保存时是否弹出确认提示框
                string commitConfirm = SystemParm.GetParameterValue("COMMIT_CONFIRM", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                if (commitConfirm.Length > 0 && Convert.ToInt16(commitConfirm) > 0)
                {
                    if (XtraMessageBox.Show("是否确认？", "提示", MessageBoxButtons.OKCancel) == DialogResult.Cancel)
                    {
                        return;
                    }
                }
                #endregion

                #region 保存时是否需要重新输入密码
                string confirmlogin = SystemParm.GetParameterValue("CONFIRM_LOGIN", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                if (confirmlogin == "1")
                {
                    FrmPrescLogin presclogin = new FrmPrescLogin();
                    presclogin.ShowDialog();
                    if (!presclogin.resultstr) return;
                }
                string DISPENSING_DATETIME = PlatCommon.Common.PublicFunction.GetSysDate();
                DataTable printdt = new DataTable();
                printdt.Columns.Add("PRESC_DATE");
                printdt.Columns.Add("PRESC_NO");
                #endregion

                #region 批量保存
                for (int i = 0; i < selectmaster.Length; i++)
                {
                    Dictionary<string, string> allsave = new Dictionary<string, string>();

                    DataTable masterdt = GetDoctPrescMasterTempByDateAndNo(Convert.ToDateTime(selectmaster[i]["PRESC_DATE"]), selectmaster[i]["PRESC_NO"].ToString());
                    if (masterdt.Rows.Count > 0)
                    {
                        txtPatientID.Text = masterdt.Rows[0]["PATIENT_ID"].ToString();
                        txtName.Text = masterdt.Rows[0]["NAME"].ToString();
                        txtAge.Text = masterdt.Rows[0]["AGE"].ToString();
                        txtSex.Text = masterdt.Rows[0]["SEX"].ToString();
                        txtIdentity.Text = masterdt.Rows[0]["IDENTITY"].ToString();
                        txtChargeType.Text = masterdt.Rows[0]["CHARGE_TYPE"].ToString();
                        sluePrescAttr.EditValue = masterdt.Rows[0]["PRESC_ATTR"];//处方属性
                        txtPrescDate.Text = masterdt.Rows[0]["PRESC_DATE"].ToString();//开方日期
                                                                                      //txtPrescNo.Text = masterdt.Rows[0]["PRESC_NO"].ToString();
                        txtPrescribed.Text = masterdt.Rows[0]["PRESCRIBED_BY"].ToString();//开单医生
                        txtDeptName.Text = masterdt.Rows[0]["DEPT_NAME"].ToString();//科室
                                                                                    //txtPrescType.Text = masterdt.Rows[0]["ORDERED_BY"].ToString() == "1" ? "中药" : "西药";//处方类别
                                                                                    //txtYu.Text = masterdt.Rows[0]["PREPAYMENT"].ToString();//预交金
                                                                                    //txtJi.Text = masterdt.Rows[0]["COSTS"].ToString();//计价
                                                                                    //txtYing.Text = masterdt.Rows[0]["PAYMENTS"].ToString();//应收
                                                                                    //txtAdmin.Text = masterdt.Rows[0]["USAGE"] != DBNull.Value ? masterdt.Rows[0]["USAGE"].ToString() : "";//用法

                        txtRepetition.Text = masterdt.Rows[0]["REPETITION"].ToString();//剂树
                        txtCountPerRepetition.Text = masterdt.Rows[0]["COUNT_PER_REPETITION"] != DBNull.Value ? masterdt.Rows[0]["COUNT_PER_REPETITION"].ToString() : "";//每剂/份
                        txtEnteredBy.Text = masterdt.Rows[0]["ENTERED_BY"] == DBNull.Value ? "" : masterdt.Rows[0]["ENTERED_BY"].ToString();

                        masterdr = masterdt.Rows[0];
                    }
                    else
                    {
                        continue;
                    }

                    int right = SaveOnePresc(batchno, DISPENSING_DATETIME, ref allsave);


                    if (right < 0) return;

                    if (allsave.Count > 0)
                    {
                        string result = new ServerPublicClient().SaveTable(allsave);
                        if (result.Length > 0)
                        {
                            XtraMessageBox.Show(result + "处方号：" + masterdr["PRESC_NO"].ToString(), "保存失败");
                            return;
                        }
                        else
                        {

                            DataRow printdr = printdt.NewRow();
                            printdr["PRESC_DATE"] = masterdr["PRESC_DATE"];
                            printdr["PRESC_NO"] = masterdr["PRESC_NO"];
                            prescdateprint = masterdr["PRESC_DATE"].ToString();
                            prescnoprint = masterdr["PRESC_NO"].ToString();
                            printdt.Rows.Add(printdr);
                        }
                    }

                }
                XtraMessageBox.Show("手术处方批量发药成功！", "提示");
                #endregion

                #region 打印
                string prnflag = SystemParm.GetParameterValue("PRNFLAG", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                if (prnflag == "1")//打印
                {
                    Hashtable hasParam = new Hashtable
                    {
                        { "BATCH_PROVIDE_NO", batchno  }
                    };

                    DataSet dsPrint = XtraReportHelper.GetPrintData_DataBase("手术处方批量发药", hasParam, this.AppCode);
                    XtraReportHelper.Print("手术处方批量发药", dsPrint, false, this.AppCode);              
                }
                else if (prnflag == "2")//打印前提示
                {
                    Hashtable hasParam = new Hashtable
                    {
                        { "BATCH_PROVIDE_NO", batchno  }
                    };

                    DataSet dsPrint = XtraReportHelper.GetPrintData_DataBase("手术处方批量发药", hasParam, this.AppCode);
                    XtraReportHelper.Print("手术处方批量发药", dsPrint, false, this.AppCode);                   
                }
                #endregion

                #region 统一接口 2022-04-14 

                DataTable[] dataTables = new DataTable[] { printdt };
                string strERROR_TEXT = string.Empty;
                Tjhis.Interface.Station.Interface_Common.InvokeInterface("Presdisp_007", "UPDATE", this.AppName, this.DeptCode, dataTables, ref strERROR_TEXT);
                #endregion

                bbtnrefresh_ItemClick(null, null);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("\r\nFrmSurgeryMorePresdispDeliver-BbtnSave_ItemClick-Exception: " + ex.Message, "温馨提示");
            }

        }
        /// <summary>
        /// 刷新
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void bbtnrefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //slueDept.Text = "";
            //cmbPrescType.SelectedIndex = -1;
            //CleanTxt();
            //CleanStorgeTxt();
            //gc2.DataSource = null;
            SetPrescMaster();
        }
        /// <summary>
        /// 退出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void bbtnExit_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        /// <summary>
        /// 单击病人，查询处方主信息和明细
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gv1_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            SetBaseInfoAndDetailByPrescDateAndPrescNo(e.RowHandle);
        }
        /// <summary>
        /// 点击明细，查询库存和单价
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gv2_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DataTable drugdt = (DataTable)gc2.DataSource;
            if (drugdt.Rows.Count > 0)
            {
                //显示零售价和库存
                lbStockCount.Text = GetDrugStockByDrugCode(this.DeptCode, drugdt.Rows[e.RowHandle]["DRUG_CODE"].ToString(), drugdt.Rows[e.RowHandle]["FIRM_ID"].ToString(), drugdt.Rows[e.RowHandle]["PACKAGE_SPEC"].ToString()).Rows[0][0].ToString();
                DataTable drugprice = GetDrugPriceDrugCode(drugdt.Rows[e.RowHandle]["DRUG_CODE"].ToString(), drugdt.Rows[e.RowHandle]["DRUG_SPEC"].ToString(), drugdt.Rows[e.RowHandle]["FIRM_ID"].ToString());
                if (drugprice.Rows.Count > 0)
                {
                    lbPrice.Text = drugprice.Rows[0]["retail_price"] != DBNull.Value ? drugprice.Rows[0]["retail_price"].ToString() : "";
                }
                else
                {
                    lbPrice.Text = "";
                }
            }
            else
            {
                lbStockCount.Text = "";
                lbPrice.Text = "";
            }
        }
        /// <summary>
        /// 科室类型变化，筛选
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void SlueDept_EditValueChanged(object sender, EventArgs e)
        {
            SetPrescMaster();
            SetPrescAttr();
        }

        #endregion

        #region 方法

        /// <summary>
        /// 重打
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <param name="isfirst"></param>
        private void RepertPrint(string prescdate, string prescno, bool isfirst)
        {
            int prisctype = 0;
            string patientid = "";
            string prescribedby = "";//开单医生姓名
            DataTable prescmasterdt = GetDrugPrescMasterByPrescDateAndNo(prescdate, prescno);
            DataTable printdt;
            if (prescmasterdt.Rows.Count > 0)
            {
                prisctype = Convert.ToInt16(prescmasterdt.Rows[0]["presc_type"]);
                patientid = prescmasterdt.Rows[0]["patient_id"].ToString();
                prescribedby = prescmasterdt.Rows[0]["prescribed_by"].ToString();
            }
            else return;
            int printindex = 0;
            string printtype = SystemParm.GetParameterValue("PRINTERTYPE", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            if (printtype == "normal")
            {
                if (prisctype == 0)
                {
                    //d_pip_print_doct_presc_master_normal_west
                    printdt = GetDrugPrescByPrint(prescdate, prescno);
                    printindex = 1;
                }
                else
                {
                    //d_pip_print_doct_presc_master_normal_china1
                    printdt = GetDrugPrescByPrint1(prescdate, prescno);
                    printindex = 2;
                }
            }
            else
            {
                //d_pip_print_doct_presc_master_normal_hot
                printdt = GetDrugPrescByPrint(prescdate, prescno);
                printindex = 3;
            }
            if (prisctype == 0)
            {
                //d_pip_print_doct_presc_master_normal_west
                printdt = GetDrugPrescByPrint(prescdate, prescno);
                printindex = 1;
            }
            else
            {
                //d_pip_print_doct_presc_master_normal_china1
                printdt = GetDrugPrescByPrint1(prescdate, prescno);
                printindex = 2;
            }
            printdt = GetDrugPrescByPrint(prescdate, prescno);
            if (printdt.Rows.Count < 1)
            {
                printdt = GetDrugPrescByPrint1(prescdate, prescno);
                printindex = 4;
            }
            if (printdt.Rows.Count == 0) return;

            foreach (DataRow dr in printdt.Rows)
            {
                DataRow dr_new = dt_print.NewRow();
                dr_new["PRESC_DATE"] = dr["PRESC_DATE"];
                dr_new["PRESC_NO"] = dr["PRESC_NO"];
                dr_new["AGE"] = dr["AGE"];
                dr_new["SEX"] = dr["SEX"];
                dr_new["DISPENSARY"] = dr["DISPENSARY"];
                dr_new["DISPENSARY_NAME"] = dr["DISPENSARY_NAME"];
                dr_new["PATIENT_ID"] = dr["PATIENT_ID"];
                dr_new["NAME"] = dr["NAME"];
                dr_new["BED_LABEL"] = dr["BED_LABEL"];
                dr_new["IDENTITY"] = dr["IDENTITY"];
                dr_new["CHARGE_TYPE"] = dr["CHARGE_TYPE"];
                dr_new["UNIT_IN_CONTRACT"] = dr["UNIT_IN_CONTRACT"];
                dr_new["PRESC_SOURCE"] = dr["PRESC_SOURCE"];
                dr_new["REPETITION"] = dr["REPETITION"];
                dr_new["ORDERED_BY"] = dr["ORDERED_BY"];
                dr_new["PRESCRIBED_BY"] = dr["PRESCRIBED_BY"];
                //dr_new["VISIT_ID"] = dr["VISIT_ID"];
                dr_new["ITEM_NO"] = dr["ITEM_NO"];
                dr_new["DRUG_NAME"] = dr["DRUG_NAME"];
                dr_new["FIRM_ID"] = dr["FIRM_ID"];
                dr_new["PACKAGE_SPEC"] = dr["PACKAGE_SPEC"];
                dr_new["PACKAGE_UNITS"] = dr["PACKAGE_UNITS"];
                dr_new["QUANTITY"] = dr["QUANTITY"];
                dr_new["NAME_PHONETIC"] = dr["NAME_PHONETIC"];
                dr_new["PRESC_TYPE"] = dr["PRESC_TYPE"];
                dr_new["COSTS"] = dr["COSTS"];
                dr_new["PAYMENTS"] = dr["PAYMENTS"];
                dr_new["ENTERED_BY"] = dr["ENTERED_BY"];
                //dr_new["COUNT_PER_REPETITION"] = dr["COUNT_PER_REPETITION"];
                dr_new["DCOSTS"] = dr["DCOSTS"];
                dr_new["FREQUENCY"] = dr["FREQUENCY"];
                dr_new["DOSAGE_EACH"] = dr["DOSAGE_EACH"];
                //dr_new["DOSAGE"] = dr["DOSAGE"];
                //dr_new["DOSAGE_UNITS"] = dr["DOSAGE_UNITS"];
                //dr_new["ADMINISTRATION"] = dr["ADMINISTRATION"];
                //dr_new["USAGE"] = dr["USAGE"];
                //dr_new["DISCHARGE_TAKING_INDICATOR"] = dr["DISCHARGE_TAKING_INDICATOR"];
                //dr_new["DACOSTS"] = dr["DACOSTS"];
                dr_new["DEPT_NAME"] = dr["DEPT_NAME"];
                dt_print.Rows.Add(dr_new);
            }
            XtraInpPrescPrint inpprint = new XtraInpPrescPrint(printdt, "手术处方批量发药");
            //inpprint.SetTitle("手术处方批量发药");
            ReportPrintTool tool = new ReportPrintTool(inpprint);
            tool.Report.CreateDocument(false);
            tool.PrintingSystem.ShowMarginsWarning = false;
            tool.Print();
        }
        /// <summary>
        /// 保存一条处方记录
        /// </summary>
        /// <param name="allsave"></param>
        private int SaveOnePresc(string batchno, string DISPENSING_DATETIME, ref Dictionary<string, string> allsave)
        {
            DataTable detaildt = GetDoctPrescDetailTempByDateAndNo(Convert.ToDateTime(masterdr["PRESC_DATE"]), masterdr["PRESC_NO"].ToString());
            Dictionary<string, string> tempsave = new Dictionary<string, string>();

            #region 计算价格
            if (detaildt.Rows.Count > 0)
            {
                string chargetype = masterdr != null ? masterdr["CHARGE_TYPE"].ToString() : "";
                string presctype = masterdr != null ? masterdr["PRESC_TYPE"].ToString() : "0";
                string itemclass = presctype == "0" ? "A" : "B";

                decimal retailprice = 0;
                for (int i = 0; i < detaildt.Rows.Count; i++)
                {
                    #region 屏蔽
                    //if (detaildt.Rows[i]["FIRM_ID"] == DBNull.Value)
                    //{
                    //    XtraMessageBox.Show("第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无厂家，不能发药！", "提示");
                    //    detaildt.Rows[i]["ERR_MSG"] = "第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无厂家，不能发药！";
                    //    continue;
                    //}
                    //if (detaildt.Rows[i]["DRUG_SPEC"] == DBNull.Value)
                    //{
                    //    XtraMessageBox.Show("第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无小规格，不能发药！", "提示");
                    //    detaildt.Rows[i]["ERR_MSG"] = "第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无小规格，不能发药！";
                    //    continue;
                    //}
                    //if (detaildt.Rows[i]["package_spec"] == DBNull.Value)
                    //{
                    //    XtraMessageBox.Show("第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无发药规格，不能发药！", "提示");
                    //    detaildt.Rows[i]["ERR_MSG"] = "第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无发药规格，不能发药！";
                    //    continue;
                    //}
                    #endregion
                    DataTable pricedt = GetPriceListByClassCodeSpec(itemclass, detaildt.Rows[i]["DRUG_CODE"].ToString(), detaildt.Rows[i]["package_spec"].ToString() + detaildt.Rows[i]["FIRM_ID"]);
                    if (pricedt.Rows.Count > 0)
                    {
                        retailprice = Convert.ToDecimal(pricedt.Rows[0][0]);
                    }
                    //计算价格
                    DataTable coeffnumerator = GetChargePriceSchedule(chargetype, SystemParm.HisUnitCode);
                    decimal decDefault = 1;
                    if (coeffnumerator.Rows.Count > 0)
                    {
                        decDefault = Convert.ToDecimal(coeffnumerator.Rows[0]["price_coeff_numerator"]);
                    }
                    decimal decChargePrice = 0.00m;

                    PlatCommon.Common.PublicFunction.GetCalcChargePrice(chargetype, itemclass, detaildt.Rows[i]["DRUG_CODE"].ToString(), detaildt.Rows[i]["package_spec"].ToString() + detaildt.Rows[i]["FIRM_ID"], retailprice, ref decDefault, ref decChargePrice);
                    detaildt.Rows[i]["RETAIL_PRICE"] = retailprice;
                    detaildt.Rows[i]["COSTS"] = retailprice * Convert.ToDecimal(detaildt.Rows[i]["QUANTITY"]);
                    detaildt.Rows[i]["PAYMENTS"] = decChargePrice * Convert.ToDecimal(detaildt.Rows[i]["QUANTITY"]);

                }
            }
            #endregion

            #region 是否在院、是否出院全结
            DataTable prescdetadt = detaildt.Copy();
            string patientid = masterdr["PATIENT_ID"].ToString();
            int visitid = Convert.ToInt16(masterdr["VISIT_ID"].ToString());
            //出院全结
            DataTable settledt = GetInpSettleMasterByPatientID(patientid, visitid);
            //是否在院
            DataTable inhospdt = GetPatInHospitapByPatientID(patientid);
            #endregion

            decimal stockcount;//库存数量
            decimal detailcount;//当前使用数量
            decimal onecount;//每次减的数量，第一条库存不够，为第一条数量，第一条够了，为使用数量

            #region 1.处方主表-DRUG_PRESC_MASTER
            string insertsql = "";
            masterdr["DISPENSING_USERCODE"] = SystemParm.LoginUser.USER_NAME;
            masterdr["DISPENSING_PROVIDER"] = SystemParm.LoginUser.NAME;
            masterdr["COSTS"] = prescdetadt.Compute("sum(COSTS)", "TRUE");
            masterdr["PAYMENTS"] = prescdetadt.Compute("sum(PAYMENTS)", "TRUE");
            insertsql = InsertDoctDrugPrescMaster(masterdr, batchno, this.DeptCode, DISPENSING_DATETIME);
            if (!tempsave.ContainsKey(insertsql))
            {
                tempsave.Add(insertsql, "新增住院处方主记录失败！");
            }
            #endregion

            #region 2.处方明细表-DRUG_PRESC_DETAIL

            DataTable stockdt = new DataTable();//药品库存
            DataTable insertdetaildt = GetPrescDetailDataRow();

            int li_item_no = 0;
            for (int i = 0; i < prescdetadt.Rows.Count; i++)
            {
                #region 2.1判断库存
                stockdt = GetDrugStockForUpdate(this.DeptCode, prescdetadt.Rows[i]["DRUG_CODE"].ToString(), prescdetadt.Rows[i]["DRUG_SPEC"].ToString(), prescdetadt.Rows[i]["PACKAGE_SPEC"].ToString(), prescdetadt.Rows[i]["FIRM_ID"].ToString());
                if (stockdt.Rows.Count == 0)
                {
                    XtraMessageBox.Show("药品" + prescdetadt.Rows[i]["DRUG_NAME"] + "的库存不够!");
                    return -1;
                }
                detailcount = Convert.ToDecimal(prescdetadt.Rows[i]["QUANTITY"]);
                stockcount = Convert.ToDecimal(stockdt.Compute("sum(QUANTITY)", "true"));//多条库存总数量
                if (stockcount < detailcount)
                {
                    XtraMessageBox.Show("药品" + prescdetadt.Rows[i]["DRUG_NAME"] + "的库存数量不够!");
                    return -1;
                }
                #endregion

                #region 2.2减库存、写处方明细表
                for (int j = 0; j < stockdt.Rows.Count; j++)
                {
                    #region 2.2.1减库存
                    if (detailcount == 0) break;
                    li_item_no = li_item_no + 1;
                    if (Convert.ToDecimal(stockdt.Rows[j]["QUANTITY"]) >= detailcount)//一条库存够减的情况
                    {
                        onecount = detailcount;

                        insertsql = UpdateDrugStock(Convert.ToDecimal(stockdt.Rows[j]["QUANTITY"]) - detailcount, this.DeptCode, prescdetadt.Rows[i]["DRUG_CODE"].ToString(), prescdetadt.Rows[i]["DRUG_SPEC"].ToString(), prescdetadt.Rows[i]["PACKAGE_SPEC"].ToString(), prescdetadt.Rows[i]["FIRM_ID"].ToString(), stockdt.Rows[j]["BATCH_NO"].ToString());
                        detailcount = 0;
                    }
                    else
                    {
                        detailcount = detailcount - Convert.ToDecimal(stockdt.Rows[j]["QUANTITY"]);
                        onecount = Convert.ToDecimal(stockdt.Rows[j]["QUANTITY"]);
                        insertsql = UpdateDrugStock(0, this.DeptCode, stockdt.Rows[j]["DRUG_CODE"].ToString(), stockdt.Rows[j]["DRUG_SPEC"].ToString(), stockdt.Rows[j]["PACKAGE_SPEC"].ToString(), stockdt.Rows[j]["FIRM_ID"].ToString(), stockdt.Rows[j]["BATCH_NO"].ToString());

                    }
                    if (!tempsave.ContainsKey(insertsql))
                    {
                        tempsave.Add(insertsql, "修改药品库存数量失败！");
                    }
                    #endregion
                    #region 2.2.2处方明细表
                    DataRow detadr = insertdetaildt.NewRow();
                    detadr["PRESC_DATE"] = prescdetadt.Rows[i]["PRESC_DATE"];
                    detadr["PRESC_NO"] = prescdetadt.Rows[i]["PRESC_NO"];
                    detadr["ITEM_NO"] = li_item_no;
                    detadr["DRUG_CODE"] = prescdetadt.Rows[i]["DRUG_CODE"];
                    detadr["DRUG_NAME"] = stockdt.Rows[j]["DRUG_NAME"];
                    detadr["TRADE_NAME"] = stockdt.Rows[j]["TRADE_NAME"];
                    detadr["DRUG_SPEC"] = prescdetadt.Rows[i]["DRUG_SPEC"];
                    detadr["UNITS"] = prescdetadt.Rows[i]["UNITS"];
                    detadr["PACKAGE_SPEC"] = prescdetadt.Rows[i]["PACKAGE_SPEC"];
                    detadr["PACKAGE_UNITS"] = prescdetadt.Rows[i]["PACKAGE_UNITS"];
                    detadr["FIRM_ID"] = prescdetadt.Rows[i]["FIRM_ID"];
                    detadr["BATCH_NO"] = stockdt.Rows[j]["BATCH_NO"];
                    detadr["GEN_SERIAL"] = GetGenSerialSeq();
                    detadr["QUANTITY"] = onecount;
                    detadr["INVENTORY"] = stockcount - onecount;
                    detadr["BATCH_CODE"] = stockdt.Rows[j]["BATCH_CODE"];
                    detadr["EXPIRE_DATE"] = stockdt.Rows[j]["EXPIRE_DATE"] == DBNull.Value ? "" : Convert.ToDateTime(stockdt.Rows[j]["EXPIRE_DATE"]).ToString("yyyy-MM-dd");
                    detadr["PURCHASE_PRICE"] = stockdt.Rows[j]["PURCHASE_PRICE"];
                    detadr["TRADE_PRICE"] = stockdt.Rows[j]["TRADE_PRICE"];
                    detadr["RETAIL_PRICE"] = stockdt.Rows[j]["RETAIL_PRICE"];
                    detadr["SUPPLIER"] = stockdt.Rows[j]["SUPPLIER"];
                    detadr["COSTS"] = (Convert.ToDecimal(stockdt.Rows[j]["RETAIL_PRICE"]) * onecount); //(Convert.ToDecimal(prescdetadt.Rows[i]["COSTS"]) // Convert.ToDecimal(prescdetadt.Rows[i]["PAYMENTS"])) * onecount;
                    detadr["PAYMENTS"] = (Convert.ToDecimal(stockdt.Rows[j]["RETAIL_PRICE"]) * onecount);//(Convert.ToDecimal(prescdetadt.Rows[i]["PAYMENTS"]) / Convert.ToDecimal(prescdetadt.Rows[i]["PAYMENTS"])) * onecount;
                    detadr["ROUND_AMT"] = prescdetadt.Rows[i]["ROUND_AMT"];
                    detadr["ORDER_NO"] = prescdetadt.Rows[i]["ORDER_NO"];
                    detadr["ORDER_SUB_NO"] = prescdetadt.Rows[i]["ORDER_SUB_NO"];
                    detadr["ADMINISTRATION"] = prescdetadt.Rows[i]["ADMINISTRATION"];
                    detadr["DOSAGE_EACH"] = prescdetadt.Rows[i]["DOSAGE_EACH"];
                    detadr["DOSAGE_UNITS"] = prescdetadt.Rows[i]["DOSAGE_UNITS"];
                    detadr["FREQUENCY"] = prescdetadt.Rows[i]["FREQUENCY"];
                    detadr["FREQ_DETAIL"] = prescdetadt.Rows[i]["FREQ_DETAIL"];
                    detadr["HANDBACK_AMOUNT"] = 0;
                    detadr["INSUR_ADULT"] = prescdetadt.Rows[i]["INSUR_ADULT"];
                    detadr["GUID"] = stockdt.Rows[j]["GUID"];

                    insertsql = InsertDoctDrugPrescDetail(detadr);
                    if (!tempsave.ContainsKey(insertsql))
                    {
                        tempsave.Add(insertsql, "新增住院批量处方明细记录失败！");
                    }
                    stockcount = stockcount - onecount;
                    insertdetaildt.Rows.Add(detadr);

                    //发药和摆药为同种药品时，由于先取库存影响明细结存数不准，插入明细后再次根据当前库存更新结存数
                    insertsql = @"update DRUG_PRESC_DETAIL set INVENTORY=(select d.quantity from drug_stock d where d.storage='" + this.DeptCode + "' " +
                        "and d.drug_code = '" + prescdetadt.Rows[i]["DRUG_CODE"] + "' " +
                        "and d.drug_spec = '" + prescdetadt.Rows[i]["DRUG_SPEC"] + "' " +
                        "and d.firm_id = '" + prescdetadt.Rows[i]["FIRM_ID"] + "' " +
                        "and d.package_spec = '" + prescdetadt.Rows[i]["PACKAGE_SPEC"] + "' " +
                        "and d.batch_no = '" + stockdt.Rows[j]["BATCH_NO"] + "' and d.his_unit_code='" + SystemParm.HisUnitCode + "') where DRUG_PRESC_DETAIL.Presc_Date =to_date('" + prescdetadt.Rows[i]["PRESC_DATE"] + "','yyyy-MM-dd HH24:mi:ss')" +
                                                                                        "and DRUG_PRESC_DETAIL.Presc_No = " + prescdetadt.Rows[i]["PRESC_NO"] +
                                                                                        " and DRUG_PRESC_DETAIL.his_unit_code='" + SystemParm.HisUnitCode + "'"+
                                                                                        "and DRUG_PRESC_DETAIL.Item_No =" + li_item_no;
                    if (!tempsave.ContainsKey(insertsql))
                    {
                        tempsave.Add(insertsql, "更新手术处方结存数失败！");
                    }
                    #endregion
                }
                #endregion

                #region 2.3记费-INP_BILL_DETAIL
                int resultindex = InsertInpBill(insertdetaildt, ref allsave, i);
                insertdetaildt.Rows.Clear();
                if (resultindex < 0) return -1;
                #endregion
            }
            #endregion

            #region 3.记煎药费
            if (masterdr["PRESC_TYPE"].ToString() == "1")//中药需要煎药， 插入病人煎药费
            {
                //是否收取煎药费 1收取 0不收
                string decoct = SystemParm.GetParameterValue("DECOCT_INDICATOR", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                if (decoct == "1")
                {
                    if (masterdr["DECOCTION"] != DBNull.Value && masterdr["DECOCTION"].ToString() == "1")
                    {
                        //煎药费在价表中的代码
                        string decoctcode = SystemParm.GetParameterValue("DECOCT_CODE", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                        if (decoctcode == "")
                        {
                            XtraMessageBox.Show("煎药代码无效！", "提示");
                            return -1;
                        }
                        else
                        {
                            int right = InsertDecoctIndicator(ref tempsave);
                            if (right < 0) return -1;
                        }
                    }
                }
            }
            #endregion

            #region 4.回填医生处方-药房、状态（=1）
            insertsql = UpdateDoctPrescMasterByPrescDateAndNo(SystemParm.LoginUser.USER_NAME, this.DeptCode, Convert.ToDateTime(masterdr["PRESC_DATE"]).ToString(), masterdr["PRESC_NO"].ToString());
            if (!allsave.ContainsKey(insertsql))
            {
                allsave.Add(insertsql, "修改住院处方主记录失败！");
            }
            #endregion
            //最终存入保存的sql集合
            for (int ip = 0; ip < tempsave.Count; ip++)
            {
                allsave.Add(tempsave.ElementAt(ip).Key, tempsave.ElementAt(ip).Value);
            }
            return 1;
        }

        /// <summary>
        /// 插入煎药费
        /// </summary>
        private int InsertDecoctIndicator(ref Dictionary<string, string> allsave)
        {
            if (masterdr == null) return -1;
            //非住院处方,直接返回
            if (masterdr["PRESC_SOURCE"] == DBNull.Value || masterdr["PRESC_SOURCE"].ToString() != "1") return -1;
            object repetition = masterdr["REPETITION"] != DBNull.Value ? masterdr["REPETITION"] : 1;
            DataTable doctordt = new DataTable();
            string doctorname = "";
            string doctorgroup = "";
            string doctoruser = "";
            string orderdept = "";//科室
            int maxitemno = 0;//住院收费明细inp_bill_detail的最大项目序号
            doctordt = GetDoctorGroupByDoctorUser(masterdr["DOCTOR_USER"].ToString());
            if (doctordt.Rows.Count == 0)
            {
                doctordt = GetOrderGroupRecByPatientID(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
                if (doctordt.Rows.Count == 0)
                {
                    doctorname = "*";
                    doctorgroup = "*";
                }
                else
                {
                    doctorname = doctordt.Rows[0]["order_doctor"].ToString();
                    doctorgroup = doctordt.Rows[0]["order_group"].ToString();
                }
            }
            else
            {
                doctorname = doctordt.Rows[0]["doctor"].ToString();
                doctorgroup = doctordt.Rows[0]["order_group"].ToString();
            }
            DataTable deptdt = new DataTable();
            deptdt = GetPatInHospDeptCode(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
            if (deptdt.Rows.Count == 0)
            {
                deptdt = GetPatVisitDeptCode(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
                if (deptdt.Rows.Count > 0)
                {
                    orderdept = deptdt.Rows[0]["dept_discharge_from"].ToString();
                }
                else
                {
                    XtraMessageBox.Show("取该病人开单科室出错！", "提示");
                    return -1;
                }
            }
            else
            {
                if (deptdt.Rows[0]["dept_code"] != DBNull.Value)
                {
                    orderdept = deptdt.Rows[0]["dept_code"].ToString();
                }
            }
            string prescdate = masterdr["PRESC_DATE"].ToString();
            string prescno = masterdr["PRESC_NO"].ToString();
            int takingind = masterdr["DISCHARGE_TAKING_INDICATOR"] != DBNull.Value ? Convert.ToInt16(masterdr["DISCHARGE_TAKING_INDICATOR"]) : -1;//带药标志
            string itemclass = masterdr["PRESC_TYPE"].ToString() == "0" ? "A" : "B";
            string verifieddatetime = "";
            string verifyby = "";
            DataTable verifydt = GetPrescMasterByNoAndDate(prescno, Convert.ToDateTime(prescdate).ToString("yyyy-MM-dd"));
            if (verifydt.Rows.Count > 0)
            {
                verifieddatetime = verifydt.Rows[0]["verified_datetime"] != DBNull.Value ? verifydt.Rows[0]["verified_datetime"].ToString() : "";
                verifyby = verifydt.Rows[0]["verify_by"] != DBNull.Value ? verifydt.Rows[0]["verify_by"].ToString() : "";
            }
            DataTable maxitemnodt = GetMaxItemNoByPatientIDAndVisitID(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
            if (maxitemnodt.Rows.Count > 0)
            {
                maxitemno = Convert.ToInt16(maxitemnodt.Rows[0][0]);
            }
            string decoctcode = SystemParm.GetParameterValue("DECOCT_CODE", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            if (decoctcode == "")
            {
                XtraMessageBox.Show("煎药代码无效!", "提示");
                return -1;
            }
            DataTable decoctdt = GetPriceListByDecocotCode(decoctcode);//煎药价表信息
            if (decoctdt.Rows.Count == 0)
            {
                XtraMessageBox.Show("煎药代码对应的价格未设置！", "提示");
                return -1;
            }
            else
            {
                if (decoctdt.Rows[0]["price"] == DBNull.Value)
                {
                    XtraMessageBox.Show("煎药代码对应的价格未知！请查证！", "提示");
                    return -1;
                }
            }
            DataTable coeffnumerator = GetChargePriceSchedule(masterdr["CHARGE_TYPE"].ToString(), SystemParm.HisUnitCode);
            decimal decDefault = 1;
            if (coeffnumerator.Rows.Count > 0)
            {
                decDefault = Convert.ToDecimal(coeffnumerator.Rows[0]["price_coeff_numerator"]);
            }
            decimal decPrice = 0.00m;
            PlatCommon.Common.PublicFunction.GetCalcChargePrice(masterdr["CHARGE_TYPE"].ToString(), itemclass, decoctcode, decoctdt.Rows[0]["item_spec"].ToString(), Convert.ToDecimal(decoctdt.Rows[0]["price"]), ref decDefault, ref decPrice);

            string batchno = "X";
            string batchcode = "X";
            int repetion = Convert.ToInt16(masterdr["REPETITION"]);
            decimal ldec_costs = repetion * Convert.ToDecimal(decoctdt.Rows[0]["price"]);
            decimal ldec_payments = repetion * decPrice;
            decimal factor = 1;
            factor = Math.Round(ldec_payments / ldec_costs, 4);
            string dispensingtime = masterdr["DISPENSING_DATETIME"] == DBNull.Value ? GetSystemTime().ToString("yyyy-MM-dd HH:mm:ss") : masterdr["DISPENSING_DATETIME"].ToString();

            DataTable priceclassdt = GetPriceListClassByItem(itemclass, itemclass, decoctdt.Rows[0]["item_spec"].ToString());
            if (priceclassdt.Rows.Count < 1)
            {
                XtraMessageBox.Show("取不到PRICE_LIST表中字段信息!", "提示");
                return -1;
            }
            string classonmr = priceclassdt.Rows[0]["class_on_mr"] == DBNull.Value ? "" : priceclassdt.Rows[0]["class_on_mr"].ToString();
            DataTable doctgroupdt = GetDoctorGroupByName(doctorname);
            string personggourp = "*";
            if (doctgroupdt.Rows.Count > 0)
            {
                personggourp = doctgroupdt.Rows[0]["order_group"] == DBNull.Value ? doctgroupdt.Rows[0]["order_group"].ToString() : "*";
            }
            string insertsql = InsertInpBillDetail(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString(), maxitemno.ToString(), itemclass, decoctdt.Rows[0]["item_name"].ToString(), decoctcode, decoctdt.Rows[0]["item_spec"].ToString(), repetion.ToString(), decoctdt.Rows[0]["units"].ToString(), orderdept, this.DeptCode, Math.Round(ldec_costs, 2).ToString(), Math.Round(ldec_payments, 2).ToString(), dispensingtime, SystemParm.LoginUser.USER_NAME, priceclassdt.Rows[0]["class_on_reckoning"].ToString(), doctorgroup, doctorname, personggourp, doctorname, masterdr["doctor_user"].ToString(), decoctdt.Rows[0]["price"].ToString(), factor.ToString(), takingind.ToString(), priceclassdt.Rows[0]["class_on_inp_rcpt"].ToString(), priceclassdt.Rows[0]["subj_code"].ToString(), classonmr, "", "A", Convert.ToDateTime(prescdate).ToString("yyyymmdd") + prescno, "", batchno, "", "2");
            if (!allsave.ContainsKey(insertsql))
            {
                allsave.Add(insertsql, "添加住院患者费用明细记录失败！");
            }
            return 1;
        }
        /// <summary>
        /// 初始化处方明细列名
        /// </summary>
        /// <returns></returns>
        private DataTable GetPrescDetailDataRow()
        {
            DataTable detaildt = new DataTable();
            detaildt.Columns.Add("PRESC_DATE");
            detaildt.Columns.Add("PRESC_NO");
            detaildt.Columns.Add("ITEM_NO");
            detaildt.Columns.Add("DRUG_CODE");
            detaildt.Columns.Add("DRUG_NAME");
            detaildt.Columns.Add("TRADE_NAME");
            detaildt.Columns.Add("DRUG_SPEC");
            detaildt.Columns.Add("UNITS");
            detaildt.Columns.Add("PACKAGE_SPEC");
            detaildt.Columns.Add("PACKAGE_UNITS");
            detaildt.Columns.Add("FIRM_ID");
            detaildt.Columns.Add("BATCH_NO");
            detaildt.Columns.Add("GEN_SERIAL");
            detaildt.Columns.Add("QUANTITY");
            detaildt.Columns.Add("INVENTORY");
            detaildt.Columns.Add("BATCH_CODE");
            detaildt.Columns.Add("EXPIRE_DATE");
            detaildt.Columns.Add("PURCHASE_PRICE");
            detaildt.Columns.Add("TRADE_PRICE");
            detaildt.Columns.Add("RETAIL_PRICE");
            detaildt.Columns.Add("SUPPLIER");
            detaildt.Columns.Add("COSTS");
            detaildt.Columns.Add("PAYMENTS");
            detaildt.Columns.Add("ROUND_AMT");
            detaildt.Columns.Add("ORDER_NO");
            detaildt.Columns.Add("ORDER_SUB_NO");
            detaildt.Columns.Add("ADMINISTRATION");
            detaildt.Columns.Add("DOSAGE_EACH");
            detaildt.Columns.Add("DOSAGE_UNITS");
            detaildt.Columns.Add("FREQUENCY");
            detaildt.Columns.Add("FREQ_DETAIL");
            detaildt.Columns.Add("HANDBACK_AMOUNT");
            detaildt.Columns.Add("INSUR_ADULT");
            detaildt.Columns.Add("GUID");
            return detaildt;
        }
        /// <summary>
        /// 清楚基础信息
        /// </summary>
        private void CleanTxt()
        {
            txtPatientID.Text = "";
            txtName.Text = "";
            txtAge.Text = "";
            txtSex.Text = "";
            txtIdentity.Text = "";
            txtChargeType.Text = "";
            sluePrescAttr.EditValue = null;//处方属性
            txtPrescDate.Text = "";//开方日期
            //txtPrescNo.Text = "";
            txtPrescribed.Text = "";//开单医生
            txtDeptName.Text = "";//科室
            //txtYu.Text = "";//预交金
            //txtJi.Text = "";//计价
            //txtYing.Text = "";//应收
            //txtAdmin.Text = "";//用法

            txtRepetition.Text = "";//剂数
            txtCountPerRepetition.Text = "";//每剂/份
            txtEnteredBy.Text = "";
            //txtDischargeTaking.Text = "";
        }
        /// <summary>
        /// 根据处方号和处方日期显示处方信息
        /// </summary>
        /// <param name="rowindex"></param>
        private void SetBaseInfoAndDetailByPrescDateAndPrescNo(int rowindex)
        {
            string prescdate = gv1.GetRowCellValue(rowindex, "PRESC_DATE").ToString();
            string prescno = gv1.GetRowCellValue(rowindex, "PRESC_NO").ToString();
            DataTable masterdt = GetDoctPrescMasterTempByDateAndNo(Convert.ToDateTime(prescdate), prescno);
            if (masterdt.Rows.Count > 0)
            {
                txtPatientID.Text = masterdt.Rows[0]["PATIENT_ID"].ToString();
                txtName.Text = masterdt.Rows[0]["NAME"].ToString();
                txtAge.Text = masterdt.Rows[0]["AGE"].ToString();
                txtSex.Text = masterdt.Rows[0]["SEX"].ToString();
                txtIdentity.Text = masterdt.Rows[0]["IDENTITY"].ToString();
                txtChargeType.Text = masterdt.Rows[0]["CHARGE_TYPE"].ToString();
                sluePrescAttr.EditValue = masterdt.Rows[0]["PRESC_ATTR"];//处方属性
                txtPrescDate.Text = masterdt.Rows[0]["PRESC_DATE"].ToString();//开方日期
                txtPrescribed.Text = masterdt.Rows[0]["PRESCRIBED_BY"].ToString();//开单医生
                txtDeptName.Text = masterdt.Rows[0]["DEPT_NAME"].ToString();//科室
                txtRepetition.Text = masterdt.Rows[0]["REPETITION"].ToString();//剂数
                txtCountPerRepetition.Text = masterdt.Rows[0]["COUNT_PER_REPETITION"] != DBNull.Value ? masterdt.Rows[0]["COUNT_PER_REPETITION"].ToString() : "";//每剂/份
                txtEnteredBy.Text = masterdt.Rows[0]["ENTERED_BY"] == DBNull.Value ? "" : masterdt.Rows[0]["ENTERED_BY"].ToString();

                masterdr = masterdt.Rows[0];
            }
            else
            {
                masterdr = null;
            }
            if (masterdr != null)
            {
                string patientid = masterdr["patient_id"].ToString();
                int visitid = Convert.ToInt16(masterdr["visit_id"]);
                //判断新生儿，预交金
                DataTable newborn = CheckNewBorn(masterdr["patient_id"].ToString());
                if (newborn.Rows.Count > 0)//是新生儿,下面所有的信息都取母亲的信息
                {
                    patientid = newborn.Rows[0]["PATIENT_ID_OF_MOTHER"].ToString();
                    visitid = Convert.ToInt16(newborn.Rows[0]["VISIT_ID_OF_MOTHER"]);
                }
                //预交金总额, 透支额度 未结算费用 预交金余额(根据参数设置, 可能包含透支额度) 可用预交金总额(根据参数设置, 可能包含透支额度)
                decimal ldc_prepayments = 0;
                decimal ldc_approve = 0;
                decimal ldc_charge = 0;
                decimal ldc_prepaybalan = 0;
                decimal ldc_ldprepayment = 0;
                PlatCommon.Common.PublicFunction.GetPrepayment("*", patientid, visitid.ToString(), ref ldc_prepayments, ref ldc_approve, ref ldc_charge, ref ldc_prepaybalan, ref ldc_ldprepayment);
                //txtYu.Text = ldc_prepaybalan.ToString();//预交金
                //判断年龄
                DataTable birthdt = GetBirthDayByPatientID(patientid);
                if (birthdt.Rows.Count > 0)
                {
                    if (PlatCommon.Common.PublicFunction.GetAge(Convert.ToDateTime(birthdt.Rows[0]["date_of_birth"]), Convert.ToDateTime(masterdr["PRESC_DATE"])) == string.Empty)
                    {
                        XtraMessageBox.Show("提取病人年龄出错!", "提示");
                    }
                    txtSex.Text = birthdt.Rows[0]["sex"].ToString();
                }
            }
            //设置明细信息
            DataTable detaildt = GetDoctPrescDetailTempByDateAndNo(Convert.ToDateTime(prescdate), prescno);

            if (detaildt.Rows.Count > 0)
            {
                string chargetype = masterdr != null ? masterdr["CHARGE_TYPE"].ToString() : "";
                string presctype = masterdr != null ? masterdr["PRESC_TYPE"].ToString() : "0";
                string itemclass = presctype == "0" ? "A" : "B";
                decimal retailprice = 0;
                for (int i = 0; i < detaildt.Rows.Count; i++)
                {
                    //if (detaildt.Rows[i]["FIRM_ID"] == DBNull.Value)
                    //{
                    //    XtraMessageBox.Show("第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无厂家，不能发药！", "提示");
                    //    detaildt.Rows[i]["ERR_MSG"] = "第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无厂家，不能发药！";
                    //    continue;
                    //}
                    //if (detaildt.Rows[i]["DRUG_SPEC"] == DBNull.Value)
                    //{
                    //    XtraMessageBox.Show("第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无小规格，不能发药！", "提示");
                    //    detaildt.Rows[i]["ERR_MSG"] = "第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无小规格，不能发药！";
                    //    continue;
                    //}
                    //if (detaildt.Rows[i]["package_spec"] == DBNull.Value)
                    //{
                    //    XtraMessageBox.Show("第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无发药规格，不能发药！", "提示");
                    //    detaildt.Rows[i]["ERR_MSG"] = "第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无发药规格，不能发药！";
                    //    continue;
                    //}
                    DataTable pricedt = GetPriceListByClassCodeSpec(itemclass, detaildt.Rows[i]["DRUG_CODE"].ToString(), detaildt.Rows[i]["package_spec"].ToString() + detaildt.Rows[i]["FIRM_ID"]);
                    if (pricedt.Rows.Count > 0)
                    {
                        retailprice = Convert.ToDecimal(pricedt.Rows[0][0]);
                    }
                    //计算价格
                    DataTable coeffnumerator = GetChargePriceSchedule(chargetype, SystemParm.HisUnitCode);
                    decimal decDefault = 1;
                    if (coeffnumerator.Rows.Count > 0)
                    {
                        decDefault = Convert.ToDecimal(coeffnumerator.Rows[0]["price_coeff_numerator"]);
                    }
                    decimal decChargePrice = 0.00m;

                    PlatCommon.Common.PublicFunction.GetCalcChargePrice(chargetype, itemclass, detaildt.Rows[i]["DRUG_CODE"].ToString(), detaildt.Rows[i]["package_spec"].ToString() + detaildt.Rows[i]["FIRM_ID"], retailprice, ref decDefault, ref decChargePrice);
                    detaildt.Rows[i]["RETAIL_PRICE"] = retailprice;
                    detaildt.Rows[i]["COSTS"] = retailprice * Convert.ToDecimal(detaildt.Rows[i]["QUANTITY"]);
                    detaildt.Rows[i]["PAYMENTS"] = decChargePrice * Convert.ToDecimal(detaildt.Rows[i]["QUANTITY"]);

                }
                //显示零售价和库存
                DataTable drugstockdt = GetDrugQuantityAndStorageAndLocation(detaildt.Rows[0]["DRUG_CODE"].ToString(), detaildt.Rows[0]["FIRM_ID"].ToString(), detaildt.Rows[0]["PACKAGE_SPEC"].ToString(), detaildt.Rows[0]["PACKAGE_UNITS"].ToString(), this.DeptCode);

                DataTable drugprice = GetDrugPriceDrugCode(detaildt.Rows[0]["DRUG_CODE"].ToString(), detaildt.Rows[0]["DRUG_SPEC"].ToString(), detaildt.Rows[0]["FIRM_ID"].ToString());
                if (drugprice.Rows.Count > 0)
                {
                    lbPrice.Text = drugprice.Rows[0]["retail_price"] != DBNull.Value ? drugprice.Rows[0]["retail_price"].ToString() : "";
                }
                else
                {
                    lbPrice.Text = "";
                }
                if (drugstockdt.Rows.Count > 0)
                {
                    lbStockCount.Text = drugstockdt.Rows[0]["quantity"] != DBNull.Value ? drugstockdt.Rows[0]["quantity"].ToString() : "";
                    //lbPrice.Text = drugstockdt.Rows[0]["sub_storage"] != DBNull.Value ? drugstockdt.Rows[0]["sub_storage"].ToString() : "";
                }
                else
                {
                    lbStockCount.Text = "";
                }
            }
            else
            {
                CleanStorgeTxt();
            }
            gc2.DataSource = detaildt;

        }
        /// <summary>
        /// 清空库存信息
        /// </summary>
        private void CleanStorgeTxt()
        {
            lbStockCount.Text = "";
            lbPrice.Text = "";
        }
        /// <summary>
        /// 设置科室
        /// </summary>
        private void SetDept()
        {
            DataTable deptdt = GetAllDept();
            slueDept.Properties.DataSource = deptdt;
            slueDept.Properties.DisplayMember = "DEPT_NAME";
            slueDept.Properties.ValueMember = "DEPT_CODE";
        }
        /// <summary>
        /// 设置处方病人主列表
        /// </summary>
        private void SetPrescMaster()
        {
            string chargeflag = SystemParm.GetParameterValue("CHARGE_FLAG", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            string presc_verified = SystemParm.GetParameterValue("PRESC_VERIFIED", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            chargetypes = SystemParm.GetParameterValue("CHARGETYPES", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            string showPrescDays = SystemParm.GetParameterValue("SHOW_PRESC_DAYS", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            DateTime starttime = DateTime.Now.AddDays(showPrescDays == "" ? -360 : -Convert.ToInt16(showPrescDays));
            DateTime endtime = DateTime.Now.AddDays(1);
            string deptcode = slueDept.EditValue == null ? "" : slueDept.EditValue.ToString();

            DataTable prescmaster = GetDoctDrugPrescMasterByTimeAndDept(starttime, endtime, this.DeptCode, deptcode);
            DataRow[] drs;
            DataTable newmasterdt = prescmaster.Copy();
            gc1.DataSource = newmasterdt;
            if (newmasterdt.Rows.Count > 0)
            {
                SetBaseInfoAndDetailByPrescDateAndPrescNo(0);
            }
        }
        /// <summary>
        /// 设置处方属性
        /// </summary>
        private void SetPrescAttr()
        {
            DataTable prescattr = GetPrescAttr();
            sluePrescAttr.Properties.DataSource = prescattr;
            sluePrescAttr.Properties.DisplayMember = "PRESC_ATTR_NAME";
            sluePrescAttr.Properties.ValueMember = "PRESC_ATTR_NAME";
        }
        #endregion

        #region SQL
        /// <summary>
        /// 删除门诊待发药处方明细表
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private string DeleteDrugPrescDetailTemp(string prescdate, string prescno)
        {
            string sql = "DELETE drug_presc_detail_temp	" +
                "WHERE presc_date = to_date('" + prescdate + "','yyyy-MM-dd HH24:mi:ss') AND presc_no = '" + prescno + "'";
            return sql;
        }
        /// <summary>
        /// 删除门诊待发药处方主表
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private string DeleteDrugPrescMasterTemp(string prescdate, string prescno)
        {
            string sql = "DELETE drug_presc_master_temp	" +
                "WHERE presc_date = to_date('" + prescdate + "','yyyy-MM-dd HH24:mi:ss') AND presc_no = '" + prescno + "'";
            return sql;
        }
        /// <summary>
        /// 获取药品处方属性
        /// </summary>
        /// <returns></returns>
        private DataTable GetPrescAttr()
        {
            string sql = "SELECT D.SERIAL_NO,D.PRESC_ATTR_CODE,D.PRESC_ATTR_NAME  FROM DRUG_PRESC_ATTR_DICT D";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            return dt;
        }
        /// <summary>
        /// 获取打印数据
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private DataTable GetDrugPrescByPrint1(string prescdate, string prescno)
        {
            string sql = "SELECT M.PRESC_DATE,M.PRESC_NO,M.AGE,M.SEX,M.DISPENSARY,'" + this.DeptName + "' as DISPENSARY_NAME,M.PATIENT_ID,(case when (SELECT BR.BED_LABEL FROM BED_REC BR, PATS_IN_HOSPITAL PIH WHERE BR.BED_NO = PIH.BED_NO AND BR.WARD_CODE = PIH.WARD_CODE AND (PIH.PATIENT_ID=M.PATIENT_ID  AND PIH.VISIT_ID=M.VISIT_ID ))= null then(SELECT BR.BED_LABEL FROM BED_REC BR WHERE BR.BED_NO = pt.DISCHARGE_BED_NO AND BR.Dept_Code = pt.dept_discharge_from) else (SELECT BR.BED_LABEL FROM BED_REC BR,PATS_IN_HOSPITAL PIH WHERE BR.BED_NO = PIH.BED_NO AND BR.WARD_CODE = PIH.WARD_CODE AND (PIH.PATIENT_ID=M.PATIENT_ID  AND PIH.VISIT_ID=M.VISIT_ID )) end) as BED_LABEL,M.NAME||'  '||(case when (SELECT BR.BED_LABEL FROM BED_REC BR, PATS_IN_HOSPITAL PIH WHERE BR.BED_NO = PIH.BED_NO AND BR.WARD_CODE = PIH.WARD_CODE AND (PIH.PATIENT_ID=M.PATIENT_ID  AND PIH.VISIT_ID=M.VISIT_ID ))= null then(SELECT BR.BED_LABEL FROM BED_REC BR WHERE BR.BED_NO = pt.DISCHARGE_BED_NO AND BR.Dept_Code = pt.dept_discharge_from) else (SELECT BR.BED_LABEL FROM BED_REC BR,PATS_IN_HOSPITAL PIH WHERE BR.BED_NO = PIH.BED_NO AND BR.WARD_CODE = PIH.WARD_CODE AND (PIH.PATIENT_ID=M.PATIENT_ID  AND PIH.VISIT_ID=M.VISIT_ID )) end) || '床' AS NAME, M.IDENTITY,M.CHARGE_TYPE,M.UNIT_IN_CONTRACT,M.PRESC_SOURCE,M.REPETITION,M.ORDERED_BY,M.PRESCRIBED_BY,D.ITEM_NO,D.DRUG_NAME,D.FIRM_ID,D.PACKAGE_SPEC,D.PACKAGE_UNITS,D.QUANTITY,M.NAME_PHONETIC,M.PRESC_TYPE,M.PRESC_ATTR,D.COSTS DCOSTS,D.PAYMENTS,M.ENTERED_BY,D.ADMINISTRATION,D.ORDER_NO,D.ORDER_SUB_NO,D.DRUG_CODE,D.DRUG_SPEC,M.DISPENSING_PROVIDER,'' Constrained_level_name,M.COSTS,P.sex,P.DATE_OF_BIRTH,D.DOSAGE_EACH,D.DOSAGE_UNITS,D.FREQUENCY,M.RCPT_NO,M.verify_by,D.DRUG_NAME || '  ' || D.QUANTITY || D.PACKAGE_UNITS bzxx,T.DEPT_NAME,trunc(D.COSTS/D.QUANTITY,2) DACOSTS  FROM DRUG_PRESC_DETAIL D, DRUG_PRESC_MASTER M, PAT_MASTER_INDEX P,DEPT_DICT T,pat_visit pt WHERE (D.PRESC_NO = M.PRESC_NO) and (D.PRESC_DATE = M.PRESC_DATE) and M.patient_id = P.patient_id(+)  AND (M.ORDERED_BY=T.DEPT_CODE) and (M.PATIENT_ID =pt.patient_id and M.Visit_Id=pt.visit_id) and ((D.PRESC_DATE = to_date(:presc_date, 'yyyy-MM-dd HH24:mi:ss')) AND (D.PRESC_NO = :presc_no))";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("presc_date");
            para.Add("presc_no");
            para_val.Add(prescdate);
            para_val.Add(prescno);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取打印数据
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private DataTable GetDrugPrescByPrint(string prescdate, string prescno)
        {
            string sql = "SELECT M.PRESC_DATE,M.PRESC_NO,M.AGE,M.SEX,M.DISPENSARY,'" + this.DeptName + "' as DISPENSARY_NAME,M.PATIENT_ID,(case when (SELECT BR.BED_LABEL FROM BED_REC BR,PATS_IN_HOSPITAL PIH WHERE BR.BED_NO = PIH.BED_NO AND BR.WARD_CODE = PIH.WARD_CODE AND (PIH.PATIENT_ID=M.PATIENT_ID  AND PIH.VISIT_ID=M.VISIT_ID ))= null then(SELECT BR.BED_LABEL FROM BED_REC BR WHERE BR.BED_NO = pt.DISCHARGE_BED_NO AND BR.Dept_Code = pt.dept_discharge_from) else (SELECT BR.BED_LABEL FROM BED_REC BR,PATS_IN_HOSPITAL PIH WHERE BR.BED_NO = PIH.BED_NO AND BR.WARD_CODE = PIH.WARD_CODE AND (PIH.PATIENT_ID=M.PATIENT_ID  AND PIH.VISIT_ID=M.VISIT_ID )) end) as BED_LABEL,M.NAME||'  '||(case when (SELECT BR.BED_LABEL FROM BED_REC BR,PATS_IN_HOSPITAL PIH WHERE BR.BED_NO = PIH.BED_NO AND BR.WARD_CODE = PIH.WARD_CODE AND (PIH.PATIENT_ID=M.PATIENT_ID  AND PIH.VISIT_ID=M.VISIT_ID ))= null then(SELECT BR.BED_LABEL FROM BED_REC BR WHERE BR.BED_NO = pt.DISCHARGE_BED_NO AND BR.Dept_Code = pt.dept_discharge_from) else (SELECT BR.BED_LABEL FROM BED_REC BR,PATS_IN_HOSPITAL PIH WHERE BR.BED_NO = PIH.BED_NO AND BR.WARD_CODE = PIH.WARD_CODE AND (PIH.PATIENT_ID=M.PATIENT_ID  AND PIH.VISIT_ID=M.VISIT_ID )) end) || '床' AS NAME, M.IDENTITY,M.CHARGE_TYPE,M.UNIT_IN_CONTRACT,M.PRESC_SOURCE,M.REPETITION,M.ORDERED_BY,M.PRESCRIBED_BY,M.VISIT_ID,D.ITEM_NO,D.DRUG_NAME,D.FIRM_ID,D.PACKAGE_SPEC,D.PACKAGE_UNITS,D.QUANTITY,M.NAME_PHONETIC,M.PRESC_TYPE,M.COSTS,M.PAYMENTS,M.ENTERED_BY,M.COUNT_PER_REPETITION,D.COSTS DCOSTS,D.FREQUENCY,D.DOSAGE_EACH,D.DOSAGE,D.DOSAGE_UNITS,D.ADMINISTRATION,M.USAGE,M.DISCHARGE_TAKING_INDICATOR,T.DEPT_NAME,trunc(D.COSTS/D.QUANTITY,2) DACOSTS FROM DOCT_DRUG_PRESC_DETAIL D, DOCT_DRUG_PRESC_MASTER M,DEPT_DICT T,pat_visit pt WHERE (D.PRESC_NO = M.PRESC_NO) and (D.PRESC_DATE = M.PRESC_DATE)  AND (M.ORDERED_BY=T.DEPT_CODE) and (M.PATIENT_ID =pt.patient_id and M.Visit_Id=pt.visit_id) and ((D.PRESC_DATE = to_date(:presc_date, 'yyyy-MM-dd HH24:mi:ss')) and (D.PRESC_NO = :presc_no))";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("presc_date");
            para.Add("presc_no");
            para_val.Add(prescdate);
            para_val.Add(prescno);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取处方发药主记录
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private DataTable GetDrugPrescMasterByPrescDateAndNo(string prescdate, string prescno)
        {
            string sql = "select patient_id,presc_type,prescribed_by from drug_presc_master where presc_date=to_date(:presc_date, 'yyyy-MM-dd HH24:mi:ss') and presc_no=:presc_no";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("presc_date");
            para.Add("presc_no");
            para_val.Add(prescdate);
            para_val.Add(prescno);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取医生分组
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        private DataTable GetDoctorGroupByName(string name)
        {
            string sql = "select distinct order_group from doctor_group where doctor=:as_perform_name";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_perform_name");
            para_val.Add(name);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="itemclass"></param>
        /// <param name="itemcode"></param>
        /// <param name="itemspec"></param>
        /// <returns></returns>
        private DataTable GetPriceListClassByItem(string itemclass, string itemcode, string itemspec)
        {
            string sql = "SELECT item_name,class_on_inp_rcpt,class_on_reckoning,subj_code,class_on_mr  FROM CURRENT_PRICE_LIST  WHERE ( ITEM_CLASS = :as_item_class ) and ( ITEM_CODE = :as_item_code ) and ( ITEM_SPEC = :as_item_spec ) and ( SYSDATE >= start_date and (SYSDATE < stop_date or stop_date is null))  and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' and   ROWNUM = 1";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_item_class");
            para.Add("as_item_code");
            para.Add("as_item_spec");
            para_val.Add(itemclass);
            para_val.Add(itemcode);
            para_val.Add(itemspec);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];

        }
        /// <summary>
        /// 住院患者费用明细
        /// </summary>
        /// <returns></returns>
        private string InsertInpBillDetail(string as_patient_id, string al_visit_id, string al_item_no, string as_item_class, string ls_item_name, string as_item_code, string as_item_spec, string adec_amount, string as_item_units, string as_order_by, string as_performed_by, string ldec_cur_costs, string ldec_cur_charges, string ldt_date, string as_operator_code, string ls_class_on_reckoning, string as_order_group, string as_order_name, string ls_perform_group, string as_perform_name, string as_order_doctor_code, string adec_price, string ldec_factor, string al_discharge_taking, string ls_class_on_inp_rcpt, string ls_subj_code, string ls_class_on_mr, string as_rcpt_no, string as_oper_type, string as_oper_code, string as_memo, string as_batch_no, string tradeprice, string sourceflag)
        {
            string sql = "insert into inp_bill_detail (patient_id,visit_id,item_no,item_class,item_name,item_code,item_spec,amount,units,ordered_by,performed_by,costs,charges,billing_date_time,operator_no,class_on_reckoning,order_group,order_doctor,perform_group,perform_doctor,doctor_user,item_price,price_quotiety,discharge_taking_indicator,class_on_inp_rcpt,subj_code,class_on_mr,rcpt_no,oper_type,oper_code,memo,drug_batch_no,HIS_UNIT_CODE) values('" + as_patient_id + "','" + al_visit_id + "','" + al_item_no + "','" + as_item_class + "','" + ls_item_name + "','" + as_item_code + "','" + as_item_spec + "','-" + adec_amount + "','" + as_item_units + "','" + as_order_by + "','" + as_performed_by + "','-" + ldec_cur_costs + "','-" + ldec_cur_charges + "',to_date('" + ldt_date + "', 'yyyy-MM-dd HH24:mi:ss'),'" + as_operator_code + "','" + ls_class_on_reckoning + "','" + as_order_group + "','" + as_order_name + "','" + ls_perform_group + "','" + as_perform_name + "','" + as_order_doctor_code + "','" + adec_price + "','" + ldec_factor + "','" + al_discharge_taking + "','" + ls_class_on_inp_rcpt + "','" + ls_subj_code + "','" + ls_class_on_mr + "','" + as_rcpt_no + "','" + as_oper_type + "','" + as_oper_code + "','" + as_memo + "','" + as_batch_no + "','" + tradeprice + "','" + sourceflag + "','" + SystemParm.HisUnitCode + "')";
            return sql;
        }
        /// <summary>
        /// 根据煎药代码去价钱
        /// </summary>
        /// <param name="decoctcode"></param>
        /// <returns></returns>
        private DataTable GetPriceListByDecocotCode(string decoctcode)
        {
            string sql = "select price,item_name,item_spec,units,item_class from CURRENT_PRICE_LIST where item_code=:ls_drug_code  and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' and (start_date<=sysdate and (stop_date>sysdate or stop_date is null))";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_drug_code");
            para_val.Add(decoctcode);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取住院收费明细inp_bill_detail的最大项目序号
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetMaxItemNoByPatientIDAndVisitID(string patientid, string visitid)
        {
            string sql = "select max(item_no) FROM inp_bill_detail WHERE patient_id = :ls_patient_id AND visit_id = :ll_visit_id and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para.Add("ll_visit_id");
            para_val.Add(patientid);
            para_val.Add(visitid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 根据医生变化获取科室编号
        /// </summary>
        /// <param name="doctoruser"></param>
        /// <returns></returns>
        private DataTable GetDeptCodeByUserName(string doctoruser)
        {
            string sql = "select t.dept_code from staff_dict t where t.user_name=:ls_doctor_user";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_doctor_user");
            para_val.Add(doctoruser);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取审核人审核时间
        /// </summary>
        /// <param name="prescno"></param>
        /// <param name="prescdate"></param>
        /// <returns></returns>
        private DataTable GetPrescMasterByNoAndDate(string prescno, string prescdate)
        {
            string sql = "select  verified_datetime, verify_by from doct_drug_presc_master where presc_no = :ll_presc_no and presc_date =to_date(:ldt_presc_date,'yyyy-mm-dd hh24:mi:ss') and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ll_presc_no");
            para.Add("ldt_presc_date");
            para_val.Add(prescno);
            para_val.Add(prescdate);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 开单科室不从医生对应的科室取，改为病人所在的科室 
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetPatVisitDeptCode(string patientid, string visitid)
        {
            string sql = "select dept_discharge_from from pat_visit where  patient_id=:ls_patient_id and visit_id=:ll_visit_id and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para.Add("ll_visit_id");
            para_val.Add(patientid);
            para_val.Add(visitid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 开单科室不从医生对应的科室取，改为病人所在的科室 
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetPatInHospDeptCode(string patientid, string visitid)
        {
            string sql = "select dept_code from pats_in_hospital where  patient_id=:ls_patient_id and visit_id=:ll_visit_id and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para.Add("ll_visit_id");
            para_val.Add(patientid);
            para_val.Add(visitid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 找不到用 经治医生
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetOrderGroupRecByPatientID(string patientid, string visitid)
        {
            string sql = "select doctor_user,order_doctor,order_group from orders_group_rec	where patient_id = :ls_patient_id and visit_id = :ll_visit_id";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para.Add("ll_visit_id");
            para_val.Add(patientid);
            para_val.Add(visitid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 取开单医生代码,开单医生姓名 ,开单医生组
        /// </summary>
        /// <param name="doctoruser"></param>
        /// <returns></returns>
        private DataTable GetDoctorGroupByDoctorUser(string doctoruser)
        {
            string sql = "select doctor,order_group from doctor_group where doctor_user = :ls_doctor_user ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_doctor_user");
            para_val.Add(doctoruser);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 返回库存
        /// </summary>
        /// <returns></returns>
        private DataTable GetDrugStockForUpdate(string storagecode, string drugcode, string drugspec, string packagespec, string firmid)
        {
            string sql = "SELECT S.DRUG_CODE,S.DRUG_NAME,S.TRADE_NAME,D.DOSE_PER_UNIT,D.DOSE_UNITS," +
                "0.0000000000 orders_dosage_in_stock_units,S.DRUG_SPEC," +
                "S.UNITS,S.FIRM_ID,S.PACKAGE_SPEC,S.PACKAGE_UNITS,S.BATCH_NO," +
                "S.QUANTITY,S.BATCH_CODE,S.EXPIRE_DATE,S.PURCHASE_PRICE," +
                "S.DISCOUNT,S.TRADE_PRICE,S.RETAIL_PRICE,S.SUPPLIER," +
                "S.SUB_STORAGE,S.LOCATION_CODE,(select max(amount_per_package) " +
                "from CURRENT_DRUG_MD_PRICE_LIST temp where temp.drug_code =S.drug_code " +
                "and temp.min_spec=S.drug_spec and temp.drug_spec=S.package_spec " +
                "and temp.firm_id = S.firm_id  and temp.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' and temp.START_DATE <= sysdate " +
                "and (temp.STOP_DATE >= sysdate OR temp.STOP_DATE is null ) ) amount_per_package," +
                "S.STORAGE,S.PACKAGE_1,S.PACKAGE_SPEC_1,S.PACKAGE_UNITS_1,S.PACKAGE_2," +
                "S.PACKAGE_SPEC_2,S.PACKAGE_UNITS_2,S.SUPPLY_INDICATOR,S.DOCUMENT_NO," +
                "S.PURCHASE_PRICE_LAST,S.FROZEN_FLAG,S.QUANTITY_PRE,S.LAST_UPDATETIME , S.GUID " +
                "FROM DRUG_STOCK S,DRUG_DICT D WHERE ( S.DRUG_CODE = D.DRUG_CODE ) " +
                "and  ( D.DRUG_SPEC = S.DRUG_SPEC ) and  ( ( S.STORAGE = :as_storage_code ) " +
                "AND  ( S.SUPPLY_INDICATOR = 1 ) )  AND ( S.QUANTITY > 0 ) " +
                "  and S.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'"+
                "and S.drug_code =:as_drug_code and S.drug_spec =:as_drug_spec " +
                "and S.package_spec =:as_package_spec and S.firm_id =:as_firm_id ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_storage_code");
            para.Add("as_drug_code");
            para.Add("as_drug_spec");
            para.Add("as_package_spec");
            para.Add("as_firm_id");
            para_val.Add(storagecode);
            para_val.Add(drugcode);
            para_val.Add(drugspec);
            para_val.Add(packagespec);
            para_val.Add(firmid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 修改住院处方主表
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private string UpdateDoctPrescMasterByPrescDateAndNo(string username, string storagecode, string prescdate, string prescno)
        {
            string sql = "UPDATE DOCT_DRUG_PRESC_MASTER SET DISPENSING_PROVIDER='" + username + "',PRESC_STATUS='1',DISPENSARY='" + storagecode + "' WHERE PRESC_DATE = to_date('" + prescdate + "','yyyy-MM-dd HH24:mi:ss') AND PRESC_NO='" + prescno + "'";
            return sql;
        }
        /// <summary>
        /// 添加处方明细表
        /// </summary>
        /// <param name="ddr"></param>
        /// <returns></returns>
        private string InsertDoctDrugPrescDetail(DataRow ddr)
        {
            string sql = "Insert into DRUG_PRESC_DETAIL(  " +
                "PRESC_DATE,PRESC_NO,ITEM_NO,DRUG_CODE,DRUG_NAME," +
                "TRADE_NAME,DRUG_SPEC,UNITS,PACKAGE_SPEC,PACKAGE_UNITS," +
                "FIRM_ID,BATCH_NO,GEN_SERIAL,QUANTITY,INVENTORY,BATCH_CODE," +
                "EXPIRE_DATE,PURCHASE_PRICE,TRADE_PRICE,RETAIL_PRICE,SUPPLIER," +
                "COSTS,PAYMENTS,ROUND_AMT,ORDER_NO,ORDER_SUB_NO,ADMINISTRATION," +
                "DOSAGE_EACH,DOSAGE_UNITS,FREQUENCY,FREQ_DETAIL," +
                "HANDBACK_AMOUNT,INSUR_ADULT , GUID,HIS_UNIT_CODE " +
                " ) values (to_date('" + ddr["PRESC_DATE"] + "','yyyy-MM-dd HH24:mi:ss'),'" + ddr["PRESC_NO"] + "','" + ddr["ITEM_NO"] + "','" + ddr["DRUG_CODE"] + "','" + ddr["DRUG_NAME"] + "','"
                + ddr["TRADE_NAME"] + "','" + ddr["DRUG_SPEC"] + "','" + ddr["UNITS"] + "','" + ddr["PACKAGE_SPEC"] + "','" + ddr["PACKAGE_UNITS"] + "','"
                + ddr["FIRM_ID"] + "','" + ddr["BATCH_NO"] + "','" + ddr["GEN_SERIAL"] + "','" + ddr["QUANTITY"] + "','" + ddr["INVENTORY"] + "','" + ddr["BATCH_CODE"]
                + "',to_date('" + ddr["EXPIRE_DATE"] + "','yyyy-MM-dd'),'" + ddr["PURCHASE_PRICE"] + "','" + ddr["TRADE_PRICE"] + "','" + ddr["RETAIL_PRICE"] + "','" + ddr["SUPPLIER"] + "','"
                + ddr["COSTS"] + "','" + ddr["PAYMENTS"] + "','" + ddr["ROUND_AMT"] + "','" + ddr["ORDER_NO"] + "','" + ddr["ORDER_SUB_NO"] + "','" + ddr["ADMINISTRATION"] + "','"
                + ddr["DOSAGE_EACH"] + "','" + ddr["DOSAGE_UNITS"] + "','" + ddr["FREQUENCY"] + "','" + ddr["FREQ_DETAIL"] + "','"
                + ddr["HANDBACK_AMOUNT"] + "','" + ddr["INSUR_ADULT"] + "','" + ddr["GUID"] + "','" + SystemParm.HisUnitCode + "')";
            return sql;
        }
        /// <summary>
        /// 生成序号
        /// </summary>
        /// <returns></returns>
        private string GetGenSerialSeq()
        {
            //string sql = "select GEN_SERIAL_SEQ.nextval from dual";
            //DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            //if (dt.Rows.Count < 1)
            //{
            //    return "";
            //}
            //else
            //{
            //    return dt.Rows[0][0] == DBNull.Value ? "" : dt.Rows[0][0].ToString();
            //}
            //改成从配置表取 by lions 2018-06-27
            #region
            string sysdate = PlatCommon.Common.PublicFunction.GetSysDate();
            string ls_gen_serail = "";
            if (!PlatCommon.Common.PublicFunction.GetSequeceFromAuto("药品出入库单号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ls_gen_serail) || string.IsNullOrEmpty(ls_gen_serail) || string.IsNullOrEmpty(sysdate))
            {
                return "";//获取序列失败
            }
            ls_gen_serail = DateTime.Parse(sysdate).ToString("yyMMdd") + ls_gen_serail;
            return ls_gen_serail;
            #endregion
        }
        /// <summary>
        ///修改药品库存 - 修复：改为扣减操作，防止负库存
        /// </summary>
        /// <returns></returns>
        private string UpdateDrugStock(decimal quantity, string storagecode, string drugcode, string drugspec, string packagespec, string firmid, string batchno)
        {
            // 2025-01-17 修复：改为扣减操作，并添加库存充足性检查
            // 原代码：QUANTITY='{quantity}' 直接设置库存值（危险操作）
            // 修复后：QUANTITY = QUANTITY - {quantity} 扣减库存，并检查扣减后不为负数
            string sql = "UPDATE DRUG_STOCK SET QUANTITY = QUANTITY - " + quantity + ",LAST_UPDATETIME=SYSDATE " +
                "WHERE STORAGE = '" + storagecode + "' AND SUPPLY_INDICATOR = 1 " +
                "AND QUANTITY >= " + quantity + " " + // 确保库存充足
                "AND drug_code = '" + drugcode + "' and drug_spec = '" + drugspec + "' " +
                "and package_spec = '" + packagespec + "' and firm_id = '" + firmid + "' " +
                "and BATCH_NO='" + batchno + "' and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            return sql;

        }
        /// <summary>
        /// 添加处方主表
        /// </summary>
        /// <param name="mdr"></param>
        /// <returns></returns>
        private string InsertDoctDrugPrescMaster(DataRow mdr, string batchno, string storagecode, string DISPENSING_DATETIME)
        {
            string sql = "INSERT INTO DRUG_PRESC_MASTER(PRESC_DATE,PRESC_NO,DISPENSARY,PATIENT_ID,VISIT_ID,";
            sql += " NAME,NAME_PHONETIC,AGE,IDENTITY,CHARGE_TYPE,UNIT_IN_CONTRACT, PRESC_TYPE,";
            sql += "PRESC_ATTR,PRESC_SOURCE, DECOCTION,REPETITION,COUNT_PER_REPETITION,";
            sql += "ORDERED_BY,DOCTOR_USER,PRESCRIBED_USERCODE,PRESCRIBED_BY,ENTERED_USERCODE,ENTERED_BY,";
            sql += "ENTERED_DATETIME,VERIFY_USERCODE,VERIFY_BY,VERIFIED_DATETIME, COSTS,PAYMENTS,";
            sql += "ROUND_AMT,DISPENSING_USERCODE,DISPENSING_PROVIDER,DISPENSING_DATETIME,FLAG,";
            sql += "ORIGINAL_PRESC_DATE,ORIGINAL_PRESC_NO, ";
            sql += " sex,batch_provide_no,HIS_UNIT_CODE) ";
            sql += " VALUES (to_date('" + mdr["PRESC_DATE"] + "','yyyy-MM-dd HH24:mi:ss'),'";
            sql += mdr["PRESC_NO"] + "','" + mdr["DISPENSARY"] + "','" + mdr["PATIENT_ID"] + "' ,'";
            sql += mdr["VISIT_ID"] + "' ,'" + mdr["NAME"] + "' ,'";
            sql += mdr["NAME_PHONETIC"] + "' ,'" + mdr["AGE"] + "' ,'" + mdr["IDENTITY"] + "' ,'";
            sql += mdr["CHARGE_TYPE"] + "' ,'" + mdr["UNIT_IN_CONTRACT"] + "' ,'";
            sql += mdr["PRESC_TYPE"] + "' ,'" + mdr["PRESC_ATTR"] + "' ,'" + mdr["PRESC_SOURCE"];
            sql += "' ,'" + mdr["DECOCTION"] + "' ,'";
            sql += mdr["REPETITION"] + "' ,'" + mdr["COUNT_PER_REPETITION"] + "' ,'" + mdr["ORDERED_BY"];
            sql += "' ,'" + mdr["DOCTOR_USER"] + "' ,'" + mdr["PRESCRIBED_USERCODE"] + "' ,'";
            sql += mdr["PRESCRIBED_BY"] + "' ,'" + mdr["ENTERED_USERCODE"] + "' ,'" + mdr["ENTERED_BY"];
            sql += "' ,to_date('" + mdr["ENTERED_DATETIME"] + "','yyyy-MM-dd HH24:mi:ss')  ,'";
            sql += mdr["VERIFY_USERCODE"] + "' ,'" + mdr["VERIFY_BY"] + "' ,to_date('" + mdr["VERIFIED_DATETIME"] + "','yyyy-MM-dd HH24:mi:ss') ";
            sql += " ,'" + mdr["COSTS"] + "' ,'" + mdr["PAYMENTS"] + "' ,'";
            sql += mdr["ROUND_AMT"] + "' ,'" + mdr["DISPENSING_USERCODE"] + "' ,'";
            sql += mdr["DISPENSING_PROVIDER"] + "' ,to_date('" + DISPENSING_DATETIME;
            sql += "','yyyy-MM-dd HH24:mi:ss') ,1 ,to_date('" + mdr["PRESC_DATE"] + "','yyyy-MM-dd HH24:mi:ss') ,'";
            sql += mdr["PRESC_NO"] + "' ,'" + mdr["sex"] + "','"+ batchno + "','" + SystemParm.HisUnitCode + "')";
            return sql;
        }
        /// <summary>
        /// 返回预交金额
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetPrepaymentByPatientID(string patientid, int visitid)
        {
            string sql = "SELECT SUM(AMOUNT) FROM PREPAYMENT_RCPT WHERE PAY_WAY ='现金' AND PATIENT_ID =:as_patient_id AND VISIT_ID = :ai_visit_id AND TRANSACT_TYPE <>'作废'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_patient_id");
            para.Add("ai_visit_id");
            para_val.Add(patientid);
            para_val.Add(visitid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 判断病人是否出院或者已办理结帐
        /// </summary>
        /// <param name="patientid"></param>
        /// <returns></returns>
        private DataTable GetPatInHospitapByPatientID(string patientid)
        {
            string sql = "SELECT settled_indicator FROM pats_in_hospital Where patient_id = :ls_patient_id and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para_val.Add(patientid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 判断病人是不是出院全结
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetInpSettleMasterByPatientID(string patientid, int visitid)
        {
            string sql = "SELECT count(*) From inp_settle_master Where transact_type = '正常' And settle_type_name = '出院全结' And patient_id = :ls_patient_id And visit_id = :ll_visit_id and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para.Add("ll_visit_id");
            para_val.Add(patientid);
            para_val.Add(visitid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取批量发药单据号
        /// </summary>
        /// <returns></returns>
        private DataTable GetBatchProvideNo()
        {
            string sql = "SELECT to_char(sysdate,'yyyymmdd')||(lpad(pharmacy.batch_provide_no_seq.nextval,6,'0')) From dual";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            return dt;
        }
        /// <summary>
        /// 获取系统时间
        /// </summary>
        /// <returns></returns>
        private DateTime GetSystemTime()
        {
            DateTime dt = new ServerPublicClient().GetSysDate();
            return dt;
        }
        /// <summary>
        /// 获取药品库存
        /// </summary>
        /// <param name="storagecode"></param>
        /// <param name="drugcode"></param>
        /// <param name="firmid"></param>
        /// <param name="packagespec"></param>
        /// <returns></returns>
        private DataTable GetDrugStockByDrugCode(string storagecode, string drugcode, string firmid, string packagespec)
        {
            string sql = "select sum(drug_stock.quantity) FROM drug_stock where  drug_stock.storage= :gs_storage_code and drug_stock.drug_code= :ls_drug_code and drug_stock.firm_id = :ls_firm_id  and drug_stock.PACKAGE_SPEC  = :ls_package_spec and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("gs_storage_code");
            para.Add("ls_drug_code");
            para.Add("ls_firm_id");
            para.Add("ls_package_spec");
            para_val.Add(storagecode);
            para_val.Add(drugcode);
            para_val.Add(firmid);
            para_val.Add(packagespec);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取药品价格
        /// </summary>
        /// <param name="drugcode"></param>
        /// <param name="drugspec"></param>
        /// <param name="firmid"></param>
        /// <returns></returns>
        private DataTable GetDrugPriceDrugCode(string drugcode, string drugspec, string firmid)
        {
            string sql = "select TRADE_PRICE,retail_price from CURRENT_DRUG_MD_PRICE_LIST where drug_code=:as_DRUG_CODE and drug_spec=:as_package_spec  and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' and firm_id=:as_firm_id and (start_date<sysdate and (stop_date>=sysdate or stop_date is null))";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_DRUG_CODE");
            para.Add("as_package_spec");
            para.Add("as_firm_id");
            para_val.Add(drugcode);
            para_val.Add(drugspec);
            para_val.Add(firmid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 判断是否是新生儿
        /// </summary>
        /// <returns></returns>
        private DataTable CheckNewBorn(string patiendid)
        {
            string sql = "select patient_id_of_mother, visit_id_of_mother  from newborn_rec  where patient_id = :ls_patient_id and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para_val.Add(patiendid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取收费系数字典
        /// </summary>
        /// <param name="chargetype"></param>
        /// <param name="hisunitcode"></param>
        /// <returns></returns>
        private DataTable GetChargePriceSchedule(string chargetype, string hisunitcode)
        {
            string sql = "Select price_coeff_numerator, price_coeff_denominator, charge_special_indicator From charge_price_schedule Where charge_type = :chargetype and his_unit_code=:hisunitcode";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("chargetype");
            para.Add("hisunitcode");
            para_val.Add(chargetype);
            para_val.Add(hisunitcode);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取药品价格
        /// </summary>
        /// <param name="itemclass"></param>
        /// <param name="itemcode"></param>
        /// <param name="itemspec"></param>
        /// <returns></returns>
        private DataTable GetPriceListByClassCodeSpec(string itemclass, string itemcode, string itemspec)
        {
            string sql = " SELECT p.price FROM current_price_list p WHERE p.item_class = :ls_item_class  AND  p.item_code = :ls_item_code  AND  p.item_spec = :ls_item_spec  AND  ( sysdate >= p.start_date ) AND	( sysdate <= p.stop_date Or p.stop_date Is null)  and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_item_class");
            para.Add("ls_item_code");
            para.Add("ls_item_spec");
            para_val.Add(itemclass);
            para_val.Add(itemcode);
            para_val.Add(itemspec);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 根据处方号和时间获取住院处方明细信息
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private DataTable GetDoctPrescDetailTempByDateAndNo(DateTime prescdate, string prescno)
        {
            string sql = "SELECT T.PRESC_DATE,T.PRESC_NO,T.ITEM_NO,T.DRUG_CODE," +
                "T.DRUG_NAME,T.FIRM_ID,T.PACKAGE_SPEC,T.PACKAGE_UNITS,T.QUANTITY," +
                "T.COSTS,T.PAYMENTS,T.DRUG_SPEC,T.UNITS,T.DOSAGE_EACH,T.ADMINISTRATION," +
                "T.ORDER_NO,T.ORDER_SUB_NO,T.BATCH_NO, T.ROUND_AMT," +
                "T.FREQUENCY,T.FREQ_DETAIL,T.BATCH_CODE,T.TRADE_NAME,T.DOSAGE_UNITS," +
                "T.RETAIL_PRICE,T.INSUR_ADULT " +
                "FROM DOCT_DRUG_PRESC_DETAIL T WHERE (T.PRESC_DATE = :adt_presc_date) AND (T.PRESC_NO = :al_presc_no) and T.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("adt_presc_date");
            para.Add("al_presc_no");
            para_val.Add(prescdate);
            para_val.Add(prescno);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取药品库存量，仓库，货位，批次
        /// </summary>
        /// <param name="drugcode"></param>
        /// <param name="firmid"></param>
        /// <param name="packagespec"></param>
        /// <param name="packageunit"></param>
        /// <param name="storagecode"></param>
        /// <returns></returns>
        private DataTable GetDrugQuantityAndStorageAndLocation(string drugcode, string firmid, string packagespec, string packageunit, string storagecode)
        {
            string sql = "SELECT QUANTITY,SUPPLY_INDICATOR,SUB_STORAGE,LOCATION_CODE,BATCH_NO FROM DRUG_STOCK WHERE DRUG_CODE=:drug_code1 AND FIRM_ID=:firm_id1 AND PACKAGE_SPEC=:package_spec1	AND PACKAGE_UNITS=:package_units1 AND STORAGE=:gs_storage_code order by batch_no ASC";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("drug_code1");
            para.Add("firm_id1");
            para.Add("package_spec1");
            para.Add("package_units1");
            para.Add("gs_storage_code");
            para_val.Add(drugcode);
            para_val.Add(firmid);
            para_val.Add(packagespec);
            para_val.Add(packageunit);
            para_val.Add(storagecode);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 根据病人ID获取生日
        /// </summary>
        /// <param name="patientid"></param>
        /// <returns></returns>
        private DataTable GetBirthDayByPatientID(string patientid)
        {
            string sql = "select sex,date_of_birth from pat_master_index where patient_id = :fs_Patient_Id";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("fs_Patient_Id");
            para_val.Add(patientid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 根据处方号和时间获取住院处方主信息
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private DataTable GetDoctPrescMasterTempByDateAndNo(DateTime prescdate, string prescno)
        {
            string sql = $@"SELECT T.PRESC_DATE,T.PRESC_NO,T.DISPENSARY,
                T.PATIENT_ID,T.VISIT_ID, T.NAME,T.NAME_PHONETIC,
                T.SEX,T.AGE,T.IDENTITY,T.CHARGE_TYPE,T.UNIT_IN_CONTRACT,
                 T.PRESC_TYPE,T.PRESC_ATTR,T.PRESC_SOURCE, 
                T.DECOCTION,T.REPETITION,T.COUNT_PER_REPETITION,T.ORDERED_BY,T.DOCTOR_USER,
                T.PRESCRIBED_USERCODE,T.PRESCRIBED_BY,T.ENTERED_USERCODE,T.ENTERED_BY,
                T.ENTERED_DATETIME,T.VERIFY_USERCODE,T.VERIFY_BY,T.VERIFIED_DATETIME,
                T.COSTS,T.PAYMENTS,T.ROUND_AMT, T.DISPENSING_USERCODE,
                T.DISPENSING_PROVIDER,T.DISPENSING_DATETIME,T.STATUS, 
                '                    ',0 FLAG,'                    ',D.DEPT_NAME 
                FROM DOCT_DRUG_PRESC_MASTER T  LEFT JOIN DEPT_DICT D ON T.ORDERED_BY=D.DEPT_CODE 
                WHERE (to_char(T.PRESC_DATE,'yyyy-mm-dd hh24:mi:ss') = '{prescdate.ToString("yyyy-MM-dd HH:mm:ss")}') AND (T.PRESC_NO = '{prescno}') and T.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";

            return new ServerPublicClient().GetDataBySql(sql).Tables[0];

        }
        /// <summary>
        /// 获取全部科室
        /// </summary>
        /// <returns></returns>
        private DataTable GetAllDept()
        {
            //string sql = "SELECT DEPT_CODE,DEPT_NAME  FROM DEPT_DICT WHERE (DEPT_NAME LIKE '%麻醉%' OR DEPT_NAME LIKE '%手术%') and end_class='1' and DEPT_NAME not LIKE '%门诊%' ";
            string sql = "SELECT DEPT_CODE,DEPT_NAME,input_code  FROM DEPT_DICT WHERE   end_class='1' and DEPT_NAME not LIKE '%门诊%' and his_unit_code='" + SystemParm.HisUnitCode + @"'";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            return dt;
        }
        /// <summary>
        /// 获取处方病人主信息
        /// </summary>
        /// <param name="begintime"></param>
        /// <param name="endtime"></param>
        /// <param name="storagecode"></param>
        /// <returns></returns>
        private DataTable GetDoctDrugPrescMasterByTimeAndDept(DateTime begintime, DateTime endtime, string storagecode, string orderedby)
        {
            string sql = "SELECT T.PRESC_NO,T.NAME,T.NAME_PHONETIC,T.PRESC_DATE,T.PAYMENTS," +
                "T.PATIENT_ID,T.ORDERED_BY,'1' as AS_COMFIRM,D.DEPT_NAME " +
                "FROM DOCT_DRUG_PRESC_MASTER  T  LEFT JOIN DEPT_DICT D ON T.ORDERED_BY=D.DEPT_CODE " +
                "WHERE (T.PRESC_DATE >= :day0) and (T.PRESC_DATE <= :day1) " +
                "AND (T.DISPENSARY = :disp) AND T.presc_source = 2 AND T.PRESC_STATUS = 0  and T.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'" +
                "and T.ORDERED_BY = :as_ordered_by";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("day0");
            para.Add("day1");
            para.Add("disp");
            para.Add("as_ordered_by");
            para_val.Add(begintime);
            para_val.Add(endtime);
            para_val.Add(storagecode);
            para_val.Add(orderedby);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        #endregion
        private void Getdt_printClone()
        {
            dt_print.Clear();
            dt_print.Columns.Add("PRESC_DATE");
            dt_print.Columns.Add("PRESC_NO");
            dt_print.Columns.Add("AGE");
            dt_print.Columns.Add("SEX");
            dt_print.Columns.Add("DISPENSARY");
            dt_print.Columns.Add("DISPENSARY_NAME");
            dt_print.Columns.Add("PATIENT_ID");
            dt_print.Columns.Add("NAME");
            dt_print.Columns.Add("BED_LABEL");
            dt_print.Columns.Add("IDENTITY");
            dt_print.Columns.Add("CHARGE_TYPE");
            dt_print.Columns.Add("UNIT_IN_CONTRACT");
            dt_print.Columns.Add("PRESC_SOURCE");
            dt_print.Columns.Add("REPETITION");
            dt_print.Columns.Add("ORDERED_BY");
            dt_print.Columns.Add("PRESCRIBED_BY");
            //dt_print.Columns.Add("VISIT_ID");
            dt_print.Columns.Add("ITEM_NO");
            dt_print.Columns.Add("DRUG_NAME");
            dt_print.Columns.Add("FIRM_ID");
            dt_print.Columns.Add("PACKAGE_SPEC");
            dt_print.Columns.Add("PACKAGE_UNITS");
            dt_print.Columns.Add("QUANTITY");
            dt_print.Columns.Add("NAME_PHONETIC");
            dt_print.Columns.Add("PRESC_TYPE");
            dt_print.Columns.Add("COSTS");
            dt_print.Columns.Add("PAYMENTS");
            dt_print.Columns.Add("ENTERED_BY");
            dt_print.Columns.Add("COUNT_PER_REPETITION");
            dt_print.Columns.Add("DCOSTS");
            dt_print.Columns.Add("FREQUENCY");
            dt_print.Columns.Add("DOSAGE_EACH");
            dt_print.Columns.Add("DOSAGE");
            dt_print.Columns.Add("DOSAGE_UNITS");
            dt_print.Columns.Add("ADMINISTRATION");
            dt_print.Columns.Add("USAGE");
            dt_print.Columns.Add("DISCHARGE_TAKING_INDICATOR");
            dt_print.Columns.Add("DEPT_NAME");
            dt_print.Columns.Add("DACOSTS");

        }




        /// <summary>
        /// 插入病人费用明细
        /// </summary>
        private int InsertInpBill(DataTable detadr1, ref Dictionary<string, string> allsave, int index)
        {
            if (masterdr == null) return -1;
            //非住院处方,直接返回
            if (masterdr["PRESC_SOURCE"] == DBNull.Value || masterdr["PRESC_SOURCE"].ToString() != "2") return -1;
            object repetition = masterdr["REPETITION"] != DBNull.Value ? masterdr["REPETITION"] : 1;
            DataTable doctordt = new DataTable();
            string doctorname = "";
            string doctorgroup = "";
            string orderdept = "";//科室
            int maxitemno = 0;
            doctordt = GetDoctorGroupByDoctorUser(masterdr["DOCTOR_USER"].ToString());
            if (doctordt.Rows.Count == 0)
            {
                doctordt = GetOrderGroupRecByPatientID(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
                if (doctordt.Rows.Count == 0)
                {
                    doctorname = "*";
                    doctorgroup = "*";
                }
                else
                {
                    doctorname = doctordt.Rows[0]["order_doctor"].ToString();
                    doctorgroup = doctordt.Rows[0]["order_group"].ToString();
                }
            }
            else
            {
                doctorname = doctordt.Rows[0]["doctor"].ToString();
                doctorgroup = doctordt.Rows[0]["order_group"].ToString();
            }
            //取开单科室 先取doct表里开单，如果没有，取患者所在科室
            orderdept = masterdr["ORDERED_BY"].ToString();
            if (string.IsNullOrEmpty(orderdept))
            {
                DataTable deptdt = new DataTable();
                deptdt = GetPatInHospDeptCode(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
                if (deptdt.Rows.Count == 0)
                {
                    deptdt = GetPatVisitDeptCode(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
                    if (deptdt.Rows.Count > 0)
                    {
                        orderdept = deptdt.Rows[0]["dept_discharge_from"].ToString();
                    }
                    else
                    {
                        XtraMessageBox.Show("取该病人开单科室出错！", "提示");
                        return -1;
                    }
                }
                else
                {
                    if (deptdt.Rows[0]["dept_code"] != DBNull.Value)
                    {
                        orderdept = deptdt.Rows[0]["dept_code"].ToString();
                    }
                    else
                    {
                        orderdept = PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE;//找不到开单科室，取医生所属
                    }
                }
            }

            string orderedby = PlatCommon.SysBase.SystemParm.GetParameterValue("ORDERED_BY", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);


            string prescdate = masterdr["PRESC_DATE"].ToString();
            string prescno = masterdr["PRESC_NO"].ToString();
            int takingind = 0; //masterdr["DISCHARGE_TAKING_INDICATOR"] != DBNull.Value ? Convert.ToInt16(masterdr["DISCHARGE_TAKING_INDICATOR"]) : -1;//带药标志
            string itemclass = masterdr["PRESC_TYPE"].ToString() == "0" ? "A" : "B";
            string verifieddatetime = "";
            string verifyby = "";

            DataTable verifydt = GetPrescMasterByNoAndDate(prescno, prescdate);
            if (verifydt.Rows.Count > 0)
            {
                verifieddatetime = verifydt.Rows[0]["verified_datetime"] != DBNull.Value ? verifydt.Rows[0]["verified_datetime"].ToString() : "";
                verifyby = verifydt.Rows[0]["verify_by"] != DBNull.Value ? verifydt.Rows[0]["verify_by"].ToString() : "";
            }
            DataTable maxitemnodt = GetMaxItemNoByPatientIDAndVisitID(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
            if (maxitemnodt.Rows.Count > 0)
            {
                if (string.IsNullOrEmpty(maxitemnodt.Rows[0][0].ToString()))
                {
                    maxitemno = 1;
                }
                else
                {
                    maxitemno = int.Parse(maxitemnodt.Rows[0][0].ToString()) + index + 1;
                }
            }
            foreach (DataRow detadr in detadr1.Rows)
            {

                //记录item_no防止主键冲突item_no
                if (list_item.Count > 0)
                {
                    if (list_item.ContainsKey(masterdr["PATIENT_ID"].ToString()))
                    {

                        string ls_item_no = list_item[masterdr["PATIENT_ID"].ToString()];
                        ls_item_no = (int.Parse(ls_item_no) + 1).ToString();
                        list_item[masterdr["PATIENT_ID"].ToString()] = ls_item_no;
                        maxitemno = int.Parse(ls_item_no);
                    }
                    else
                    {
                        list_item.Add(masterdr["PATIENT_ID"].ToString(), maxitemno.ToString());
                    }
                }
                else
                {
                    list_item.Add(masterdr["PATIENT_ID"].ToString(), maxitemno.ToString());
                }
                //记录item_no

                string order_no = detadr["ORDER_NO"].ToString();
                decimal ldec_costs = Convert.ToDecimal(detadr["costs"]);
                decimal ldec_payments = Convert.ToDecimal(detadr["payments"]);
                decimal factor = 1;
                if (ldec_costs == 0)
                {
                    factor = 0;
                }
                else
                {
                    factor = Math.Round(ldec_payments / ldec_costs, 4);
                }
                string dispensingtime = masterdr["DISPENSING_DATETIME"] == DBNull.Value ? GetSystemTime().ToString("yyyy-MM-dd HH:mm:ss") : masterdr["DISPENSING_DATETIME"].ToString();
                detadr["firm_id"] = detadr["firm_id"] == DBNull.Value ? "" : detadr["firm_id"].ToString();
                DataTable priceclassdt = GetPriceListClassByItem(itemclass, detadr["DRUG_CODE"].ToString(), detadr["package_spec"].ToString() + detadr["firm_id"].ToString());
                if (priceclassdt.Rows.Count < 1)
                {
                    XtraMessageBox.Show("取不到PRICE_LIST表中字段信息!", "提示");
                    return -1;
                }
                string classonmr = priceclassdt.Rows[0]["class_on_mr"] == DBNull.Value ? "" : priceclassdt.Rows[0]["class_on_mr"].ToString();
                DataTable doctgroupdt = GetDoctorGroupByName(doctorname);
                string personggourp = "*";
                if (doctgroupdt.Rows.Count > 0)
                {
                    personggourp = doctgroupdt.Rows[0]["order_group"] != DBNull.Value ? doctgroupdt.Rows[0]["order_group"].ToString() : "*";
                }
                string insertsql = InsertInpBillDetail(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString(), maxitemno.ToString(),
                    itemclass, priceclassdt.Rows[0]["item_name"].ToString(), detadr["DRUG_CODE"].ToString(), detadr["package_spec"].ToString() + detadr["firm_id"].ToString(),
                    detadr["quantity"].ToString(), detadr["package_units"].ToString(), orderdept, this.DeptCode,
                    Math.Round(Convert.ToDecimal(detadr["costs"]), 2).ToString(), Math.Round(Convert.ToDecimal(detadr["payments"]), 2).ToString(),
                    dispensingtime, PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO, priceclassdt.Rows[0]["class_on_reckoning"].ToString(), doctorgroup, doctorname,
                    personggourp, doctorname, masterdr["doctor_user"].ToString(), detadr["retail_price"].ToString(), factor.ToString(),
                    takingind.ToString(), priceclassdt.Rows[0]["class_on_inp_rcpt"].ToString(), priceclassdt.Rows[0]["subj_code"].ToString(),
                    classonmr, "", "A", order_no + "", "", detadr["batch_no"].ToString(), detadr["TRADE_PRICE"] != DBNull.Value ? detadr["TRADE_PRICE"].ToString() : "0", "0",
                    detadr["batch_code"].ToString(), detadr["guid"].ToString());
                if (!allsave.ContainsKey(insertsql))
                {
                    allsave.Add(insertsql, "添加手术患者费用明细记录失败！");
                }
            }

            return 1;
        }

        /// <summary>
        /// 住院患者费用明细
        /// </summary>
        /// <returns></returns>
        private string InsertInpBillDetail(string as_patient_id, string al_visit_id, string al_item_no,
            string as_item_class, string ls_item_name, string as_item_code, string as_item_spec, string adec_amount,
            string as_item_units, string as_order_by, string as_performed_by, string ldec_cur_costs, string ldec_cur_charges,
            string ldt_date, string as_operator_code, string ls_class_on_reckoning, string as_order_group,
            string as_order_name, string ls_perform_group, string as_perform_name, string as_order_doctor_code,
            string adec_price, string ldec_factor, string al_discharge_taking, string ls_class_on_inp_rcpt,
            string ls_subj_code, string ls_class_on_mr, string as_rcpt_no, string as_oper_type, string as_oper_code, string as_memo,
            string as_batch_no, string tradeprice, string sourceflag,
            string _batch_code, string _guid)
        {
            string sql = "insert into inp_bill_detail (" +
                "patient_id,visit_id,item_no,item_class,item_name," +
                "item_code,item_spec,amount,units,ordered_by," +
                "performed_by,costs,charges,billing_date_time,operator_no," +
                "class_on_reckoning,order_group,order_doctor,perform_group,perform_doctor," +
                "doctor_user,item_price,price_quotiety,discharge_taking_indicator,class_on_inp_rcpt," +
                "subj_code,class_on_mr,rcpt_no,oper_type,oper_code," +
                "memo,drug_batch_no,trade_price,source_flag ," +
                " drug_batch_code , guid,HIS_UNIT_CODE" +
                ") values('"
                + as_patient_id + "','" + al_visit_id + "','" + al_item_no + "','" + as_item_class + "','" + ls_item_name + "','"
                + as_item_code + "','" + as_item_spec + "','" + adec_amount + "','" + as_item_units + "','" + as_order_by + "','"
                + as_performed_by + "','" + ldec_cur_costs + "','" + ldec_cur_charges + "',to_date('" + ldt_date + "', 'yyyy-MM-dd HH24:mi:ss'),'" + as_operator_code + "','"
                + ls_class_on_reckoning + "','" + as_order_group + "','" + as_order_name + "','" + ls_perform_group + "','" + as_perform_name + "','"
                + as_order_doctor_code + "','" + adec_price + "','" + ldec_factor + "','" + al_discharge_taking + "','" + ls_class_on_inp_rcpt + "','"
                + ls_subj_code + "','" + ls_class_on_mr + "','" + as_rcpt_no + "','" + as_oper_type + "','" + as_oper_code + "','"
                + as_memo + "','" + as_batch_no + "','" + tradeprice + "','" + sourceflag + "','"
                 + _batch_code + "','" + _guid + "','" + SystemParm.HisUnitCode + "')";
            return sql;
        }

        private void BarLargeButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

        }


    }
}
