﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using PlatCommon.Comm;
using PlatCommon.SysBase;
using Tjhis.Oboutp.Station.Comm;
using Tjhis.Oboutp.Station.Comm.CustomControl;
using Tjhis.Oboutp.Station.Comm.DevHelper;
using Tjhis.Oboutp.Station.Comm.Globals;
using Tjhis.Oboutp.Station.Comm.Globals.CachePool;
using Tjhis.Oboutp.Station.Comm.Inputs;
using Tjhis.Oboutp.Station.Cost;
using Tjhis.Oboutp.Station.CostSvr;
using Tjhis.Oboutp.Station.PrescSvr;
using Tjhis.Oboutp.Station.Presc;

namespace Tjhis.Oboutp.Station.Presc
{
    /// <summary>
    /// 处方主界面
    /// </summary>
    public partial class FrmPrescDoctInput : PlatCommon.SysBase.ParentForm
    {
        #region 变量
        private PatientInfo _patientInfo;//字段

        private DataTable dtAllDetail;
        private DataTable dtSaveDetail;
        private DataTable dtList;
        private DataTable dtDrugStore;
        private DataTable dtOutMedHis;             //艾隆接口

        private List<AgreementPresItem> _apiList; //用于接收协定处方的数据

        private CalcChargePrice calcPrice = new CalcChargePrice();

        private string strPrescToxiProtitiesJingshen; //药品精神类属性
        private string strPrescToxiProtitiesDuma;   //药品毒麻类属性
        private decimal decDefaultFator; //收费系统
        private int miSelectedListRowHandle = -1;//记录gvList选中行的handle
        private int msSelectedPrescNo = -1;//==20200624选中的处方号
        private int miPrescMaxQuantity = 6;//处方最大数量
        public bool boolChange = false;
        private string visitDate;
        private string visitNo;
        private string postUrl = "http://*************:1500/Report.TJ/";
        Hashtable imageList = new Hashtable();

        // 输入法控件 药品
        private InputControlHelper inputPrescText;

        public new PatientInfo PatientInfo//属性
        {
            set { _patientInfo = value; }
            get { return _patientInfo; }
        }

        public List<AgreementPresItem> ApiList { get => _apiList; set => _apiList = value; }

        private bool mbControlReadOnly;
        #endregion 变量

        public FrmPrescDoctInput(PatientInfo patientInfo, bool pbControlReadOnly = false)
        {
            InitializeComponent();
            this.PatientInfo = patientInfo;

            mbControlReadOnly = pbControlReadOnly;
        }

        /// <summary>
        ///  窗口Load事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void FrmPrescDoctInput_Load(object sender, EventArgs e)
        {
            try
            {
                //F9字典
                await InitInputControl(this.PatientInfo);
                //初始化数据
                await this.InitData();

                DataTable table = FrmPrescDoctInputSrv.GetObPatientInfo(this.PatientInfo);
                if (table.Rows.Count.Equals(0))
                {
                    UIMessageBox.Err("提取用户信息出错！");
                    this.Close();
                    return;
                }

                //赋值全局变量
                this.visitDate = table.Rows[0][0].ToString("");
                this.visitNo = table.Rows[0][1].ToString("");

                //this.gvDetail.Columns["NWARN"].ColumnEdit = this.imageComboBoxList;
                //美康合理用药相关
                MedicomItem item = new MedicomItem();
                item.Status = 2;
                item.Lactation = -1;
                item.Pregnancy = -1;
                item.HepdamageDegree = -1;
                item.RendamageDegree = -1;
                Global.SetMedicomInitPatient(this.PatientInfo, item);

                //如果无处方信息 默认新建一个
                if (this.dtList.Rows.Count.Equals(0))
                {
                    this.btNewPresc_Click(this.btNewPresc, null);
                }

                //离观患者不可操作 只能READ
                ControlReadOnly();

                //ParameterItem item2 = new ParameterItem();
                //item2.APP_NAME = "OBDOCTWS";
                //item2.DEPT_CODE = "*";
                //item2.EMP_NO = "*";
                //item2.PARAMETER_NAME = "maxpresc";
                //item2.DEFAULT_VALUE = "6";

                miPrescMaxQuantity = int.Parse(Parameter.MAXPRESC);
            }
            catch (Exception ex)
            {
                UIMessageBox.Warn(ex.ToString());
            }
        }

        #region 方法

        /// <summary>
        /// 初始化界面信息  药房 用药途径 频次等  包括数据绑定
        /// </summary>
        private async Task InitData()
        {
            try
            {
                decDefaultFator = 1;// await Global.GetDefaultFatorValueAsync(this.PatientInfo.ChargeType);

                txtDispensary.Properties.DataSource = dtDrugStore;
                if (dtDrugStore.Rows.Count > 0)
                {
                    DataRow drDefaultDispensary = dtDrugStore.AsEnumerable().FirstOrDefault(r => r["STORAGE_NAME"].ToString().Contains("急诊"));
                    if (drDefaultDispensary != null)
                        txtDispensary.EditValue = drDefaultDispensary["STORAGE_CODE"].ToString();
                    else
                        txtDispensary.EditValue = dtDrugStore.Rows[0][0].ToString();
                }

                //ParameterItem item = new ParameterItem();
                //item.APP_NAME = "*";
                //item.DEPT_CODE = "*";
                //item.EMP_NO = "*";
                //item.PARAMETER_NAME = "PRESC_TOXI_PROTITIES_JINGSHEN";
                //item.DEFAULT_VALUE = "";
                //strPrescToxiProtitiesJingshen = ";" + await Global.GetParameterValueAsync(item) + ";";
                strPrescToxiProtitiesJingshen = ";" + Parameter.PRESC_TOXI_PROTITIES_JINGSHEN + ";";

                //item.PARAMETER_NAME = "PRESC_TOXI_PROTITIES_DUMA";
                //strPrescToxiProtitiesDuma = ";" + await Global.GetParameterValueAsync(item) + ";";
                strPrescToxiProtitiesDuma = ";" + Parameter.PRESC_TOXI_PROTITIES_DUMA + ";";

                //item.PARAMETER_NAME = "MR_EMR_UPLOAD_URL";
                //postUrl = await Global.GetParameterValueAsync(item);
                postUrl = Parameter.MR_EMR_UPLOAD_URL;

                //处方和药品Grid数据绑定
                await this.BindingDataSourceAsync();
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方InitData方法出错!详情" + ex.ToString());
            }
        }

        /// <summary>
        /// Grid绑定数据源 处方和药品
        /// </summary>
        /// 
        private async Task BindingDataSourceAsync()
        {
            try
            {
                //gcDetail
                string strDetailWhere = "OB_DRUG_PRESC_MASTER.PATIENT_ID='" + PatientInfo.PatientId + "'";
                dtAllDetail = await FrmPrescDoctInputSrv.GetDetailAsync(strDetailWhere);
                //mdtSaveDetail = mdtAllDetail;//引用类型赋值在内存中共用同一地址
                //DataRow[] drsDtAllDetail = mdtAllDetail.Select("");
                //if (drsDtAllDetail.Count() > 0)
                //{
                //    mdtSaveDetail = drsDtAllDetail.CopyToDataTable();
                //}
                //else
                //{
                //mdtSaveDetail = mdtAllDetail.Clone();
                //}
                dtSaveDetail = dtAllDetail.Clone();

                gcDetail.DataSource = dtSaveDetail;
                setSubOrder(dtAllDetail);
                //gcList
                string strListWhere = "( OB_DRUG_PRESC_MASTER.PATIENT_ID = '" + PatientInfo.PatientId + "' )";
                dtList = await FrmPrescDoctInputSrv.GetListAsync(strListWhere);
                gcList.DataSource = dtList;

                //选中最后一行
                if (gvList.RowCount > 0)
                {
                    //gvList.UnselectRow(0);
                    //gvList.Focus();
                    gvList.SelectRow(gvList.RowCount - 1);
                    //gvList.FocusedRowHandle = gvList.RowCount - 1;
                    //liujun add 2021-11-16
                    this.gvList_FocusedRowChanged(this.gvList, new FocusedRowChangedEventArgs(GridControl.InvalidRowHandle, this.gvList.RowCount - 1));
                }

                this.imageList.Clear();
                this.imageList.Add("0", System.Drawing.Image.FromFile(Global.GetImageFileNameFullAndPath("pic", "0.bmp")));
                this.imageList.Add("1", System.Drawing.Image.FromFile(Global.GetImageFileNameFullAndPath("pic", "1.bmp")));
                this.imageList.Add("2", System.Drawing.Image.FromFile(Global.GetImageFileNameFullAndPath("pic", "2.bmp")));
                this.imageList.Add("3", System.Drawing.Image.FromFile(Global.GetImageFileNameFullAndPath("pic", "3.bmp")));
                this.imageList.Add("4", System.Drawing.Image.FromFile(Global.GetImageFileNameFullAndPath("pic", "4.bmp")));
                this.imageList.Add("5", System.Drawing.Image.FromFile(Global.GetImageFileNameFullAndPath("pic", "5.bmp")));
                this.imageList.Add("6", System.Drawing.Image.FromFile(Global.GetImageFileNameFullAndPath("pic", "6.bmp")));
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方界面绑定数据出错!详情" + ex.ToString());
            }
        }

        /// <summary>
        /// 选择上面的处方显示相应的诊疗项目明细(药品，非计价项目)
        /// </summary>
        /// <param name="pListFocusedRowHandle"></param>
        //private void SelectedListRefreshMasterDetail(int pListFocusedRowHandle)
        private void SelectedListRefreshMasterDetail(PrescMasterItem item)
        {
            try
            {
                //选择上面的处方显示相应的明细
                //DataRow dr = gvList.GetDataRow(pListFocusedRowHandle);
                //DateTime dtPrescDate = (DateTime)dr["PRESC_DATE"];

                //int iPrescNo = int.Parse(dr["PRESC_NO"].ToString());
                string selectSql = "(PRESC_DATE = '" + item.PRESC_DATE.ToString() + "' and PRESC_NO= " + item.PRESC_NO + ")";
                //StringBuilder strbFilter = new StringBuilder();
                //strbFilter.Append("( ( PRESC_DATE = ");
                ////TO_DATE('','')
                //strbFilter.Append("'" + dtPrescDate.ToString("yyyy/MM/dd HH:mm:ss") + "'");
                //strbFilter.Append(")");
                //strbFilter.Append(" and ");
                //strbFilter.Append("(PRESC_NO =");
                //strbFilter.Append(iPrescNo.ToString());
                //strbFilter.Append(") ) ");

                //显示相应的Master数据
                //DataRow drList = gvList.GetDataRow(pListFocusedRowHandle);
                //txtPatientId.EditValue = drList["PATIENT_ID"];
                //txtBedLabel.EditValue = drList["BED_LABEL"];
                //txtName.EditValue = drList["NAME"];
                //txtIdentity.EditValue = drList["IDENTITY"];
                //txtChargeType.EditValue = drList["CHARGE_TYPE"];
                //txtUnitInContract.EditValue = drList["UNIT_IN_CONTRACT"];
                //txtDiagnosisName.EditValue = drList["DIAGNOSIS_NAME"];
                //txtPrescNo.EditValue = drList["PRESC_NO"];
                //txtDispensary.EditValue = drList["DISPENSARY"] == DBNull.Value ? "" : drList["DISPENSARY"];
                //txtPrepayments.EditValue = drList["PREPAYMENTS"];
                //txtCosts.EditValue = drList["COSTS"];

                txtPatientId.EditValue = item.PATIENT_ID;
                txtBedLabel.EditValue = item.BED_LABEL;
                txtName.EditValue = item.NAME;
                txtIdentity.EditValue = item.IDENTITY;
                txtChargeType.EditValue = item.CHARGE_TYPE;
                txtUnitInContract.EditValue = item.UNIT_IN_CONTRACT;
                txtDiagnosisName.EditValue = item.DIAGNOSIS_NAME;
                txtPrescNo.EditValue = item.PRESC_NO;
                if (!(item.DISPENSARY == null || item.DISPENSARY.Length.Equals(0)))
                    txtDispensary.EditValue = item.DISPENSARY;
                txtPayments.EditValue = item.PREPAYMENTS;
                txtCosts.EditValue = item.COSTS;

                //liujun add 2019-12-5 处理预交金余额问题
                decimal decPrepayment;
                decimal decCharges;

                decPrepayment = Global.GetOutpTotalBalance(this.PatientInfo.PatientId);
                decCharges = Global.GetObOrdersTotalCost(this.PatientInfo.PatientId, this.PatientInfo.ObVisitNo);
                txtPrepayments.EditValue = decPrepayment.ToString();

                //if(decPrepayment - decCharges < 0)
                //{
                //    if(Parameter.ENABLE_PREPAYMENT.Equals("1") && Parameter.NO_PREPAYMENT_ALLOW_OPERATE.Equals("0"))
                //    {
                //        btAdd.Enabled = false;
                //        UIMessageBox.Warn("此病人预交金已经用完,请先交预交金!");
                //        return;
                //    }
                //}
                //liujun add 2019-12-5 处理预交金余额问题

                //txtPayments.EditValue = drList["PAYMENTS"];

                //显示相应的gvDetail数据
                //DataRow[] drs = mdtAllDetail.Select(strbFilter.ToString());
                DataRow[] drs = dtAllDetail.Select(selectSql.ToString());
                if (drs.Count() > 0)
                {
                    dtSaveDetail = drs.CopyToDataTable();
                }
                else
                {
                    dtSaveDetail = dtAllDetail.Clone();
                }

                dtSaveDetail.TableName = "OB_DRUG_PRESC_DETAIL";

                gcDetail.DataSource = dtSaveDetail;

                if (this.gvDetail.RowCount > 0)
                {
                    //显示药理 YYS 2020/8/16
                    string strToxiProperty = "普通";
                    strToxiProperty = GetToxiProperty(0);//毒理属性
                    this.gvDetail.FocusedRowHandle = this.gvDetail.RowCount - 1;
                    this.gvDetail.UnselectRow(0);
                    this.gvDetail.SelectRow(this.gvDetail.FocusedRowHandle);

                    //显示每个药方的药理 YYS 2020/8/16
                    for (int i = 0; i < gvDetail.RowCount; i++)
                    {
                        gvDetail.SetRowCellValue(i, "TOXI_PROPERTY", strToxiProperty);
                    }
                }
                //修改状态 不然显示后然后关闭提示保存  YYS 2020/8/16 
                dtSaveDetail.AcceptChanges();
                //GridViewHelper.ColumnWidthAutoContent(gvDetail);
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("选择处方显示下面的药品出错!详情" + ex.ToString());
            }
        }

        /// <summary>
        /// 已收费等则不允许医生再修改该处方
        /// </summary>
        private int NotAllowModifyPresc(int pListFocusedRowHandle)
        {
            //已收费、已发药则不允许医生再修改该处方.判断了已收费就不需要再判断已发药了，因为只有收费后才能发药
            DataRow dr = gvList.GetDataRow(pListFocusedRowHandle);
            //处方收费标志,收费后收费会更改OB_DRUG_PRESC_MASTER.CHARGE_INDICATOR收费标志。默认值为null
            int iChargeIndicator = dr["CHARGE_INDICATOR"].ToInt(0);

            //已收费不允许修改该处方
            if (iChargeIndicator == 1)
            {
                gvDetail.OptionsBehavior.Editable = false;
                gvDetail.OptionsBehavior.ReadOnly = true;

                btAdd.Enabled = false;//加药
                btDelete.Enabled = false;//减药
                btChildPresc.Enabled = false;//子处方
                btNewPresc.Enabled = true;//新方
                btBindingPresc.Enabled = false;//协定处方
                btCopyPresc.Enabled = false;//复制处方
                btDelPresc.Enabled = false;//毁方
                btSave.Enabled = false;//保存

                return -1;
            }
            else
            {
                gvDetail.OptionsBehavior.Editable = true;
                gvDetail.OptionsBehavior.ReadOnly = false;

                btAdd.Enabled = true;//加药
                btDelete.Enabled = true;//减药
                btChildPresc.Enabled = true;//子处方
                btNewPresc.Enabled = true;//新方
                btBindingPresc.Enabled = true;//协定处方
                btCopyPresc.Enabled = true;//复制处方
                btDelPresc.Enabled = true;//毁方
                btSave.Enabled = true;//保存
            }
            return 1;
        }

        /// <summary>
        /// 初始化处方F9字典
        /// </summary>
        private async Task InitInputControl(PatientInfo patientInfo)
        {
            //20211105 BTE 原来是构造里的内容开始
            Tjhis.Oboutp.Station.Comm.DevHelper.XtraGridViewHelper.Initialize(gvList, gvDetail, gvDetail);
            //liujun add 2019-12-05 
            this.gvList.Click += (x, y) => { this.gvList_FocusedRowChanged(x, null); };
            //liujun add 2019-12-05


            dtDrugStore = await FrmPrescDoctInputSrv.GetPrescDrugStoreAsync();

            this.gvList.OptionsView.ColumnAutoWidth = false;
            this.gvList.Columns["CHARGE_INDICATOR"].ColumnEdit = CustomItemLookUpEdit.Instance(await FrmPrescDoctInputSrv.GetChargeStatuDictAsync(), "VALUE", "NAME");
            this.gvList.Columns["PRESC_TYPE"].ColumnEdit = CustomItemLookUpEdit.Instance(await FrmPrescDoctInputSrv.GetPrescTypeAsync(), "ITEM_CODE", "ITEM_NAME");


            this.gvDetail.OptionsView.ColumnAutoWidth = false;
            this.gvDetail.Columns["SKIN_TEST"].ColumnEdit = CustomCheckEdit.Instance();
            this.gvDetail.Columns["DISPENSARY"].ColumnEdit = new CustomItemSearchLookUpEdit(this.dtDrugStore, "STORAGE_CODE", "STORAGE_NAME");
            this.gvDetail.Columns["ADMINISTRATION"].ColumnEdit = CustomItemLookUpEdit.Instance(await FrmPrescDoctInputSrv.GetAdministrationDictDTAsync(), "途径名称", "途径名称");
            this.gvDetail.Columns["FREQUENCY"].ColumnEdit = CustomItemLookUpEdit.Instance(await FrmPrescDoctInputSrv.GetPerformFreqDictDTSelectAsync(), "频次", "频次");
            this.gvDetail.Columns["DOSAGE_EACH"].ColumnEdit = CustomTextEditMark.Instance(EnumClassType.Decimial);
            this.gvDetail.Columns["QUANTITY"].ColumnEdit = CustomTextEditMark.Instance(EnumClassType.Integer);
            this.gvDetail.Columns["ABIDANCE"].ColumnEdit = CustomTextEditMark.Instance(EnumClassType.Integer);
            //20211105 BTE 原来是构造里的内容结束

            string inputCondition = string.Empty;
            foreach (DataRow dr in dtDrugStore.Rows)
            {
                if (!string.IsNullOrEmpty(inputCondition))
                    inputCondition += ",";
                inputCondition += "'" + dr["STORAGE_CODE"].ToString("") + "'";
            }
            if (!string.IsNullOrEmpty(inputCondition))
                inputCondition = " STORAGE in (" + inputCondition + ")";
            else
                inputCondition = " STORAGE is null";
            if (!string.IsNullOrEmpty(inputCondition))
            {
                inputCondition += " and (his_unit_code = '" + SystemParm.HisUnitCode + "') ";
            }
            else
            {
                inputCondition += " (his_unit_code = '" + SystemParm.HisUnitCode + "') ";
            }
            inputPrescText = await Inputs.GetInputControlHelperInstanceAsync(/*"诊疗药品_留观"*/InputCatalog.留观诊疗药品, InputControl1_deInputResult, inputCondition, gvDetail, "DRUG_NAME");
            inputPrescText.HideDialog();
            this.Controls.Add(inputPrescText);
        }

        // 获取行编辑状态：0-可编辑；1--只读；2--不可操作
        private int GetRowModifyStatus(int rowHandle, GridView sender = null)
        {
            // 默认取GridView1的状态
            if (sender == null) sender = gvList;

            // 非有效行时，默认为可编辑
            if ((rowHandle < 0) || (rowHandle >= sender.RowCount)) return 0;

            // 若项目已收费不可操作
            DataRow dr = sender.GetDataRow(rowHandle);
            if (dr["CHARGE_INDICATOR"].ToInt(0) == 1) return 1;
            //// 项目保存后不可编辑
            //string itemCode = dr["ITEM_CODE"].ToString("");
            //string serialNo = dr["SERIAL_NO"].ToString("");
            //if (dr.RowState != DataRowState.Added) return 1;

            return 0;
        }

        /// <summary>
        /// 为处方添加新药
        /// </summary>
        /// <returns></returns>
        private int AddNewDrug()
        {
            this.boolChange = true;
            gvDetail.AddNewRow();
            DataRow drDetailNew = gvDetail.GetFocusedDataRow();//.GetDataRow(iDetailFocusedRowHandle);
            gvDetail.FocusedColumn = gridColumn12;//药品名称 设置gridColumn12列获得焦点
            //使新增的行自动开启编辑模式，既所在焦点单元格显示闪动的光标
            gvDetail.ShowEditorByMouse();

            int iListFocusedRowHandle = gvList.FocusedRowHandle;
            DataRow drList = gvList.GetDataRow(iListFocusedRowHandle);

            //liujun add 2021-11-16 处理提取交费序号出错
            string clinicSerialNo = Global.GetClinicSerialNo();
            for (int i = 0; i < 10; i++)
            {
                if (!CommonMethod.StringIsNull(clinicSerialNo) && clinicSerialNo != "-1")
                {
                    break;
                }
                clinicSerialNo = Global.GetClinicSerialNo();
            }

            if (CommonMethod.StringIsNull(clinicSerialNo) || clinicSerialNo == "-1")
            {
                UIMessageBox.Err("提取交易序号出错，请重新加药！");
                drDetailNew.Delete();
                return -1;
            }
            //liujun add 2021-11-16 处理提取交费序号出错

            //drDetailNew = gvDetail.GetFocusedDataRow();//.GetDataRow(iDetailFocusedRowHandle);
            drDetailNew["PRESC_DATE"] = drList["PRESC_DATE"];
            drDetailNew["PRESC_NO"] = drList["PRESC_NO"];
            drDetailNew["PRESC_STATUS"] = 0;
            drDetailNew["PRESC_TYPE"] = 0; //处方类别 西药 草药
            drDetailNew["ORDER_SUB_NO"] = 1;
            drDetailNew["CLINIC_SERIAL_NO"] = clinicSerialNo;
            drDetailNew["NEW_ROW_FLAG"] = 1;//新增行标志，分方用
            drDetailNew["HIS_UNIT_CODE"] = SystemParm.HisUnitCode;
            //drDetailNew["NWARN"] = 1;//新增行标志，分方用
            //保存值到数据源中
            this.gvDetail.UpdateCurrentRow();
            this.gvDetail.CloseEditor();
            return this.gvDetail.FocusedRowHandle;//iDetailFocusedRowHandle;
        }

        /// <summary>
        /// 保存时判断数据有效性（药品库存是否足够）
        /// </summary>
        /// <returns></returns>
        private int SaveJudgmentValidity()
        {
            int iFocusedRowHandle = gvList.FocusedRowHandle;
            if (iFocusedRowHandle < 0)
            {
                UIMessageBox.Warn("没有要保存的处方！");
                return -1;
            }

            try
            {
                // 处方药品明细整理
                for (int i = 0; i < gvDetail.RowCount; i++)
                {
                    DataRow drDetail = gvDetail.GetDataRow(i);
                    string strDrugCode = drDetail["DRUG_CODE"].ToString();
                    if (string.IsNullOrEmpty(strDrugCode))
                    {
                        drDetail.Delete();
                        continue;
                    }

                    string strDrugName = drDetail["DRUG_NAME"].ToString();
                    string strAdministration = drDetail["ADMINISTRATION"].ToString();
                    string strFrequency = drDetail["FREQUENCY"].ToString();
                    decimal decQuantity = decimal.TryParse(drDetail["QUANTITY"].ToString(), out decQuantity) ? decQuantity : 0;
                    decimal decDosageEach = decimal.TryParse(drDetail["DOSAGE_EACH"].ToString(), out decDosageEach) ? decDosageEach : 0;
                    int iAbidance = int.TryParse(drDetail["ABIDANCE"].ToString(), out iAbidance) ? iAbidance : 0;
                    if (decQuantity == 0 || decDosageEach == 0 || string.IsNullOrEmpty(strAdministration) || string.IsNullOrEmpty(strFrequency) || iAbidance == 0)
                    {
                        UIMessageBox.Warn("请输入第" + (i + 1) + "行药品【" + strDrugName + "】单次剂量、用药途径、频次、数量、天数不能为空！");
                        return -1;
                    }

                    decimal decCosts = decimal.TryParse(drDetail["COSTS"].ToString(), out decCosts) ? decCosts : 0;
                    if (decCosts <= 0 && !string.IsNullOrEmpty(strDrugCode))
                    {
                        UIMessageBox.Warn("第＜" + (i + 1) + "＞行药品【" + strDrugName + "】价表错误，请修改正确后再保存！");
                        return -1;
                    }

                    // 校验药品是否有库存是否充足
                    string strPackageSpec = drDetail["PACKAGE_SPEC"].ToString();
                    string strFirmId = drDetail["FIRM_ID"].ToString();
                    string strDispensary = drDetail["DISPENSARY"].ToString();
                    string strDrugStockWhere = "drug_code = '" + strDrugCode + "' And package_spec = '" + strPackageSpec + "' And firm_id = '" + strFirmId + "' And Storage = '" + strDispensary + "' And supply_indicator = '1'";
                    decimal decSumQuantity = decimal.TryParse(Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetDrugStockValue(strDrugStockWhere).ToString(), out decSumQuantity) ? decSumQuantity : 0;
                    decimal decTempAmount = 0;//待发药数量

                    // 查询待发药处药品数量
                    if (Parameter.CHECK_MODE == "1")
                    {
                        string strVOutpDrugUsableQuantityWhere = "drug_code = '" + strDrugCode + "'  And firm_id = '" + strFirmId + "' And  package_spec = '" + strPackageSpec + "' And Storage = '" + strDispensary + "'";
                        decTempAmount = decimal.TryParse(Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetVOutpDrugUsableQuantityValue(strVOutpDrugUsableQuantityWhere).ToString(), out decTempAmount) ? decTempAmount : 0;
                        decTempAmount = decSumQuantity - decTempAmount;
                    }
                    if (decSumQuantity < (decQuantity + decTempAmount))
                    {
                        if (Parameter.AMOUNT_MUST_ENOUGH == "1")
                        {
                            UIMessageBox.Warn("处方第" + (i + 1) + "行药品【" + strDrugName + "】库存不足(考虑待发药药品，实际余量：" + (decSumQuantity - decTempAmount) + ")，请确认！");
                            return -1;
                        }
                        else
                        {
                            UIMessageBox.Warn("处方第" + (i + 1) + "行药品【" + strDrugName + "】库存不足(实际余量：" + (decSumQuantity - decTempAmount) + ")，请注意！");
                        }
                    }
                }
                return 0;
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("查看药品库存是否足够时出错!详情" + ex.ToString());
                return -1;
            }


        }

        /// <summary>
        /// 毒理分类
        /// </summary>
        /// <returns></returns>
        private string GetToxiProperty(int irowHandle)
        {
            string strToxiProperty = "普通";
            try
            {
                string strDrugCode = gvDetail.GetRowCellValue(irowHandle, "DRUG_CODE").ToString();
                string strDrugSpec = gvDetail.GetRowCellValue(irowHandle, "PACKAGE_SPEC").ToString();//gvDetail.GetRowCellValue(irowHandle, "DRUG_SPEC").ToString();
                string strFirmId = gvDetail.GetRowCellValue(irowHandle, "FIRM_ID").ToString();

                string strWhere = "drug_code = '" + strDrugCode + "' And drug_spec = '" + strDrugSpec + "' And firm_id = '" + strFirmId + "' And rownum = 1";
                string strMinSpec = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetMinSpec(strWhere).ToString();

                strWhere = "drug_code = '" + strDrugCode + "' And drug_spec = '" + strMinSpec + "'";
                string strDBToxiProperty = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetToxiProperty(strWhere).ToString();
                if (!string.IsNullOrEmpty(strDBToxiProperty))
                {
                    strDBToxiProperty = ";" + strDBToxiProperty + ";";

                    // 药品毒理属性
                    //ParameterItem parameterItem1 = new ParameterItem();
                    //parameterItem1.APP_NAME = "*";
                    //parameterItem1.DEPT_CODE = "*";
                    //parameterItem1.EMP_NO = "*";
                    //parameterItem1.PARAMETER_NAME = "presc_toxi_protities_jingshen";
                    //parameterItem1.DEFAULT_VALUE = "";
                    string strPrescToxiProtitiesJingshen = Parameter.PRESC_TOXI_PROTITIES_JINGSHEN;// Global.GetParameterValue(parameterItem1);//精神二类
                    strPrescToxiProtitiesJingshen = ";" + strPrescToxiProtitiesJingshen + ";";

                    //ParameterItem parameterItem2 = new ParameterItem();
                    //parameterItem2.APP_NAME = "*";
                    //parameterItem2.DEPT_CODE = "*";
                    //parameterItem2.EMP_NO = "*";
                    //parameterItem2.PARAMETER_NAME = "presc_toxi_protities_duma";
                    //parameterItem2.DEFAULT_VALUE = "";
                    string strPrescToxiProtitiesDuma = Parameter.PRESC_TOXI_PROTITIES_DUMA;// Global.GetParameterValue(parameterItem2);//毒麻;精神一类;麻醉药品
                    strPrescToxiProtitiesDuma = ";" + strPrescToxiProtitiesDuma + ";";

                    if (strPrescToxiProtitiesJingshen.Contains(strDBToxiProperty))
                    {
                        strToxiProperty = "精二";
                    }
                    else
                    {
                        if (strPrescToxiProtitiesDuma.Contains(strDBToxiProperty))
                        {
                            strToxiProperty = "毒麻";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("获取药品毒理属性时出错!详情" + ex.ToString());
            }

            return strToxiProperty;
        }

        /// <summary>
        /// 新加药Detail生成OrderNo
        /// </summary>
        private void SetDetailOrderNo()
        {
            if (gvDetail.RowCount == 0)
            {
                return;
            }

            try
            {
                DataRow drList = gvList.GetDataRow(gvList.FocusedRowHandle);
                string strObVisitNo = drList["OB_VISIT_NO"].ToString();

                int iMaxOrderNO = -100;
                int iOrderNo;
                if (!string.IsNullOrEmpty(strObVisitNo))//原方修改(增加、修改、删除)
                {
                    for (int i = 0; i < gvDetail.RowCount; i++)
                    {
                        //为新添加药品设置order_no
                        DataRow drDetail = gvDetail.GetDataRow(i);
                        //为新增数据(新加药) 或者修改数据(主要针对取消子处方 order_no需要重新赋值 YYS 2020/9/3)
                        if (drDetail.RowState == DataRowState.Added || (drDetail.RowState == DataRowState.Modified && drDetail["ORDER_NO"] == DBNull.Value))
                        {
                            if (iMaxOrderNO < 0)
                            {
                                string strMaxOrderNoWhere = "patient_id = '" + PatientInfo.PatientId + "' And ob_visit_no = '" + PatientInfo.ObVisitNo + "'";
                                object oMaxOrderNo = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetMaxOrderNO(strMaxOrderNoWhere);
                                iMaxOrderNO = int.TryParse(oMaxOrderNo != null ? oMaxOrderNo.ToString() : "", out iMaxOrderNO) ? iMaxOrderNO : 0;
                            }
                            if (i == 1 || int.Parse(drDetail["ORDER_SUB_NO"].ToString()) == 1)
                            {
                                iMaxOrderNO = iMaxOrderNO + 1;
                                iOrderNo = iMaxOrderNO;
                            }
                            else
                            {
                                iOrderNo = int.Parse(gvDetail.GetDataRow(i - 1)["ORDER_NO"].ToString());
                            }
                            drDetail["ORDER_NO"] = iOrderNo;

                            Utility.LogFile.WriteLogAuto("保存处方SetDetailOrderNo在原处方新加药/或取消子处方赋值OrderNo:OrderNo:" + iOrderNo
                                + ",ORDER_SUB_NO:" + drDetail["ORDER_SUB_NO"].ToString() + ",DRUG_NAME:" + drDetail["DRUG_NAME"].ToString(), "TJHIS_ObOutp_View.txt");
                        }
                    }
                }
                else//新方
                {
                    string strMaxOrderNoWhere = "patient_id = '" + PatientInfo.PatientId + "' And ob_visit_no = '" + PatientInfo.ObVisitNo + "'";
                    iMaxOrderNO = int.TryParse(Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetMaxOrderNO(strMaxOrderNoWhere).ToString(), out iMaxOrderNO) ? iMaxOrderNO : 0;
                    iOrderNo = iMaxOrderNO;
                    for (int i = 0; i < gvDetail.RowCount; i++)
                    {
                        if (int.Parse(gvDetail.GetRowCellValue(i, "ORDER_SUB_NO").ToString()) == 1)
                        {
                            iOrderNo = iOrderNo + 1;
                        }
                        gvDetail.SetRowCellValue(i, "ORDER_NO", iOrderNo);
                    }
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("新加药生成ORDER_NO出错!详情" + ex.ToString());
            }

        }

        ///// <summary>
        ///// 医嘱表中order_no最大值
        ///// </summary>
        ///// <returns></returns>
        //private int getMaxOrderNumber()
        //{
        //    int iMaxOrderNO = 0;
        //    string strMaxOrderNoWhere = "patient_id = '" + PatientInfo.PatientId + "' And ob_visit_no = '" + PatientInfo.ObVisitNo + "'";
        //    object oMaxOrderNo = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetMaxOrderNO(strMaxOrderNoWhere);
        //    iMaxOrderNO= int.TryParse(oMaxOrderNo != null ? oMaxOrderNo.ToString() : "", out iMaxOrderNO) ? iMaxOrderNO : 0;
        //    return iMaxOrderNO+1;
        //}

        /// <summary>
        /// 整理处方号、分方.返回dtNewPresc
        /// </summary>
        private void DetailClassity(ref DataTable dtNewPresc)
        {
            if (gvDetail.RowCount == 0)
            {
                return;
            }

            try
            {
                DataRow drList = gvList.GetDataRow(gvList.FocusedRowHandle);

                string strObVisitNo = drList["OB_VISIT_NO"].ToString();

                //分方
                //new_row_flag=1新增记录标志
                string strToxiProperty = "普通";
                if (!string.IsNullOrEmpty(strObVisitNo))
                {
                    strToxiProperty = GetToxiProperty(0);//毒理属性
                }

                for (int i = 0; i < gvDetail.RowCount; i++)
                {
                    //非新增行
                    if (gvDetail.GetDataRow(i).RowState != DataRowState.Added)
                    {
                        gvDetail.SetRowCellValue(i, "TOXI_PROPERTY", strToxiProperty);
                    }
                }

                // 新开处方，根据药局、毒理进行自动分方
                //dw_detail.SetSort("new_row_flag a,dispensary a, toxi_property a, order_no, order_sub_no")
                //dw_detail.Sort()

                //不能在上面排序   ITEM_NO重新排序后  多条数据更新 更新表OB_DRUG_PRESC_DETAIL会报错 主键冲突 放到下面
                //2020/8/22  YYS
                //if (this.gvDetail.SortInfo.Count <= 0)
                //{
                //    this.gvDetail.SortInfo.AddRange(new DevExpress.XtraGrid.Columns.GridColumnSortInfo[] {
                //new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.gridColumn39, DevExpress.Data.ColumnSortOrder.Ascending),
                //new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.gridColumn25, DevExpress.Data.ColumnSortOrder.Ascending),
                //new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.gridColumn29, DevExpress.Data.ColumnSortOrder.Ascending),
                //new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.gridColumn30, DevExpress.Data.ColumnSortOrder.Ascending),
                //new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.gridColumn31, DevExpress.Data.ColumnSortOrder.Ascending)});
                //}

                int iCount = 0;
                string strStorage = "";
                string strToxi = "";
                int iOrderNo = -100;
                //复合医嘱如果药房、毒理属性相同，则分方时不允许分开
                ///此段代码进行统计每行药品详情 记录有几条 用于后面进行分方判断限制条数 （例 子处方可能count n条）
                for (int i = gvDetail.RowCount - 1; i >= 0; i--)
                {
                    DataRow drDetaili = gvDetail.GetDataRow(i);
                    if (drDetaili["DISPENSARY"].ToString() != strStorage || drDetaili["TOXI_PROPERTY"].ToString() != strToxi || int.Parse(drDetaili["ORDER_NO"].ToString()) != iOrderNo)
                    {
                        strStorage = drDetaili["DISPENSARY"].ToString();
                        strToxi = drDetaili["TOXI_PROPERTY"].ToString();
                        iOrderNo = int.Parse(drDetaili["ORDER_NO"].ToString());

                        iCount = 1;

                        for (int j = i - 1; j >= 0; j--)
                        {
                            DataRow drDetailj = gvDetail.GetDataRow(j);
                            if (drDetailj["DISPENSARY"].ToString() != strStorage || drDetailj["TOXI_PROPERTY"].ToString() != strToxi || int.Parse(drDetailj["ORDER_NO"].ToString()) != iOrderNo)
                            {
                                break;//退出当前整个循环
                            }
                            iCount = iCount + 1;
                        }

                        drDetaili["ORDER_DRUG_COUNT"] = iCount;
                    }
                    else
                    {
                        drDetaili["ORDER_DRUG_COUNT"] = iCount;
                    }
                }

                // 根据西药处方药品限制数重新进行分方，生成处方号
                int iTotalCount = 0;
                int iItemNo = 0;
                int iPrescNo;
                DateTime dtPrescDate;
                //DateTime dtSysDate = DataServerBase.GetServerTime();//获取系统时间
                dtNewPresc = dtList.Clone();
                //if (gvDetail.RowCount>0)
                //{
                strStorage = gvDetail.GetRowCellValue(0, "DISPENSARY").ToString();
                strToxi = gvDetail.GetRowCellValue(0, "TOXI_PROPERTY").ToString();
                iPrescNo = int.Parse(gvDetail.GetRowCellValue(0, "PRESC_NO").ToString());
                dtPrescDate = DateTime.Parse(gvDetail.GetRowCellValue(0, "PRESC_DATE").ToString());
                //}


                for (int i = 0; i < gvDetail.RowCount; i++)
                {
                    DataRow drDetail = gvDetail.GetDataRow(i);

                    //iCount = int.Parse(drDetail["ORDER_DRUG_COUNT"].ToString());
                    iCount = drDetail["ORDER_DRUG_COUNT"].ToInt(0);  //**********
                    if ((strToxi != drDetail["TOXI_PROPERTY"].ToString() || strStorage != drDetail["DISPENSARY"].ToString() || (drDetail["ORDER_SUB_NO"].ToInt(0) == 1) && (iTotalCount + iCount > int.Parse(Parameter.MAXPRESC) && int.Parse(Parameter.MAXPRESC) > 0)))
                    {

                        //int.Parse(drDetail["ORDER_SUB_NO"].ToString()) == 1 //**********
                        iTotalCount = iCount;

                        iItemNo = 1;
                        iPrescNo = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetPrescNo();//生成新的处方号,分方
                        //dtPrescDate = dtSysDate;

                        strStorage = drDetail["DISPENSARY"].ToString();
                        strToxi = drDetail["TOXI_PROPERTY"].ToString();
                    }
                    else
                    {
                        //if (drDetail["NEW_ROW_FLAG"].ToString().Trim().Equals("0"))
                        //    iItemNo = int.Parse(drDetail["ITEM_NO"].ToString());
                        //else
                        iItemNo = iItemNo + 1;
                        if (drDetail["ORDER_SUB_NO"].ToInt(0) == 1)
                        //if (int.Parse(drDetail["ORDER_SUB_NO"].ToString()) == 1) //**********
                        {
                            iTotalCount = iTotalCount + iCount;
                        }
                    }

                    drDetail["PRESC_DATE"] = dtPrescDate;
                    drDetail["PRESC_NO"] = iPrescNo;
                    //if (drDetail["NEW_ROW_FLAG"].ToString().Trim().Equals("1"))
                    drDetail["ITEM_NO"] = iItemNo;

                }
                ////在ITEM_NO赋值之后排序
                //if (this.gvDetail.SortInfo.Count <= 0)
                //{
                //    this.gvDetail.SortInfo.AddRange(new DevExpress.XtraGrid.Columns.GridColumnSortInfo[] {
                //new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.gridColumn39, DevExpress.Data.ColumnSortOrder.Ascending),
                //new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.gridColumn25, DevExpress.Data.ColumnSortOrder.Ascending),
                //new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.gridColumn29, DevExpress.Data.ColumnSortOrder.Ascending),
                //new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.gridColumn30, DevExpress.Data.ColumnSortOrder.Ascending),
                //new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.gridColumn31, DevExpress.Data.ColumnSortOrder.Ascending)});
                //}
                // 计算当前处方金额合计
                decimal decCosts = 0;
                decimal decPayments = 0;
                iPrescNo = int.Parse(drList["PRESC_NO"].ToString());
                for (int i = 0; i < gvDetail.RowCount; i++)
                {
                    DataRow drDetail = gvDetail.GetDataRow(i);
                    if (iPrescNo != int.Parse(drDetail["PRESC_NO"].ToString()))
                    {
                        break;
                    }
                    decCosts = decCosts + decimal.Parse(drDetail["COSTS"].ToString());
                    decPayments = decPayments + decimal.Parse(drDetail["PAYMENTS"].ToString());
                }
                drList["COSTS"] = decCosts;
                drList["PAYMENTS"] = decPayments;
                drList["DISPENSARY"] = gvDetail.GetRowCellValue(0, "DISPENSARY");

                // 动态插入后两张处方处方主记录
                decCosts = 0;
                decPayments = 0;
                iPrescNo = -100;
                string strDispensaryTemp = "";
                for (int i = gvDetail.RowCount - 1; i >= 0; i--)
                {
                    DataRow drDetail = gvDetail.GetDataRow(i);

                    if (iPrescNo != int.Parse(drDetail["PRESC_NO"].ToString()))
                    {
                        if (iPrescNo != -100)
                        {
                            // 插入新处方主记录
                            DataRow drNewPresc = dtNewPresc.NewRow();

                            foreach (DevExpress.XtraGrid.Columns.GridColumn dc in gvList.Columns)
                            {
                                drNewPresc[dc.FieldName] = drList[dc.FieldName];
                            }
                            drNewPresc["PRESC_DATE"] = dtPrescDate;
                            drNewPresc["PRESC_NO"] = iPrescNo;
                            drNewPresc["COSTS"] = decCosts;
                            drNewPresc["PAYMENTS"] = decPayments;
                            drNewPresc["DISPENSARY"] = strDispensaryTemp;
                            drNewPresc["HIS_UNIT_CODE"] = SystemParm.HisUnitCode;

                            dtNewPresc.Rows.Add(drNewPresc);
                        }

                        dtPrescDate = DateTime.Parse(gvDetail.GetRowCellValue(i, "PRESC_DATE").ToString());
                        iPrescNo = int.Parse(gvDetail.GetRowCellValue(i, "PRESC_NO").ToString());
                        strDispensaryTemp = gvDetail.GetRowCellValue(i, "DISPENSARY").ToString();
                        decCosts = 0;
                        decPayments = 0;


                        if (iPrescNo == int.Parse(drList["PRESC_NO"].ToString()))
                        {
                            break;
                        }

                    }

                    decCosts = decCosts + decimal.Parse(drDetail["COSTS"].ToString());
                    decPayments = decPayments + decimal.Parse(drDetail["PAYMENTS"].ToString());
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方分方时出错!详情" + ex.ToString());
            }

        }

        /// <summary>
        /// 给处方新方赋值OB_VISIT_NO
        /// </summary>
        /// <param name="dtNewPresc"></param>
        private void SetListObVisitNo(DataTable dtNewPresc)
        {
            int iFocusedRowHandle = gvList.FocusedRowHandle;
            string strListObVisitNo = gvList.GetRowCellValue(iFocusedRowHandle, "OB_VISIT_NO").ToString();
            if (string.IsNullOrEmpty(strListObVisitNo))
            {
                gvList.SetRowCellValue(iFocusedRowHandle, "OB_VISIT_NO", PatientInfo.ObVisitNo);
            }

            for (int i = 0; i < dtNewPresc.Rows.Count; i++)
            {
                dtNewPresc.Rows[i]["OB_VISIT_NO"] = PatientInfo.ObVisitNo;
            }
        }

        /// <summary>
        /// 根据Detail生成费用 cost bill
        /// </summary>
        private int ClinicVsCharge(DataRow drDetail, DataTable dtObOutpBillDetail, DataTable dtObOrdersCosts)
        {
            try
            {
                // 若不自动根据途径添加途径治疗费用，则转换途径以免产生费用
                string strDetailAdministration = drDetail["ADMINISTRATION"].ToString();
                if (string.IsNullOrEmpty(Parameter.AUTO_ADMINISTRATION) || Parameter.AUTO_ADMINISTRATION == "0")
                {
                    strDetailAdministration = "###";
                }

                //子处方途径不应绑定材料费。因为复合处方主处方和子处方的途径是一样的,只需主处方绑定材料费即可
                int iDetailOrderSubNo = int.Parse(drDetail["ORDER_SUB_NO"].ToString());
                if (iDetailOrderSubNo > 1)//子处方
                {
                    strDetailAdministration = "";
                }

                string strDetailDrugCode = drDetail["DRUG_CODE"].ToString();
                StringBuilder sbWhere = new StringBuilder();
                sbWhere.Append("(CLINIC_VS_CHARGE.CLINIC_ITEM_CLASS = CLINIC_ITEM_NAME_DICT.ITEM_CLASS) and");
                sbWhere.Append("( CLINIC_VS_CHARGE.CLINIC_ITEM_CODE = CLINIC_ITEM_NAME_DICT.ITEM_CODE ) and ");
                sbWhere.Append("( CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS = PRICE_ITEM_NAME_DICT.ITEM_CLASS ) and ");
                sbWhere.Append("( CLINIC_VS_CHARGE.CHARGE_ITEM_CODE = PRICE_ITEM_NAME_DICT.ITEM_CODE ) and ");
                sbWhere.Append("(( CLINIC_ITEM_NAME_DICT.ITEM_CODE = '" + strDetailDrugCode + "'  AND CLINIC_ITEM_NAME_DICT.ITEM_CLASS = 'A' )");
                sbWhere.Append("or (CLINIC_ITEM_NAME_DICT.ITEM_NAME = '" + strDetailAdministration + "' AND CLINIC_ITEM_NAME_DICT.ITEM_CLASS = 'E' ))  AND ");
                sbWhere.Append("PRICE_ITEM_NAME_DICT.STD_INDICATOR = 1 and ");
                sbWhere.Append("CLINIC_ITEM_NAME_DICT.STD_INDICATOR = 1");
                //获取计价项目  包括途径绑定材料费用
                DataTable dtAdminCost = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetAdminCostDT(sbWhere.ToString());

                //产生计价项目
                int iDetailOrderNo = int.Parse(drDetail["ORDER_NO"].ToString());
                string strDetailClinicSerialNo = drDetail["CLINIC_SERIAL_NO"].ToString();

                string strWhere = "PATIENT_ID='" + PatientInfo.PatientId + "'";
                string strWardCode = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetWardCodeValue(strWhere).ToString();
                string strDeptCOde = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetDeptCodeValue("PATIENT_ID='" + PatientInfo.PatientId + "'").ToString();

                DateTime dtSysdate = DataServerBase.GetServerTime();

                decimal decAmount;
                string strPerformedBy;
                string strItemSpec;
                string strUnits;
                for (int i = 0; i < dtAdminCost.Rows.Count; i++)
                {
                    DataRow drAdminCost = dtAdminCost.Rows[i];

                    string strItemName = drAdminCost["ITEM_NAME"].ToString();
                    string strItemCode = drAdminCost["CHARGE_ITEM_CODE"].ToString();
                    string strItemClass = drAdminCost["CHARGE_ITEM_CLASS"].ToString();

                    DataRow drCosts = dtObOrdersCosts.NewRow();

                    drCosts["PATIENT_ID"] = PatientInfo.PatientId;
                    drCosts["OB_VISIT_NO"] = PatientInfo.ObVisitNo;
                    drCosts["ORDER_NO"] = iDetailOrderNo;
                    drCosts["ORDER_SUB_NO"] = iDetailOrderSubNo;
                    //drCosts["ITEM_NO"] = i;
                    drCosts["ITEM_NO"] = 0;
                    drCosts["ITEM_NAME"] = strItemName;
                    drCosts["ITEM_CODE"] = strItemCode;
                    drCosts["ITEM_CLASS"] = strItemClass;
                    if (strItemClass == "A" || strItemClass == "B")
                    {
                        decAmount = decimal.Parse(drDetail["QUANTITY"].ToString());//药品数量以处方为准
                        strPerformedBy = drDetail["DISPENSARY"].ToString();//药品执行以处方科室为准
                        strItemSpec = drDetail["PACKAGE_SPEC"].ToString() + drDetail["FIRM_ID"].ToString();
                        strUnits = drDetail["PACKAGE_UNITS"].ToString();
                    }
                    else
                    {
                        //药品对应的材料费
                        decAmount = int.Parse(drDetail["PERFORM_TIMES"].ToString());//非药品以执行次数为准
                        strPerformedBy = strWardCode;//非药品以病人所在护理单元
                        strItemSpec = drAdminCost["CHARGE_ITEM_SPEC"].ToString();
                        strUnits = drAdminCost["UNITS"].ToString();
                    }
                    drCosts["ITEM_SPEC"] = strItemSpec;
                    drCosts["AMOUNT"] = decAmount;
                    drCosts["UNITS"] = strUnits;

                    string strBillSerialNo = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetPatternSerialNo().ToString();
                    drCosts["CLINIC_SERIAL_NO"] = strDetailClinicSerialNo;
                    drCosts["PATTERN_SERAIL_NO"] = strBillSerialNo;
                    drCosts["HIS_UNIT_CODE"] = SystemParm.HisUnitCode;

                    //根据计价项目产生费用
                    int iChargePriceIndicator = 0;
                    string strChargeTypeDictWhere = "charge_type_name = '" + PatientInfo.ChargeType + "'" + " and (his_unit_code = '" + SystemParm.HisUnitCode + "') ";
                    DataTable dtChargeTypeDict = FrmPrescDoctInputSrv.GetChargeTypeDictDT(strChargeTypeDictWhere);
                    if (dtChargeTypeDict.Rows.Count > 0)
                    {
                        iChargePriceIndicator = int.Parse(dtChargeTypeDict.Rows[0]["CHARGE_PRICE_INDICATOR"].ToString());
                    }

                    StringBuilder sbPriceWhere = new StringBuilder();
                    sbPriceWhere.Append("a.ITEM_CLASS = '" + strItemClass + "' And ");
                    sbPriceWhere.Append("a.ITEM_CODE = '" + strItemCode + "' and ");
                    sbPriceWhere.Append("(a.ITEM_SPEC = '" + strItemSpec + "' Or a.ITEM_SPEC IS Null) And ");
                    sbPriceWhere.Append("(a.UNITS = '" + strUnits + "' ) And ");
                    sbPriceWhere.Append("b.start_date <= sysdate And (b.stop_date >= sysdate Or b.stop_date Is Null) and ");
                    sbPriceWhere.Append("b.his_unit_code='" + SystemParm.HisUnitCode + "'");
                    DataTable dtPriceList = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetPriceListDT(sbPriceWhere.ToString());
                    decimal decPrice = 0;
                    switch (iChargePriceIndicator)
                    {
                        case 0:
                            decPrice = decimal.Parse(dtPriceList.Rows[0]["PRICE"].ToString());
                            break;
                        case 1:
                            decPrice = decimal.Parse(dtPriceList.Rows[0]["PREFER_PRICE"].ToString());
                            break;
                        case 2:
                            decPrice = decimal.Parse(dtPriceList.Rows[0]["FOREIGNER_PRICE"].ToString());
                            break;
                    }
                    //f_his21_calc_charge_price(sqlca, is_chargetype, itemclass, itemcode, itemspec, ldec_price, //ii_price_numerator / ii_price_denominator, ldc_charges)
                    decimal decChargePrice = 0;
                    CalcChargePriceItem calcChargePriceItem = new CalcChargePriceItem();
                    calcChargePriceItem.ChargeType = PatientInfo.ChargeType;
                    calcChargePriceItem.ItemClass = strItemClass;
                    calcChargePriceItem.ItemCode = strItemCode;
                    calcChargePriceItem.ItemSpec = strItemSpec;
                    calcChargePriceItem.Price = decPrice;
                    calcChargePriceItem.ChargePrice = decChargePrice;
                    calcChargePriceItem.DefaultFactor = 1;// Global.GetDefaultFatorValue(PatientInfo.ChargeType);
                    CalcChargePrice calcChargePrice = new CalcChargePrice();
                    calcChargePrice.Calc(calcChargePriceItem);

                    //drCosts["COSTS"] = decAmount * decAmount * calcChargePriceItem.Price;

                    drCosts["COSTS"] = decAmount * calcChargePriceItem.Price;

                    dtObOrdersCosts.Rows.Add(drCosts);

                    DataRow drBillDetail = dtObOutpBillDetail.NewRow();

                    drBillDetail["PATIENT_ID"] = PatientInfo.PatientId;
                    drBillDetail["OB_VISIT_NO"] = PatientInfo.ObVisitNo;
                    drBillDetail["VISIT_NO"] = int.Parse(Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetVisitNoValue().ToString());
                    drBillDetail["ITEM_NO"] = dtObOutpBillDetail.Rows.Count + 1;
                    drBillDetail["CLASS_ON_RCPT"] = dtPriceList.Rows[0]["CLASS_ON_OUTP_RCPT"];
                    drBillDetail["ITEM_CLASS"] = strItemClass;
                    drBillDetail["ITEM_NAME"] = strItemName;
                    drBillDetail["ITEM_CODE"] = strItemCode;
                    drBillDetail["ITEM_SPEC"] = strItemSpec;
                    drBillDetail["UNITS"] = strUnits;
                    drBillDetail["ORDERED_BY"] = strDeptCOde;
                    drBillDetail["ORDERED_BY_DOCTOR"] = SystemParm.LoginUser.USER_NAME;
                    drBillDetail["PERFORMED_BY"] = strPerformedBy;
                    drBillDetail["AMOUNT"] = decAmount;
                    drBillDetail["COSTS"] = decAmount * calcChargePriceItem.Price;
                    drBillDetail["CHARGES"] = decAmount * calcChargePriceItem.ChargePrice;
                    drBillDetail["BILLING_DATE_TIME"] = dtSysdate;
                    drBillDetail["OPERATOR_NO"] = SystemParm.LoginUser.ID;
                    drBillDetail["CHARGE_INDICATOR"] = 0;
                    drBillDetail["OPER_CODE"] = iDetailOrderNo.ToString();
                    drBillDetail["OPER_TYPE"] = strItemClass;
                    drBillDetail["ITEM_PRICE"] = calcChargePriceItem.ChargePrice;

                    drBillDetail["CLINIC_SERIAL_NO"] = strDetailClinicSerialNo;
                    drBillDetail["BILL_SERAIL_NO"] = strBillSerialNo;
                    drBillDetail["HIS_UNIT_CODE"] = SystemParm.HisUnitCode;

                    dtObOutpBillDetail.Rows.Add(drBillDetail);

                    Utility.LogFile.WriteLogAuto("保存处方ClinicVsCharge：生成医嘱对应的计价项目信息：PATIENT_ID:" + drBillDetail["PATIENT_ID"] + ",ITEM_NAME:" + strItemName + ",CLINIC_SERIAL_NO:" + strDetailClinicSerialNo, "TJHIS_ObOutp_View.txt");
                }
                return 0;
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("医嘱生成计价费用过程中出错!详情" + ex.ToString());
                return -1;
            }

        }
        /// <summary>
        /// 由明细生成医嘱、费用
        /// </summary>
        private int DetailInsertOrderCost(DataTable dtOrders, DataTable dtObOutpBillDetail, DataTable dtObOrdersCosts)
        {
            try
            {
                // 根据新添加处方药品，生成医嘱记录、费用
                string strClinicSerialNo;
                DataRow drObOrders = null;
                DataRow[] drObOrdersSelects;
                DateTime dtSysdate = DataServerBase.GetServerTime();
                int resultCode = 0;
                string strOrderBy = gvList.GetRowCellValue(gvList.FocusedRowHandle, "ORDERED_BY").ToString();
                //查询当前detail中的药品  修改或添加 删除医嘱中的数据
                for (int i = 0; i < dtSaveDetail.Rows.Count; i++)
                {
                    DataRow drDetail = dtSaveDetail.Rows[i];
                    switch (drDetail.RowState)
                    {
                        case DataRowState.Detached:
                            continue;
                        case DataRowState.Unchanged:
                            continue;
                        case DataRowState.Deleted:
                            try
                            {
                                strClinicSerialNo = drDetail["CLINIC_SERIAL_NO", DataRowVersion.Original].ToString();

                                drObOrdersSelects = dtOrders.Select("CLINIC_SERIAL_NO='" + strClinicSerialNo + "'");
                                if (drObOrdersSelects.Length == 0)
                                {
                                    UIMessageBox.Warn("第" + (i + 1).ToString() + "行,没有找到对应的医嘱记录...!");
                                    return -1;
                                }
                                drObOrders = drObOrdersSelects[0];

                                //减药保存后  改成作废状态 YYS 2020/8/18
                                //作废医嘱
                                if (drObOrders["ORDER_STATUS"].ToString() != "4")
                                {
                                    drObOrders["ORDER_STATUS"] = "4";// 医嘱状态直接改为提交状态 改为作废状态
                                    drObOrders["BILLING_ATTR"] = 3;
                                    drObOrders["DRUG_BILLING_ATTR"] = 3;
                                    drObOrders["CANCEL_DOCTOR"] = SystemParm.LoginUser.USER_NAME;//作废医生
                                    drObOrders["CANCEL_DATE_TIME"] = dtSysdate; //作废时间

                                    string strOrderText = drObOrders["ORDER_TEXT"].ToString();
                                    if (strOrderText.Substring(0, 2) != "作废")
                                    {
                                        drObOrders["ORDER_TEXT"] = "作废" + strOrderText;
                                    }
                                }

                                //删除对应的费用
                                for (int j = dtObOutpBillDetail.Rows.Count - 1; j >= 0; j--)
                                {
                                    if (dtObOutpBillDetail.Rows[j].RowState.Equals(DataRowState.Deleted))
                                        continue;
                                    if (strClinicSerialNo == dtObOutpBillDetail.Rows[j]["CLINIC_SERIAL_NO"].ToString())
                                    {
                                        dtObOutpBillDetail.Rows[j].Delete();//打删除标记
                                    }
                                }
                                for (int j = dtObOrdersCosts.Rows.Count - 1; j >= 0; j--)
                                {
                                    if (dtObOrdersCosts.Rows[j].RowState.Equals(DataRowState.Deleted))
                                        continue;
                                    if (strClinicSerialNo == dtObOrdersCosts.Rows[j]["CLINIC_SERIAL_NO"].ToString())
                                    {
                                        dtObOrdersCosts.Rows[j].Delete();//打删除标记
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                UIMessageBox.Err("明细生成医嘱相关费用时出错!详情" + ex.ToString());
                            }


                            continue;
                        case DataRowState.Modified:
                            strClinicSerialNo = drDetail["CLINIC_SERIAL_NO"].ToString();

                            drObOrdersSelects = dtOrders.Select("CLINIC_SERIAL_NO='" + strClinicSerialNo + "'");
                            if (drObOrdersSelects.Length == 0)
                            {
                                UIMessageBox.Warn("第" + (i + 1).ToString() + "行,没有找到对应的医嘱记录...!");
                                return -1;
                            }
                            drObOrders = drObOrdersSelects[0];

                            //删除对应的费用
                            for (int j = dtObOutpBillDetail.Rows.Count - 1; j >= 0; j--)
                            {
                                if (dtObOutpBillDetail.Rows[j].RowState.Equals(DataRowState.Deleted))
                                    continue;
                                if (strClinicSerialNo == dtObOutpBillDetail.Rows[j]["CLINIC_SERIAL_NO"].ToString())
                                {
                                    dtObOutpBillDetail.Rows[j].Delete();//打删除标记
                                }
                            }
                            for (int j = dtObOrdersCosts.Rows.Count - 1; j >= 0; j--)
                            {
                                if (dtObOrdersCosts.Rows[j].RowState.Equals(DataRowState.Deleted))
                                    continue;
                                if (strClinicSerialNo == dtObOrdersCosts.Rows[j]["CLINIC_SERIAL_NO"].ToString())
                                {
                                    dtObOrdersCosts.Rows[j].Delete();//打删除标记
                                }
                            }
                            break;


                        case DataRowState.Added:
                            strClinicSerialNo = drDetail["CLINIC_SERIAL_NO"].ToString();

                            drObOrders = dtOrders.NewRow();
                            drObOrders["CLINIC_SERIAL_NO"] = strClinicSerialNo;
                            drObOrders["PATIENT_ID"] = PatientInfo.PatientId;
                            drObOrders["OB_VISIT_NO"] = PatientInfo.ObVisitNo;

                            drObOrders["ENTER_DATE_TIME"] = dtSysdate;//开医嘱录入日期及时间
                            drObOrders["HIS_UNIT_CODE"] = SystemParm.HisUnitCode;
                            break;
                    }

                    drObOrders["PRESC_DATE"] = drDetail["PRESC_DATE"];//记录处方日期.存在分方的情况,所以不能取List的
                    drObOrders["PRESC_ITEM_NO"] = drDetail["ITEM_NO"];//记录处方项目序号

                    //西药处方生成的临时医嘱，将药品规格、数量、单位显示在医嘱的医生说明字段中
                    string strDetailFreqDetail = drDetail["FREQ_DETAIL"].ToString();//医生说明
                    if (string.IsNullOrEmpty(strDetailFreqDetail))
                    {
                        drObOrders["FREQ_DETAIL"] = drDetail["QUANTITY"].ToString() + drDetail["PACKAGE_UNITS"].ToString() + "×" + drDetail["PACKAGE_SPEC"].ToString() + drDetail["FREQUENCY"].ToString();
                    }
                    else
                    {
                        drObOrders["FREQ_DETAIL"] = strDetailFreqDetail + drDetail["QUANTITY"].ToString() + drDetail["PACKAGE_UNITS"].ToString() + "×" + drDetail["PACKAGE_SPEC"].ToString() + drDetail["FREQUENCY"].ToString();
                    }
                    drObOrders["ORDER_NO"] = drDetail["ORDER_NO"];
                    drObOrders["ORDER_SUB_NO"] = drDetail["ORDER_SUB_NO"];
                    drObOrders["ORDER_STATUS"] = "2";
                    drObOrders["REPEAT_INDICATOR"] = 0;
                    drObOrders["START_DATE_TIME"] = drDetail["PRESC_DATE"];
                    drObOrders["ORDER_CLASS"] = "A";
                    drObOrders["ORDER_TEXT"] = drDetail["DRUG_NAME"];
                    drObOrders["ORDER_CODE"] = drDetail["DRUG_CODE"];
                    drObOrders["SKIN_TEST"] = drDetail["SKIN_TEST"];
                    drObOrders["ADMINISTRATION"] = drDetail["ADMINISTRATION"];

                    drObOrders["BILLING_ATTR"] = 4;
                    drObOrders["DRUG_BILLING_ATTR"] = 4;

                    drObOrders["DOSAGE"] = drDetail["DOSAGE_EACH"];
                    drObOrders["DOSAGE_UNITS"] = drDetail["DOSAGE_UNITS"];
                    drObOrders["ORDERING_DEPT"] = strOrderBy;//开医嘱科室
                    drObOrders["CURRENT_PRESC_NO"] = drDetail["PRESC_NO"];//存在分方的情况,所以处方号不能取dw_master的
                    drObOrders["DOCTOR"] = SystemParm.LoginUser.USER_NAME;//开医嘱医生

                    drObOrders["FREQUENCY"] = drDetail["FREQUENCY"];
                    drObOrders["FREQ_COUNTER"] = drDetail["FREQ_COUNTER"];
                    drObOrders["FREQ_INTERVAL"] = drDetail["FREQ_INTERVAL"];
                    drObOrders["FREQ_INTERVAL_UNIT"] = drDetail["FREQ_INTERVAL_UNIT"];

                    if (drDetail.RowState == DataRowState.Added)
                    {
                        dtOrders.Rows.Add(drObOrders);
                    }
                    Utility.LogFile.WriteLogAuto("保存处方DetailInsertOrderCost：生成医嘱信息：PATIENT_ID:" + PatientInfo.PatientId + ",ORDER_TEXT:" + drDetail["DRUG_NAME"], "TJHIS_ObOutp_View.txt");
                    //根据Detail生成费用
                    resultCode = ClinicVsCharge(drDetail, dtObOutpBillDetail, dtObOrdersCosts);
                }

                return resultCode;
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("保存处方生成医嘱时出错!详情" + ex.ToString());
                return -1;
            }

        }

        /// <summary>
        /// 协定处方数据处理
        /// </summary>
        private void BindingPrecData()
        {
            try
            {
                int intListHandle;
                intListHandle = gvList.FocusedRowHandle;
                if (intListHandle < 0) return;

                FrmAgreementPres frmPres = new FrmAgreementPres();
                frmPres.FrmParent = this;
                frmPres.ShowDialog();
                if (this.ApiList.Count.Equals(0)) return;

                if (ApiList[0].PRESC_TYPE.Equals(1))
                {
                    UIMessageBox.Err("请确认处方类型是否正确！");
                    return;
                }

                string strStageName;
                string strItemClass = "A";
                string strSkinTest = "0";
                decimal decAmountPerPackage;
                int intRepetition;

                intRepetition = this.gvList.GetRowCellValue(intListHandle, "REPETITION") == null ? 0 : this.gvList.GetRowCellValue(intListHandle, "REPETITION").ToString().Trim().Length.Equals(0) ? 1 : int.Parse(this.gvList.GetRowCellValue(intListHandle, "REPETITION").ToString());
                strStageName = FrmPrescDoctInputSrv.GetStageName(ApiList[0].STARGE);

                btSave.Enabled = true;
                btAdd.Enabled = true;
                btDelete.Enabled = true;
                btDelPresc.Enabled = true;
                btChildPresc.Enabled = true;

                for (int intIndex = 0; intIndex < this.ApiList.Count; intIndex++)
                {
                    DoctorCheckLimitDrugItem item = new DoctorCheckLimitDrugItem();
                    string strDrugSpec;
                    decimal decCosts;
                    decimal decPayments;
                    decimal itemPrice;

                    item.DrugCode = this.ApiList[intIndex].DRUG_CODE;
                    item.DrugName = this.ApiList[intIndex].DRUG_NAME;
                    item.Warn = 1;
                    item.PatientInfo = this.PatientInfo;
                    item.OperTypeOne = true;
                    item.OperTypeTwo = true;
                    DrugTypeItem drugTypeItem;

                    if (Parameter.LIMIT_DRUG.Equals("1"))
                        Global.DoctorCheckLimitDrug(item);

                    if (Parameter.LIMIT_DRUG.Equals("2"))
                        if (Global.DoctorCheckLimitDrug(item).Equals(-1))
                            return;

                    strDrugSpec = FrmPrescDoctInputSrv.GetDrugSpec(this.ApiList[intIndex]);
                    if (strDrugSpec.Length.Equals(0))
                    {
                        if (UIMessageBox.YesNo("药品【" + item.DrugName + "】在【" + strStageName + "】中不存在或不可供！~r~n是否继续?") == System.Windows.Forms.DialogResult.Yes)
                            continue;
                        return;
                    }

                    DataTable table = FrmPrescDoctInputSrv.GetDrugPrices(strItemClass, item.DrugCode, this.ApiList[intIndex].PACKAGE_SPEC + this.ApiList[intIndex].FIRM_ID, this.ApiList[intIndex].PACKAGE_UNITS);
                    if (table.Rows.Count.Equals(0))
                    {
                        if (UIMessageBox.YesNo("找不到" + this.ApiList[intIndex].DRUG_NAME + "的价表信息，是否继续?") == System.Windows.Forms.DialogResult.Yes)
                            continue;
                        return;
                    }

                    if (strItemClass.Equals("A"))//校验是否需要皮试
                    {
                        if (this.SkinResult(this.ApiList[intIndex].DRUG_CODE, this.ApiList[intIndex].DRUG_NAME, ref strSkinTest).Equals(-1))
                            return;
                    }

                    decAmountPerPackage = FrmPrescDoctInputSrv.GetDrugPackage(this.ApiList[intIndex]);

                    drugTypeItem = FrmPrescDoctInputSrv.GetDrugTypeInfo(this.ApiList[intIndex]);

                    if (drugTypeItem.ToxiProperty.Trim().Length.Equals(0))
                        drugTypeItem.ToxiProperty = "普通";
                    drugTypeItem.ToxiProperty = ";" + drugTypeItem.ToxiProperty + ";";
                    if (this.strPrescToxiProtitiesJingshen.IndexOf(drugTypeItem.ToxiProperty) >= 0)
                        drugTypeItem.ToxiProperty = "精二";
                    else if (this.strPrescToxiProtitiesDuma.IndexOf(drugTypeItem.ToxiProperty) >= 0)
                        drugTypeItem.ToxiProperty = "毒麻";
                    else
                        drugTypeItem.ToxiProperty = "普通";

                    #region 划价
                    if (!drugTypeItem.DosePerUnit.Equals(0) && strItemClass.Equals("B"))
                        this.ApiList[intIndex].QUANTITY = decimal.Ceiling(intRepetition * this.ApiList[intIndex].DOSAGE / drugTypeItem.DosePerUnit);
                    if (this.ApiList[intIndex].QUANTITY > 0)
                    {
                        CalcChargePriceItem priceItem = new CalcChargePriceItem();
                        priceItem.ItemClass = strItemClass;
                        priceItem.ItemCode = this.ApiList[intIndex].DRUG_CODE;
                        priceItem.ItemSpec = this.ApiList[intIndex].PACKAGE_SPEC + this.ApiList[intIndex].FIRM_ID;
                        priceItem.ChargeType = this.PatientInfo.ChargeType;
                        priceItem.DefaultFactor = decDefaultFator;
                        priceItem.ChargePrice = 0;
                        switch (this.PatientInfo.ChargePriceIndicator)
                        {
                            case 0:
                                priceItem.Price = table.Rows[0]["PRICE"] == null ? 0 : table.Rows[0]["PRICE"].ToString().Trim().Length.Equals(0) ? 0 : decimal.Parse(table.Rows[0]["PRICE"].ToString().Trim());
                                break;
                            case 1:
                                priceItem.Price = table.Rows[0]["PREFER_PRICE"] == null ? 0 : table.Rows[0]["PREFER_PRICE"].ToString().Trim().Length.Equals(0) ? 0 : decimal.Parse(table.Rows[0]["PREFER_PRICE"].ToString().Trim());
                                break;
                            case 2:
                                priceItem.Price = table.Rows[0]["FOREIGNER_PRICE"] == null ? 0 : table.Rows[0]["FOREIGNER_PRICE"].ToString().Trim().Length.Equals(0) ? 0 : decimal.Parse(table.Rows[0]["FOREIGNER_PRICE"].ToString().Trim());
                                break;
                        }
                        calcPrice.Calc(priceItem);

                        decCosts = this.ApiList[intIndex].QUANTITY * priceItem.Price;
                        decPayments = this.ApiList[intIndex].QUANTITY * priceItem.ChargePrice;
                        itemPrice = priceItem.ChargePrice;
                    }
                    else
                    {
                        decCosts = 0;
                        decPayments = 0;
                        itemPrice = 0;
                    }
                    #endregion 划价

                    #region 生成处方明细
                    DataRow drNew = this.gvDetail.GetDataRow(this.gvDetail.RowCount - 1);
                    string tmpDrugCode = string.Empty;
                    if (drNew != null)
                        tmpDrugCode = drNew["DRUG_CODE"].ToString("");
                    if (!string.IsNullOrEmpty(tmpDrugCode))
                    {
                        int intNewRowHandle = AddNewDrug();
                        drNew = this.gvDetail.GetDataRow(intNewRowHandle);
                    }

                    drNew["DRUG_CODE"] = this.ApiList[intIndex].DRUG_CODE;
                    drNew["DRUG_SPEC"] = strDrugSpec;
                    drNew["DRUG_NAME"] = this.ApiList[intIndex].DRUG_NAME;
                    drNew["FIRM_ID"] = this.ApiList[intIndex].FIRM_ID;
                    drNew["PACKAGE_SPEC"] = this.ApiList[intIndex].PACKAGE_SPEC;
                    drNew["PACKAGE_UNITS"] = this.ApiList[intIndex].PACKAGE_UNITS;
                    drNew["DOSAGE_EACH"] = this.ApiList[intIndex].DOSAGE;
                    drNew["QUANTITY"] = this.ApiList[intIndex].QUANTITY;

                    drNew["ITEM_PRICE"] = itemPrice;
                    drNew["COSTS"] = decCosts;
                    drNew["PAYMENTS"] = decPayments;
                    drNew["FREQUENCY"] = this.ApiList[intIndex].FREQUENCY;

                    drNew["DOSAGE_UNITS"] = this.ApiList[intIndex].DOSAGE_UNITS;
                    drNew["ADMINISTRATION"] = this.ApiList[intIndex].ADMINISTRATION;
                    drNew["SPLIT_FLAG"] = this.ApiList[intIndex].SPLIT_FLAG;

                    // 设置处方Order、SubNo，若是子处方，后续通过 【子处方按钮】处理
                    if (this.ApiList[intIndex].SUB_NO.ToInt(1) == 1 || this.gvDetail.RowCount == 1)
                        drNew["ORDER_SUB_NO"] = 1;

                    drNew["DOSAGE"] = drugTypeItem.DosePerUnit;
                    drNew["DOSC_PER_UNIT"] = drugTypeItem.DosePerUnit;
                    drNew["AMOUNT_PER_PACKAGE"] = decAmountPerPackage;

                    // 处理协定处方中 performedBy 为空的情况
                    string dispensary = this.ApiList[intIndex].STARGE?.Trim() ?? "";
                    if (string.IsNullOrEmpty(dispensary))
                    {
                        // 协定处方的执行科室为空时，使用当前选择的药房
                        string strDispensary = txtDispensary.EditValue?.ToString();
                        if (!string.IsNullOrEmpty(strDispensary))
                        {
                            dispensary = strDispensary;
                        }
                        else
                        {
                            UIMessageBox.Warn("协定处方中药品【" + this.ApiList[intIndex].DRUG_NAME + "】的执行科室为空，请先选择药房！");
                            return;
                        }
                    }
                    drNew["DISPENSARY"] = dispensary;

                    drNew["TOXI_PROPERTY"] = drugTypeItem.ToxiProperty;
                    drNew["ABIDANCE"] = this.ApiList[intIndex].ABIDANCE;
                    drNew["FREQ_DETAIL"] = this.ApiList[intIndex].FREQ_DETAIL;

                    int iDBFreqCounter = 0;
                    int iDBFreqInterval = 0;
                    string strDBFreqIntervalUnits = "";
                    DataRow drFrequency = BasicDict.PerformFreqDict.AsEnumerable().FirstOrDefault(r => r["FREQ_DESC"].ToString("").Equals(this.ApiList[intIndex].FREQUENCY));
                    if (drFrequency != null)
                    {
                        iDBFreqCounter = drFrequency["FREQ_COUNTER"].ToInt(0);
                        iDBFreqInterval = drFrequency["FREQ_INTERVAL"].ToInt(0);
                        strDBFreqIntervalUnits = drFrequency["FREQ_INTERVAL_UNITS"].ToString("");
                    }
                    drNew["FREQ_COUNTER"] = iDBFreqCounter;
                    drNew["FREQ_INTERVAL"] = iDBFreqInterval;
                    drNew["FREQ_INTERVAL_UNIT"] = strDBFreqIntervalUnits;

                    //设置默认皮试路径
                    drNew["SKIN_TEST"] = strSkinTest;
                    if (strSkinTest.Equals("1"))
                        drNew["ADMINISTRATION"] = Parameter.SKIN_ADMINISTRATION;
                    //设置默认皮试路径

                    #region 公费用药提示
                    if (Global.CheckChargeType(this.PatientInfo.ChargeType).Equals(-1))
                    {
                        CheckOfficialCataLogItem offItem = new CheckOfficialCataLogItem();
                        offItem.ItemClass = strItemClass;
                        offItem.ItemCode = this.ApiList[intIndex].DRUG_CODE;
                        offItem.ItemSpec = this.ApiList[intIndex].PACKAGE_SPEC + this.ApiList[intIndex].FIRM_ID;
                        offItem.ItemType = "inp";
                        Global.CheckOfficialCatalog(ref offItem);
                        if (offItem.ReturnValue > 0)
                        {
                            if (Global.OfficialRemind.Equals("1"))
                            {
                                if (UIMessageBox.YesNo(this.PatientInfo.ChargeType + ";" + offItem.ItemMsg) == DialogResult.No)
                                    drNew["WRITEOFF"] = 1;
                            }
                            else
                                drNew["WRITEOFF"] = 1;
                        }
                    }

                    if (int.Parse(this.ApiList[intIndex].SUB_NO.ToString()) > 1)
                        this.btChildPresc_Click(this.btChildPresc, null);
                    //
                    #endregion 公费用药提示



                    #endregion 生成处方明细
                    int rowHandle = this.gvDetail.FocusedRowHandle;
                    if (rowHandle > 0)
                        gvDetail_CellValueChanged(gvDetail, new CellValueChangedEventArgs(rowHandle, gvDetail.Columns["QUANTITY"], this.ApiList[intIndex].QUANTITY));

                }

                #region 修改主记录
                this.gvList.SetRowCellValue(intListHandle, "DISPENSARY", this.ApiList[0].STARGE);
                this.gvList.SetRowCellValue(intListHandle, "PRESC_TYPE", this.ApiList[0].PRESC_TYPE);
                this.gvList.SetRowCellValue(intListHandle, "REPETITION", 1);
                this.gvList.SetRowCellValue(intListHandle, "BINDING_PRESC_TITLE", this.ApiList[0].TOPIC);
                this.gvList.SetRowCellValue(intListHandle, "USAGE", this.ApiList[0].USAGE);

                //计算总费用 
                this.CalcPrescTotalCost(intListHandle);
                //计算总费用

                #endregion 修改主记录
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("点击协定处方时出错!详情" + ex.ToString());
            }
        }

        /// <summary>
        /// 复制处方数据处理
        /// </summary>
        private void CopyPrescData()
        {
            try
            {
                if (this.gvList.FocusedRowHandle < 0) { UIMessageBox.Warn("请添加当前处方！"); return; }

                FrmPrescCopy frmCopy = new FrmPrescCopy(this.PatientInfo);
                frmCopy.CopyResult += this.CopyResult;
                frmCopy.ShowDialog();
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方CopyPrescData方法出错!详情" + ex.ToString());
            }

        }

        /// <summary>
        /// 复制处方
        /// </summary>
        /// <param name="result"></param>
        private void CopyResult(List<DataRow> result)
        {
            try
            {
                if (result == null)
                    return;

                if (result.Count.Equals(0))
                    return;

                // 分限用药检验
                foreach (DataRow r in result)
                {
                    DoctorCheckLimitDrugItem item = new DoctorCheckLimitDrugItem();

                    item.DrugCode = r["DRUG_CODE"].ToString();
                    item.DrugName = r["DRUG_NAME"].ToString();
                    item.Warn = 1;
                    item.PatientInfo = this.PatientInfo;
                    item.OperTypeOne = true;
                    item.OperTypeTwo = true;

                    if (Parameter.LIMIT_DRUG.Equals("1"))
                        Global.DoctorCheckLimitDrug(item);

                    if (Parameter.LIMIT_DRUG.Equals("2"))
                        if (Global.DoctorCheckLimitDrug(item).Equals(-1))
                            return;
                }

                // 若为空处方，设置处方的相关属性
                DataRow curPresc = this.gvList.GetDataRow(this.gvList.FocusedRowHandle);
                if (string.IsNullOrEmpty(curPresc["PRESC_TYPE"].ToString("")))
                {
                    curPresc["PRESC_TYPE"] = result[0]["PRESC_TYPE"];
                    curPresc["REPETITION"] = 1;
                }

                for (int rowNo = 0; rowNo < result.Count; rowNo++)
                {
                    // 在最后行添加新记录
                    int newHandle = this.gvDetail.RowCount - 1;
                    if (newHandle < 0)
                    {
                        newHandle = this.AddNewDrug();
                    }
                    else
                    {
                        string drugCode = this.gvDetail.GetRowCellValue(newHandle, "DRUG_CODE").ToString("");
                        if (!string.IsNullOrEmpty(drugCode))
                            newHandle = this.AddNewDrug();
                    }

                    DataRow r = this.gvDetail.GetDataRow(newHandle);

                    r["DRUG_CODE"] = result[rowNo]["DRUG_CODE"];
                    r["DRUG_SPEC"] = result[rowNo]["DRUG_SPEC"];
                    r["DRUG_NAME"] = result[rowNo]["DRUG_NAME"];
                    r["FIRM_ID"] = result[rowNo]["FIRM_ID"];
                    r["PACKAGE_SPEC"] = result[rowNo]["PACKAGE_SPEC"];
                    r["PACKAGE_UNITS"] = result[rowNo]["PACKAGE_UNITS"];
                    r["QUANTITY"] = result[rowNo]["QUANTITY"];
                    r["COSTS"] = result[rowNo]["COSTS"];
                    r["PAYMENTS"] = result[rowNo]["PAYMENTS"];
                    r["ADMINISTRATION"] = result[rowNo]["ADMINISTRATION"];
                    r["DOSAGE"] = result[rowNo]["DOSAGE"];
                    r["DOSAGE_UNITS"] = result[rowNo]["DOSAGE_UNITS"];
                    r["AMOUNT_PER_PACKAGE"] = result[rowNo]["AMOUNT_PER_PACKAGE"];
                    r["FREQUENCY"] = result[rowNo]["FREQUENCY"];
                    r["DOSAGE_EACH"] = result[rowNo]["DOSAGE_EACH"];
                    r["FREQ_DETAIL"] = result[rowNo]["FREQ_DETAIL"];
                    r["WRITEOFF"] = result[rowNo]["WRITEOFF"];
                    r["PERFORM_TIMES"] = result[rowNo]["PERFORM_TIMES"];
                    r["INSUR_ABIDANCE"] = result[rowNo]["INSUR_ABIDANCE"];
                    r["SPLIT_FLAG"] = result[rowNo]["SPLIT_FLAG"];
                    r["ABIDANCE"] = result[rowNo]["ABIDANCE"];

                    // 处理历史处方复制中 performedBy 为空的情况
                    string dispensary = result[rowNo]["DISPENSARY"]?.ToString()?.Trim() ?? "";
                    if (string.IsNullOrEmpty(dispensary))
                    {
                        // 历史处方的执行科室为空时，使用当前选择的药房
                        string strDispensary = txtDispensary.EditValue?.ToString();
                        if (!string.IsNullOrEmpty(strDispensary))
                        {
                            dispensary = strDispensary;
                        }
                        else
                        {
                            UIMessageBox.Warn("历史处方中药品【" + result[rowNo]["DRUG_NAME"] + "】的执行科室为空，请先选择药房！");
                            return;
                        }
                    }
                    r["DISPENSARY"] = dispensary;

                    r["MAX_DOSAGE"] = result[rowNo]["MAX_DOSAGE"];
                    r["MAX_PRESC_DOSAGE"] = result[rowNo]["MAX_PRESC_DOSAGE"];

                    // 子处方、及组合标志
                    r["ORDER_SUB_NO"] = result[rowNo]["ORDER_SUB_NO"];
                    r["SUB_STRING"] = result[rowNo]["SUB_STRING"];

                    r["FREQ_COUNTER"] = result[rowNo]["FREQ_COUNTER"];
                    r["FREQ_INTERVAL"] = result[rowNo]["FREQ_INTERVAL"];
                    r["FREQ_INTERVAL_UNIT"] = result[rowNo]["FREQ_INTERVAL_UNIT"];
                    r["DOSC_PER_UNIT"] = result[rowNo]["DOSC_PER_UNIT"];
                    r["TOXI_PROPERTY"] = result[rowNo]["TOXI_PROPERTY"];
                    r["ORDER_DRUG_COUNT"] = result[rowNo]["ORDER_DRUG_COUNT"];
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("复制处方时出错!详情" + ex.ToString());
            }

        }

        /// <summary>
        /// 新方处理
        /// </summary>
        /// <returns></returns>
        private int AddNewPresc()
        {
            //liujun add 2019-12-5
            if (boolChange)
            {
                UIMessageBox.Warn("数据已修改，必须先保存才能开新方！");
                return -1;
            }

            gvList.AddNewRow();//同时会触发gvList_FocusedRowChanged事件
            gvList.UpdateCurrentRow();
            int iFocusedRowHandle = gvList.FocusedRowHandle;

            try
            {
                //判断gvDetial是否有修改，有修改则保存
                gvDetail.CloseEditor();
                gvDetail.UpdateCurrentRow();
                //if (util.DataTableHelper.GetDTChangesRowsCount(mdtSaveDetail) > 0 || gvDetail.RowCount == 0)
                //{
                //    UIMessageBox.Warn("数据已修改，必须先保存才能开新方！", "提示");
                //    return;
                //}

                //新增处方时刷新合理用药接口

                //新方
                dtSaveDetail = dtAllDetail.Clone();
                gcDetail.DataSource = dtSaveDetail;

                DataRow drListNew = gvList.GetDataRow(iFocusedRowHandle);
                drListNew["PATIENT_ID"] = PatientInfo.PatientId;
                drListNew["NAME"] = PatientInfo.PatientName;
                drListNew["NAME_PHONETIC"] = PatientInfo.NamePhonetic;
                drListNew["IDENTITY"] = PatientInfo.Identity;
                drListNew["CHARGE_TYPE"] = PatientInfo.ChargeType;
                drListNew["UNIT_IN_CONTRACT"] = PatientInfo.UnitInContract;
                drListNew["BED_LABEL"] = PatientInfo.BedNo;
                drListNew["PREPAYMENTS"] = PatientInfo.Prepayments;
                drListNew["PRESC_DATE"] = DataServerBase.GetServerTime();
                drListNew["PRESC_NO"] = FrmPrescDoctInputSrv.GetPrescNo();
                listPrescInfo.Add(new PrescInfo { PRESC_NO = int.Parse(drListNew["PRESC_NO"].ToString()), PRESC_DATE = DateTime.Parse(drListNew["PRESC_DATE"].ToString()) });
                drListNew["REPETITION"] = 1;
                drListNew["DOCTOR_USER"] = SystemParm.LoginUser.USER_NAME;
                drListNew["PRESCRIBED_BY"] = SystemParm.LoginUser.NAME;//开方医生姓名
                drListNew["ENTERED_BY"] = SystemParm.LoginUser.NAME;//录入人姓名
                drListNew["ORDERED_BY"] = SystemParm.LoginUser.DEPT_CODE;//开单科室代码
                drListNew["PRESC_TYPE"] = 0;// 0处方类型：0-西药、1-草药
                drListNew["PRESC_SOURCE"] = 3;// 急诊留观处方：0-门诊   1-住院   2-其它, 3-留观
                drListNew["PRESC_STATUS"] = 0;
                drListNew["DIAGNOSIS_NAME"] = PatientInfo.Diagnosis; //诊断描述  
                drListNew["CHARGE_INDICATOR"] = 0; //收费标志
                drListNew["HIS_UNIT_CODE"] = SystemParm.HisUnitCode;
                gvList.UpdateCurrentRow();
                gvList.CloseEditor();

                //liujun add 2019-12-5
                boolChange = true;
                this.btSave.Enabled = true;
                this.btAdd.Enabled = true;
                this.btDelete.Enabled = true;
                this.btChildPresc.Enabled = true;
                this.btBindingPresc.Enabled = true;
                this.btCopyPresc.Enabled = true;
                this.btDelPresc.Enabled = true;
                this.gvDetail.OptionsBehavior.Editable = true;

                this.gvList_FocusedRowChanged(this.gvList, new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs(iFocusedRowHandle - 1, iFocusedRowHandle));

                this.AddNewDrug();
                //liujun add 2019-12-5
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方添加新方时出错!详情" + ex.ToString());
            }

            return iFocusedRowHandle;
        }

        /// <summary>
        /// 毁方处理
        /// </summary>
        private async void DestroyPresc()
        {
            try
            {
                int intFocusHandle;
                string strObVisitNo;

                intFocusHandle = this.gvList.FocusedRowHandle;
                if (intFocusHandle < 0)
                {
                    UIMessageBox.Warn("请选择要进行毁方的记录");
                    return;
                }
                DataRow row = this.gvList.GetDataRow(intFocusHandle);

                strObVisitNo = row["OB_VISIT_NO"] == DBNull.Value ? "" : row["OB_VISIT_NO"].ToString().Trim();
                if (string.IsNullOrEmpty(strObVisitNo) || strObVisitNo.Length.Equals(0))
                {
                    if (UIMessageBox.YesNo("确实要毁方吗？") == DialogResult.No)
                        return;

                    await this.BindingDataSourceAsync();
                    boolChange = false;
                    return;

                }

                string strPrescStatus;
                string strChargeIndicator;

                strPrescStatus = row["PRESC_STATUS"] == DBNull.Value ? "0" : row["PRESC_STATUS"].ToString().Trim();
                strChargeIndicator = row["CHARGE_INDICATOR"] == DBNull.Value ? "0" : row["CHARGE_INDICATOR"].ToString().Trim();

                if (strPrescStatus.Equals("1") || strChargeIndicator.Equals("1"))
                {
                    UIMessageBox.Warn("已收费、已发药处方不能毁方！");
                    return;
                }
                else
                {
                    if (UIMessageBox.YesNo("确实要毁方吗？") == DialogResult.No)
                        return;
                    DateTime dateTimePrescDate;
                    string strPrescNo;

                    dateTimePrescDate = (DateTime)row["PRESC_DATE"];
                    strPrescNo = row["PRESC_NO"].ToString().Trim();

                    DataTable[] tables = FrmPrescDoctInputSrv.GetDestroyPresData(strPrescNo, dateTimePrescDate.ToString("yyyy-MM-dd HH:mm:ss"));

                    #region 整理艾隆数据条件
                    OutMedItem item = new OutMedItem();
                    item.VisitDate = this.visitDate;
                    item.VisitNo = this.visitNo;
                    item.PrescDate = dateTimePrescDate.ToString("yyyy-MM-dd HH:mm:ss");
                    item.PrescNo = strPrescNo;
                    #endregion 整理艾隆数据条件

                    //删除费用表
                    if (tables[3].Rows.Count > 0)
                        for (int rowNo = tables[3].Rows.Count - 1; rowNo >= 0; rowNo--)
                            tables[3].Rows[rowNo].Delete();

                    //作废医嘱记录
                    if (tables[2].Rows.Count > 0)
                    {
                        string strOrderText;
                        string strOrderStatus;
                        DateTime dateTimeSysdate = DataServerBase.GetServerTime();
                        for (int rowNo = 0; rowNo < tables[2].Rows.Count; rowNo++)
                        {
                            strOrderText = tables[2].Rows[rowNo]["ORDER_TEXT"] == DBNull.Value ? "" : tables[2].Rows[rowNo]["ORDER_TEXT"].ToString().Trim();
                            strOrderStatus = tables[2].Rows[rowNo]["ORDER_STATUS"] == DBNull.Value ? "" : tables[2].Rows[rowNo]["ORDER_STATUS"].ToString().Trim();
                            if (!strOrderStatus.Equals("4"))
                            {
                                tables[2].Rows[rowNo]["ORDER_STATUS"] = "4";
                                tables[2].Rows[rowNo]["BILLING_ATTR"] = "3";
                                tables[2].Rows[rowNo]["DRUG_BILLING_ATTR"] = "3";
                                tables[2].Rows[rowNo]["CANCEL_DOCTOR"] = SystemParm.LoginUser.USER_NAME;
                                tables[2].Rows[rowNo]["CANCEL_DATE_TIME"] = dateTimeSysdate;
                                //if (strOrderText.IndexOf("作废") >= 0)
                                tables[2].Rows[rowNo]["ORDER_TEXT"] = "作废" + strOrderText;
                            }
                        }
                    }

                    //删除处方明细
                    if (tables[1].Rows.Count > 0)
                        for (int rowNo = tables[1].Rows.Count - 1; rowNo >= 0; rowNo--)
                            tables[1].Rows[rowNo].Delete();
                    DataTable masterdtCopy = new DataTable();
                    //DataRow masterDataRow = null;
                    //删除处方主记录
                    if (tables[0].Rows.Count > 0)
                    {
                        for (int rowNo = tables[0].Rows.Count - 1; rowNo >= 0; rowNo--)
                        {
                            //masterDataRow = tables[0].NewRow();
                            masterdtCopy = tables[0].Copy();
                            //dt.Rows.Add(tables[0].Rows[rowNo].ItemArray[0]);
                            //masterDataRow = tables[0].Rows[rowNo];
                            tables[0].Rows[rowNo].Delete();
                        }
                    }

                    DataTable[] tableList = tables;

                    //#region 处理艾隆数据
                    //if (this.ProcessOutMedHis(item, true))
                    //{
                    //    tableList = new DataTable[5];
                    //    tableList[0] = tables[0];
                    //    tableList[1] = tables[1];
                    //    tableList[2] = tables[2];
                    //    tableList[3] = tables[3];
                    //    tableList[4] = this.mdtOutMedHis;
                    //}
                    //else
                    //{
                    //    tableList = tables;
                    //}
                    //#endregion 处理艾隆数据

                    //if (FrmPrescDoctInputSrv.SaveData(tables) > 0)
                    if (FrmPrescDoctInputSrv.SaveData(tableList) > 0)
                    {
                        //try
                        //{
                        //    if (masterdtCopy.Rows[0] != null)
                        //    {
                        //        // 毁方 也需要推送平台消息  YYS  2020/9/21
                        //        string result = UploadFile("4", masterdtCopy.Rows[0], new byte[1], 1, "D");
                        //    }
                        //}
                        //catch (Exception ex)
                        //{
                        //    UIMessageBox.Err("处方毁方向平台发送数据出错!详情" + ex.ToString());
                        //    Utility.LogFile.WriteLogAuto("毁方向平台发送时出错：详情：" + ex.ToString());
                        //}

                        UIMessageBox.Info("毁方成功");
                        await this.BindingDataSourceAsync();
                        this.gvList_FocusedRowChanged(this.gvList, null);
                    }
                    else
                    {
                        UIMessageBox.Warn("毁方失败");
                    }
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方毁方时出错!详情" + ex.ToString());
            }

        }

        /// <summary>
        /// 计算处理总费用
        /// </summary>
        /// <param name="focusHandle"></param>
        private void CalcPrescTotalCost(int focusHandle)
        {
            if (focusHandle < 0)
                return;


            if (this.gvDetail.RowCount.Equals(0))
            {
                this.gvList.SetRowCellValue(focusHandle, "COSTS", 0);
                this.gvList.SetRowCellValue(focusHandle, "PAYMENTS", 0);
            }
            else
            {
                decimal decSumCost;
                decimal decPayments;

                int intRepetition = this.gvList.GetRowCellValue(focusHandle, "REPETITION") == null ? 0 : this.gvList.GetRowCellValue(focusHandle, "REPETITION").ToString().Trim().Length.Equals(0) ? 0 : int.Parse(this.gvList.GetRowCellValue(focusHandle, "REPETITION").ToString());

                decSumCost = this.gvDetail.Columns["COSTS"].SummaryItem.SummaryValue == null ? 0 : this.gvDetail.Columns["COSTS"].SummaryItem.SummaryValue.ToString().Trim().Length.Equals(0) ? 0 : decimal.Parse(this.gvDetail.Columns["COSTS"].SummaryItem.SummaryValue.ToString().Trim());
                decPayments = this.gvDetail.Columns["PAYMENTS"].SummaryItem.SummaryValue == null ? 0 : this.gvDetail.Columns["PAYMENTS"].SummaryItem.SummaryValue.ToString().Trim().Length.Equals(0) ? 0 : decimal.Parse(this.gvDetail.Columns["PAYMENTS"].SummaryItem.SummaryValue.ToString().Trim());

                this.gvList.SetRowCellValue(focusHandle, "COSTS", decSumCost * intRepetition);
                this.gvList.SetRowCellValue(focusHandle, "PAYMENTS", decPayments * intRepetition);
            }

            this.gvDetail.CloseEditor();
        }

        /// <summary>
        /// 皮试结果
        /// </summary>
        /// <param name="drugCode"></param>
        /// <param name="drugName"></param>
        /// <param name="result"></param>
        /// <returns></returns>
        private int SkinResult(string drugCode, string drugName, ref string result)
        {
            string strSkinTest;
            string strSkinResult;
            strSkinTest = FrmPrescDoctInputSrv.GetDrugSkinInfo(drugCode);
            if (strSkinTest.Equals("1"))
            {
                strSkinResult = FrmPrescDoctInputSrv.GetDrugSkinResult(drugCode, this.PatientInfo);
                if (strSkinResult.Equals("1"))
                {
                    UIMessageBox.Err("药品【" + drugName + "】有皮试阳性记录！");
                    return -1;
                }

                // //修改皮试药品 提示  YYS  2020/8/18
                if (this.gvDetail.RowCount > 0)
                {
                    //皮试药品弹框
                    if (UIMessageBox.YesNo("【" + drugName + "】是皮试药品是否进行皮试?") == System.Windows.Forms.DialogResult.No)
                        result = "0";
                    else
                        result = "1";
                    //DataRow[] rows = this.mdtAllDetail.Select("DRUG_CODE='" + drugCode + "' AND  SKIN_TEST=1");
                    ////此处不对 注释
                    ////if (!rows.Length.Equals(0))
                    ////{
                    ////    if (UIMessageBox.YesNo("【" + drugName + "】是皮试药品是否进行皮试?") == System.Windows.Forms.DialogResult.No)
                    ////        strSkinTest = "0";
                    ////    else
                    ////        strSkinTest = "1";
                    ////}
                    //if (rows.Length == 0 )
                    //{
                    //    //如果之前无此记录 则显示弹框
                    //    if (UIMessageBox.YesNo("【" + drugName + "】是皮试药品是否进行皮试?") == System.Windows.Forms.DialogResult.No)
                    //        result = "0";
                    //    else
                    //        result = "1";
                    //}
                    //else
                    //{
                    //    //有此药品则不显示弹框
                    //    result = "0";
                    //} 
                }
                else
                    result = "0";

            }
            else
                result = "0";
            return 1;
        }

        /// <summary>
        /// 计算Detail费用
        /// </summary>
        private void ComputeDetailCost(DataRow dr)
        {
            decimal decQuantity = decimal.Parse(dr["QUANTITY"].ToString());
            if (decQuantity == 0)
            {
                dr["COSTS"] = 0; //应付费用
                dr["PAYMENTS"] = 0; //实付费用
                return;
            }

            // 取已计算过的项目价格
            decimal itemPrice = dr["ITEM_PRICE"].ToDecimal(0);
            if (itemPrice <= 0)
            {
                // 若原来未记录价格，则重新计算并记录
                string strItemClass = "A";
                string strItemCode = dr["DRUG_CODE"].ToString();
                string strItemSpec = dr["PACKAGE_SPEC"].ToString() + dr["FIRM_ID"].ToString();
                string strUnits = dr["PACKAGE_UNITS"].ToString();
                if (string.IsNullOrEmpty(strItemCode)) return;

                string strCurrentPriceListWhere = "item_class = '" + strItemClass + "' And item_code = '" + strItemCode + "' And Upper(item_spec) = Upper('" + strItemSpec + "') And units = '" + strUnits + "'";
                DataTable dtCurrentPriceList = FrmPrescDoctInputSrv.GetCurrentPriceList(strCurrentPriceListWhere);
                if (dtCurrentPriceList.Rows.Count == 0)
                {
                    UIMessageBox.Warn("没有该项目的价格！");
                    return;
                }

                DataRow drCurrentPriceList = dtCurrentPriceList.Rows[0];
                switch (PatientInfo.ChargePriceIndicator)
                {
                    case 0:
                        itemPrice = decimal.Parse(drCurrentPriceList["PRICE"].ToString());
                        break;
                    case 1:
                        itemPrice = decimal.Parse(drCurrentPriceList["PREFER_PRICE"].ToString());
                        break;
                    case 2:
                        itemPrice = decimal.Parse(drCurrentPriceList["FOREIGNER_PRICE"].ToString());
                        break;
                }
                dr["ITEM_PRICE"] = itemPrice;
            }

            dr["COSTS"] = decQuantity * itemPrice; //应付费用
            dr["PAYMENTS"] = decQuantity * itemPrice; //实付费用
        }

        /// <summary>
        /// //转换药品拆包消耗量作为单次实际使用量
        /// </summary>
        /// <returns></returns>
        private decimal CalcSplitDosageEach(DataRow dr)
        {
            decimal decRetDosageEach = 0;
            if (string.IsNullOrEmpty(dr["DRUG_CODE"].ToString()))
                return decRetDosageEach;
            int iSplitFlag = int.TryParse(dr["SPLIT_FLAG"].ToString(), out iSplitFlag) ? iSplitFlag : 0;
            if (iSplitFlag == 1)//拆包
            {
                decRetDosageEach = decimal.Parse(dr["DOSAGE_EACH"].ToString());//单次剂量
            }
            else//不拆包
            {
                string strDrugCode = dr["DRUG_CODE"].ToString();
                string strPackageSpec = dr["PACKAGE_SPEC"].ToString();
                string strFirmId = dr["FIRM_ID"].ToString();
                string strPackageUnits = dr["PACKAGE_UNITS"].ToString();
                decimal decDosageEach = decimal.TryParse(dr["DOSAGE_EACH"].ToString(), out decDosageEach) ? decDosageEach : 0;
                decimal decDoscPerUnit = decimal.TryParse(dr["DOSC_PER_UNIT"].ToString(), out decDoscPerUnit) ? decDoscPerUnit : 0;//最小单位剂量

                if (decDoscPerUnit.Equals(0))
                {
                    DrugUnitItem item = new DrugUnitItem();
                    item.DrugCode = strDrugCode;
                    item.DrugSpec = strPackageSpec;
                    item.FrmId = strFirmId;
                    item.Units = strPackageUnits;
                    FrmPrescDoctInputSrv.GetDrugUnits(item);
                    decDoscPerUnit = item.DoscPerUnit.Equals(0) ? decDosageEach : item.DoscPerUnit;
                    dr["DOSC_PER_UNIT"] = decDoscPerUnit;
                    dr["AMOUNT_PER_PACKAGE"] = item.AmountPerPackage; ;
                }

                //Math.Ceiling：只要有小数都加1
                decimal decAmount = Math.Ceiling(decDosageEach / (decDoscPerUnit/*==0?1: decDoscPerUnit*/));
                decRetDosageEach = decAmount * decDoscPerUnit;
            }
            return decRetDosageEach;
        }

        /// <summary>
        /// 计算执行次数、天数、医保天数
        /// </summary>
        private void CalcInsurAbidance(DataRow drGvDetail, ref decimal decPerformTimes, ref decimal decAbidance, ref decimal decInsurAbidance)
        {
            decimal decQuantity = decimal.Parse(drGvDetail["QUANTITY"].ToString());//数量

            int iFreqCounter = int.TryParse(drGvDetail["FREQ_COUNTER"].ToString(), out iFreqCounter) ? iFreqCounter : 0;//频率次数
            int iFreqInterval = int.TryParse(drGvDetail["FREQ_INTERVAL"].ToString(), out iFreqInterval) ? iFreqInterval : 0;//频率间隔
            string strFreqIntervalUnits = drGvDetail["FREQ_INTERVAL_UNIT"].ToString();//频率间隔单位

            //转换药品拆包消耗量作为单次实际使用量.单次剂量
            decimal decDosageEach = CalcSplitDosageEach(drGvDetail);

            int iAmountPerPackage = int.TryParse(drGvDetail["AMOUNT_PER_PACKAGE"].ToString(), out iAmountPerPackage) ? iAmountPerPackage : 0;//包装数量
            decimal decDoscPerUnit = decimal.TryParse(drGvDetail["DOSC_PER_UNIT"].ToString(), out decDoscPerUnit) ? decDoscPerUnit : 0;//最小单位剂量

            // 计算总执行次数（向下取整）
            //执行次数=数量*包装数量*最小单位剂量/单次剂量
            //Math.Round四舍五入;Math.Floor向下取整
            if (decDosageEach == 0)
            {
                decPerformTimes = 0;
                //UIMessageBox.Err("单次剂量不为零");
                return;
            }
            else
            {
                decPerformTimes = Math.Floor(Math.Round(decQuantity * iAmountPerPackage * decDoscPerUnit / decDosageEach, 4, MidpointRounding.AwayFromZero));
            }


            // 根据频次信息，计算用药天数
            if (string.IsNullOrEmpty(strFreqIntervalUnits))//
            {
                decAbidance = 1;
                decInsurAbidance = 1;
            }
            else
            {
                if (iFreqCounter == 0 || iFreqInterval == 0)
                {
                    decAbidance = 1;
                    decInsurAbidance = 1;
                }
                else
                {
                    decimal decDays = 0;
                    switch (strFreqIntervalUnits)
                    {
                        case "周":
                            //一天多少次=iFreqCounter/(iFreqInterval*7)
                            //decPerformTimes次多少天=decPerformTimes/(iFreqCounter/(iFreqInterval*7))
                            decDays = decPerformTimes * iFreqInterval * 7 / iFreqCounter;
                            decInsurAbidance = Math.Floor(Math.Round(((decQuantity * iAmountPerPackage * decDoscPerUnit / decDosageEach) * iFreqInterval * 7) / iFreqCounter, 4, MidpointRounding.AwayFromZero));
                            break;
                        case "日":
                            decDays = decPerformTimes * iFreqInterval / iFreqCounter;
                            decInsurAbidance = Math.Floor(Math.Round(((decQuantity * iAmountPerPackage * decDoscPerUnit / decDosageEach) * iFreqInterval) / iFreqCounter, 4, MidpointRounding.AwayFromZero));
                            break;
                        case "小时":
                            decDays = (decPerformTimes * iFreqInterval) / (iFreqCounter * 24);
                            decInsurAbidance = Math.Floor(Math.Round(((decQuantity * iAmountPerPackage * decDoscPerUnit / decDosageEach) * iFreqInterval) / (iFreqCounter * 24), 4, MidpointRounding.AwayFromZero));
                            break;
                        case "月":
                            decDays = (decPerformTimes * 30 * iFreqInterval) / iFreqCounter;
                            decInsurAbidance = Math.Floor(Math.Round(((decQuantity * iAmountPerPackage * decDoscPerUnit / decDosageEach) * 30 * iFreqInterval) / iFreqCounter, 4, MidpointRounding.AwayFromZero));
                            break;
                    }
                    // 考虑到隔日、间隔用药的情况，需要将频次系数重新整理：小于1时取1，否则向上取整
                    decAbidance = Math.Ceiling(Math.Round(decDays, 4, MidpointRounding.AwayFromZero));
                }

            }
        }

        /// <summary>
        /// 刷新Master费用
        /// </summary>
        /// <param name="gvDetail"></param>
        /// <returns></returns>
        private void RefreshMasterCost(DevExpress.XtraGrid.Views.Grid.GridView gvDetail)
        {
            gvDetail.UpdateCurrentRow();
            string strDetailSumPayments = gvDetail.Columns["PAYMENTS"].SummaryItem.SummaryValue.ToString();
            string strDetailSumCosts = gvDetail.Columns["COSTS"].SummaryItem.SummaryValue.ToString();

            txtPayments.EditValue = strDetailSumPayments;
            txtCosts.EditValue = strDetailSumCosts;

            DataRow drList = gvList.GetDataRow(gvList.FocusedRowHandle);
            drList["PAYMENTS"] = decimal.Parse(strDetailSumPayments);
            drList["COSTS"] = decimal.Parse(strDetailSumCosts);
        }

        /// <summary>
        /// 艾隆接口处理
        /// </summary>
        /// <param name="item">艾隆数据查询条件</param>
        /// <param name="isDel">true:毁方;</param>
        /// <returns>true艾隆接口数据又修改</returns>
        private bool ProcessOutMedHis(OutMedItem item, bool isDel)
        {
            bool isChange = false;
            try
            {
                this.dtOutMedHis = FrmPrescDoctInputSrv.GetOutMedTableHisDetail(item);

                if (this.dtOutMedHis.Rows.Count > 0)
                {
                    foreach (DataRow row in this.dtOutMedHis.Rows)
                    {
                        row.Delete();
                    }

                    isChange = true;
                }
                if (!isDel)
                {
                    if (this.dtSaveDetail.Rows.Count > 0)
                    {
                        foreach (DataRow row in this.dtSaveDetail.Rows)
                        {
                            if (row.RowState.Equals(DataRowState.Deleted))
                                continue;
                            List<DrugPrescDetailItem> list = new List<DrugPrescDetailItem>();
                            DataTableLIstHelp.DataRowsToList<DrugPrescDetailItem>(list, new DataRow[] { row });
                            string durgSpec = FrmPrescDoctInputSrv.GetDrugSpec(list[0].DRUG_CODE.ToString(""), list[0].FIRM_ID.ToString(""), list[0].DRUG_SPEC.ToString(""));
                            string age = Global.GetChineseAge(this.PatientInfo.BirthOfDate);

                            DataRow newRow = this.dtOutMedHis.NewRow();
                            newRow["PRESNO"] = list[0].PRESC_NO.ToString("");
                            newRow["MEDID"] = list[0].ITEM_NO;
                            newRow["MEDONLYCODE"] = list[0].DRUG_CODE;
                            newRow["MEDAMT"] = list[0].QUANTITY;
                            newRow["MEDPACK"] = list[0].PACKAGE_UNITS.ToString("");
                            newRow["MEDUNITPACK"] = durgSpec;
                            newRow["MEDUNIT"] = list[0].DRUG_SPEC;
                            //newRow["MEDOUTTIME"] =
                            //newRow["PRESTYPE"] =
                            //newRow["ISPAY"] =
                            newRow["PATIENT_ID"] = this.PatientInfo.PatientId;
                            newRow["PATIENTNAME"] = this.PatientInfo.PatientName;
                            newRow["PATIENTSEX"] = this.PatientInfo.Sex;
                            //截取前三位  因为包含岁字 占用两位
                            newRow["PATIENTAGE"] = age.Trim().Substring(0, 3).ToString();
                            newRow["PATIENTBIRTH"] = this.PatientInfo.BirthOfDate;
                            //newRow["DIAGNOSIS"] =要核对数据
                            newRow["WARDNO"] = SystemParm.LoginUser.DEPT_CODE;
                            newRow["WARDNAME"] = PlatCommon.SysBase.SystemParm.LoginUser.DEPT_NAME;
                            newRow["MEDPERDOS"] = list[0].DOSAGE.ToString("");
                            newRow["MEDUSAGE"] = list[0].ADMINISTRATION.ToString("");
                            newRow["MEDPERDAY"] = list[0].FREQUENCY.ToString("");
                            newRow["DOCTORNAME"] = SystemParm.LoginUser.USER_NAME;
                            newRow["FPNO"] =
                            newRow["SENDFLAG"] = "0";
                            newRow["MEDUNITPRICE"] = list[0].COSTS;
                            //newRow["MEDTOTALPRICE"] =
                            //newRow["MEDTYPE"] =
                            //newRow["MEDCONVERCOF"] =
                            newRow["REMARK"] = list[0].FREQ_DETAIL.ToString("");
                            newRow["YFCODE"] = "1";
                            newRow["MEDNAME"] = list[0].DRUG_NAME.ToString("");
                            newRow["MEDFACTORY"] = list[0].FIRM_ID.ToString("");
                            //newRow["PAYSTATUS"] =
                            //newRow["WINDOWNO"] =
                            //newRow["UPDATETIME"] =
                            //newRow["ISREAD"] =
                            //newRow["FPNO1"] =
                            newRow["GROUPNO"] = 0;
                            //如果VISIT_DATE为空  则存入当前日期 不然报错
                            newRow["VISIT_DATE"] = string.IsNullOrEmpty(this.visitDate) ? new NM_Service.NMService.ServerPublicClient().GetSysDate().ToShortDateString() : this.visitDate;
                            newRow["VISIT_NO"] = this.visitNo.ToInt(0);
                            newRow["CLINIC_SERIAL_NO"] = list[0].CLINIC_SERIAL_NO.ToString("");
                            newRow["PRESC_DATE"] = list[0].PRESC_DATE;
                            newRow["DISPENSARY"] = this.txtDispensary.EditValue.ToString("");
                            newRow["SUB_ORDER_NO"] = list[0].ORDER_SUB_NO;


                            this.dtOutMedHis.Rows.Add(newRow);
                        }
                        isChange = true;
                    }
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方艾隆接口表保存出错!详情" + ex.ToString());
            }
            return isChange;
        }

        /// <summary>
        /// 美康合理用药:药物信息查询、浮动窗口、用药研究、警告和审查功能
        /// </summary>
        /// <param name="model">1：住院医生站保存自动审查；
        /// 		            2：住院医生站提交自动审查；
        /// 		            3：手工显示审查界面；
        /// 		            4：临床药学单病人审查；
        /// 		            5：临床药学多病人审查；
        /// 		            6：查看单药警告；
        /// 		            7：手工审查不显示审查界面；
        /// 		            33：门诊医生站保存自动审查（老版本中为1，即：门诊、住院统一）；
        /// 		            34：门诊医生站提交自动审查（老版本中为2，即：门诊、住院统一）；
        /// 		            110：右键菜单有效性控制标识；
        /// 		            401：显示药品浮动窗口；
        /// 		            402：关闭所有浮动窗口；
        /// 		            403：显示单药最近一次审查结果浮动提示窗口；</param>
        /// <param name="handle">行号</param>
        private int PassQueryOrCheck(int model, int handle)
        {
            DateTime sysDateTime = DataServerBase.GetServerTime();
            if (sysDateTime == null)
            {
                UIMessageBox.Err("取系统时间失败,无法继续合理用药审查!");
                return -1;
            }

            try
            {
                string drugCode = string.Empty;
                string drugName = string.Empty;
                string packageSpec = string.Empty;
                string frmId = string.Empty;

                switch (model)
                {
                    case 51:
                        drugCode = this.gvDetail.GetRowCellValue(handle, "DRUG_CODE").ToString("");
                        drugName = this.gvDetail.GetRowCellValue(handle, "DRUG_NAME").ToString("");
                        packageSpec = this.gvDetail.GetRowCellValue(handle, "PACKAGE_SPEC").ToString("");
                        frmId = this.gvDetail.GetRowCellValue(handle, "FIRM_ID").ToString("");

                        Tjhis.Oboutp.Station.Comm.ReadCard.CardReadAPI.MDC_DoSetDrug(drugCode + "_" + packageSpec + "_" + frmId, drugName);

                        Tjhis.Oboutp.Station.Comm.ReadCard.CardReadAPI.MDC_DoRefDrug(model);
                        break;
                    case 33:

                        for (int rowNo = 0; rowNo < this.gvDetail.RowCount; rowNo++)
                        {
                            string strRow = rowNo.ToString();
                            int orderNo = this.gvDetail.GetRowCellValue(rowNo, "ORDER_NO").ToInt();
                            drugCode = this.gvDetail.GetRowCellValue(rowNo, "DRUG_CODE").ToString("");
                            drugName = this.gvDetail.GetRowCellValue(rowNo, "DRUG_NAME").ToString("");
                            packageSpec = this.gvDetail.GetRowCellValue(rowNo, "PACKAGE_SPEC").ToString("");
                            frmId = this.gvDetail.GetRowCellValue(rowNo, "FIRM_ID").ToString("");
                            string dosageEach = this.gvDetail.GetRowCellValue(rowNo, "DOSAGE_EACH").ToString("");
                            string dosageUnits = this.gvDetail.GetRowCellValue(rowNo, "DOSAGE_UNITS").ToString("");
                            string frequency = this.gvDetail.GetRowCellValue(rowNo, "FREQUENCY").ToString("");
                            string administration = this.gvDetail.GetRowCellValue(rowNo, "ADMINISTRATION").ToString("");
                            string pcStartTime = sysDateTime.ToString("yyyy-MM-dd HH:mm:ss");
                            string pcEndTime = "";
                            string pcExecuTime = pcStartTime;
                            string pcGroupTag = orderNo.ToString();
                            string pcIsTempDrug = "0";
                            string pcOrderType = "0";
                            string pcDeptCode = SystemParm.LoginUser.DEPT_CODE;
                            string pcDeptName = PlatCommon.SysBase.SystemParm.LoginUser.DEPT_NAME;
                            string pcDoctorCode = SystemParm.LoginUser.USER_NAME;
                            string pcDoctorName = SystemParm.LoginUser.NAME;
                            string pcPrescNo = this.gvDetail.GetRowCellValue(rowNo, "PRESC_NO").ToString("");
                            string pcNum = this.gvDetail.GetRowCellValue(rowNo, "QUANTITY").ToString("0");
                            string pcNumUnit = this.gvDetail.GetRowCellValue(rowNo, "PACKAGE_UNITS").ToString("");
                            string pcPurpose = "";
                            string pcOprCode = "";
                            string pcMediTime = "";
                            string pcRemark = this.gvDetail.GetRowCellValue(rowNo, "FREQ_DETAIL").ToString("");

                            Tjhis.Oboutp.Station.Comm.ReadCard.CardReadAPI.MDC_AddScreenDrug(rowNo.ToString(), orderNo,
                                              drugCode + "_" + packageSpec + "_" + frmId,
                                              drugName,
                                              dosageEach,
                                              dosageUnits,
                                              frequency,
                                              administration,
                                              administration,
                                              pcStartTime,
                                              pcEndTime,
                                              pcExecuTime,
                                              pcGroupTag,
                                              pcIsTempDrug,
                                              pcOrderType,
                                              pcDeptCode,
                                              pcDeptName,
                                              pcDoctorCode,
                                              pcDoctorName,
                                              pcPrescNo,
                                              pcNum,
                                              pcNumUnit,
                                              pcPurpose,
                                              pcOprCode,
                                              pcMediTime,
                                              pcRemark);


                        }

                        Tjhis.Oboutp.Station.Comm.ReadCard.CardReadAPI.MDC_DoCheck(1, 1);

                        for (int rowNo = 0; rowNo < this.gvDetail.RowCount; rowNo++)
                        {
                            int warningStatus = Tjhis.Oboutp.Station.Comm.ReadCard.CardReadAPI.MDC_GetWarningCode(rowNo.ToString());
                            if (warningStatus >= 0)
                                warningStatus += 1;
                            if (warningStatus.Equals(1))//蓝色
                            {
                                this.gvDetail.SetRowCellValue(rowNo, "NWARN", warningStatus);
                            }
                            else if (warningStatus.Equals(2)) //黑色
                            {
                                this.gvDetail.SetRowCellValue(rowNo, "NWARN", 4);
                                if (Parameter.OB_PASS_WARNING_ENAB.Equals("0"))
                                {
                                    if (UIMessageBox.YesNo("PASS系统审查出存在黑灯用药，是否继续保存医嘱?").Equals(DialogResult.No))
                                        return -1;
                                }
                                else
                                {
                                    UIMessageBox.Warn("PASS系统审查出存在黑灯用药，不能保存医嘱!");
                                    return -1;
                                }
                            }
                            else if (warningStatus.Equals(3))//红色
                            {
                                this.gvDetail.SetRowCellValue(rowNo, "NWARN", warningStatus);
                            }
                            else if (warningStatus.Equals(4))//橙色
                            {
                                this.gvDetail.SetRowCellValue(rowNo, "NWARN", 5);
                            }
                            else if (warningStatus.Equals(5)) //黄色
                            {
                                this.gvDetail.SetRowCellValue(rowNo, "NWARN", 2);
                            }

                        }
                        break;
                    case 6:
                        Tjhis.Oboutp.Station.Comm.ReadCard.CardReadAPI.MDC_ShowWarningHint(handle.ToString());
                        break;
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方合理用药PassQueryOrCheck出错!详情" + ex.ToString());
            }
            return 0;
        }

        /// <summary>
        /// 右键单击药品行(美康接口已开)
        /// </summary>
        /// <param name="rowHandle"></param>
        private void ClickedAndCheck(int rowHandle)
        {
            if (rowHandle < 0) return;
            string drugName = this.gvDetail.GetRowCellValue(rowHandle, "DRUG_NAME").ToString();

            if (Global.StringIsNull(drugName)) return;
            this.PassQueryOrCheck(51, rowHandle);
        }

        /// <summary>
        /// 离观患者不可操作 只能READ
        /// </summary>
        private void ControlReadOnly()
        {
            if (mbControlReadOnly == false)
            {
                return;
            }
            gvList.OptionsBehavior.Editable = false;
            txtPatientId.Enabled = false;
            txtBedLabel.Enabled = false;
            txtName.Enabled = false;
            txtIdentity.Enabled = false;
            txtChargeType.Enabled = false;
            txtUnitInContract.Enabled = false;
            txtDiagnosisName.Enabled = false;
            txtPrescNo.Enabled = false;
            txtDispensary.Enabled = false;
            txtPrepayments.ReadOnly = false;
            txtCosts.Enabled = false;
            txtPayments.Enabled = false;
            gcDetail.Enabled = false;
            btAdd.Enabled = false;
            btDelete.Enabled = false;
            btChildPresc.Enabled = false;
            btNewPresc.Enabled = false;
            btBindingPresc.Enabled = false;
            btCopyPresc.Enabled = false;
            btDelPresc.Enabled = false;
            btRefresh.Enabled = false;
            btSave.Enabled = false;
            btPrint.Enabled = false;
            simpleButtonCharge.Enabled = false;
        }
        #endregion 方法

        #region 事件
        /// <summary>
        /// 关闭处方界面
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public void FrmPrescDoctInput_FormClosing(object sender, FormClosingEventArgs e)
        {
            //if (this.DataChange())
            //{
            //    if (UIMessageBox.YesNo("处方有未保存的数据,是否关闭?") == DialogResult.No)
            //    {
            //        e.Cancel = true;
            //    }
            //}
        }


        public bool DataChange()
        {
            bool isChange = false;
            if (dtSaveDetail == null)
                return isChange;
            dtSaveDetail.TableName = "OB_DRUG_PRESC_DETAIL";
            if (DataTableHelper.GetDTChangesRowsCount(dtSaveDetail) > 0)
            {
                if (dtSaveDetail.Rows.Count == 1 && this.dtSaveDetail.Rows[0].RowState == DataRowState.Added && CommonMethod.StringIsNull(this.dtSaveDetail.Rows[0]["DRUG_CODE"].ToString("")))
                {
                    isChange = false;
                }
                else
                {
                    isChange = true;
                }

            }

            return isChange;
        }
        /// <summary>
        /// 选择上面的处方显示相应的明细事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gvList_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            try
            {
                int iFocusedRowHandle = gvList.FocusedRowHandle;
                if (iFocusedRowHandle < 0)
                {
                    return;
                }
                txtBedLabel.EditValue = PatientInfo.BedNo;

                //if (iFocusedRowHandle == miSelectedListRowHandle)
                if (iFocusedRowHandle != 0 && iFocusedRowHandle == miSelectedListRowHandle) //**********
                {
                    return;
                }

                //判断gvDetial是否有修改，有修改则保存.新增处方不需要此判断
                gvDetail.CloseEditor();
                gvDetail.UpdateCurrentRow();
                if (gvList.GetDataRow(iFocusedRowHandle).RowState == DataRowState.Added)
                {
                    txtDispensary.Enabled = true;
                    //return;
                }
                else
                {
                    dtSaveDetail.TableName = "OB_DRUG_PRESC_DETAIL";
                    if (DataTableHelper.GetDTChangesRowsCount(dtSaveDetail) > 0)
                    {
                        if (dtSaveDetail.Rows.Count == 1 && this.dtSaveDetail.Rows[0].RowState == DataRowState.Added && CommonMethod.StringIsNull(this.dtSaveDetail.Rows[0]["DRUG_CODE"].ToString("")))
                        {

                        }
                        else
                        {
                            gvList.FocusedRowHandle = miSelectedListRowHandle;
                            UIMessageBox.Warn("数据已修改，必须先保存！");
                            return;
                        }

                    }
                    //txtDispensary.Enabled = false;
                }

                DataRow row = this.gvList.GetFocusedDataRow();
                string strPrescNo;

                strPrescNo = row["PRESC_NO"] == DBNull.Value ? "" : row["PRESC_NO"] == null ? "" : row["PRESC_NO"].ToString().Trim();
                if (strPrescNo.Length.Equals(0))
                    return;

                //msSelectedPrescNo = int.Parse(strPrescNo);//==20200624记录选中处方号

                List<PrescMasterItem> list = new List<PrescMasterItem>();
                DataTableLIstHelp.DataRowsToList(list, new DataRow[] { row });
                //选择上面的处方显示相应的明细
                //SelectedListRefreshMasterDetail(iFocusedRowHandle);

                SelectedListRefreshMasterDetail(list[0]);

                //已收费、已发药则不允许修改该处方
                NotAllowModifyPresc(iFocusedRowHandle);

                //记录选中的行
                miSelectedListRowHandle = iFocusedRowHandle;


                if (this.gvDetail.RowCount > 0)
                {
                    this.gvDetail.FocusedRowHandle = this.gvDetail.RowCount - 1;
                    this.gvDetail.SelectRow(this.gvDetail.FocusedRowHandle);
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("选择处方显示药品明细出错!详情" + ex.ToString());
            }
        }

        /// F9字典
        /// </summary>
        /// <param name="result"></param>
        private void InputControl1_deInputResult(InputResult result)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(result.ItemCode))
                    return;

                int iFocusedRowHandle = gvDetail.FocusedRowHandle;

                //最小单位规格,厂商标识
                string strsql = "a.drug_code='" + result.ItemCode + "' and a.drug_spec||a.firm_id='" + result.ItemSpec + "' and b.his_unit_code = '" + SystemParm.HisUnitCode + "' and (b.stop_date Is Null Or b.stop_date > sysdate) and b.start_date < sysdate";
                DataTable dtDrugPriceList = FrmPrescDoctInputSrv.GetDrugPriceList(strsql);
                if (dtDrugPriceList.Rows.Count != 1)
                {
                    UIMessageBox.Warn("提取药品数据出错！");
                    return;
                }
                DataRow drDrugPriceList = dtDrugPriceList.Rows[0];
                string strFirmId = drDrugPriceList["FIRM_ID"].ToString();//厂家标识
                string strMinSpec = drDrugPriceList["MIN_SPEC"].ToString();//最小单位规格
                string strPackageSpec = drDrugPriceList["DRUG_SPEC"].ToString();//包装规格
                string strPackageUnits = drDrugPriceList["UNITS"].ToString();//包装单位
                int iAmountPerPackage = int.Parse(drDrugPriceList["AMOUNT_PER_PACKAGE"].ToString());//包装数量

                // liujun add 2019-12-05
                string strSkinResult = string.Empty;
                strSkinResult = "0";
                //校验是否需要皮试
                if (result.ItemClass.Equals("A"))
                {
                    if (this.SkinResult(result.ItemCode, result.ItemName, ref strSkinResult).Equals(-1))
                        return;

                    //修改皮试药品 提示  默认途径为皮试  YYS  2020/8/26
                    this.gvDetail.SetRowCellValue(iFocusedRowHandle, "SKIN_TEST", strSkinResult);
                    if (strSkinResult == "1")
                    {
                        //皮试药品 默认途径为皮试
                        this.gvDetail.SetRowCellValue(iFocusedRowHandle, "ADMINISTRATION", Parameter.SKIN_ADMINISTRATION);
                    }

                }
                else
                {
                    this.gvDetail.SetRowCellValue(iFocusedRowHandle, "SKIN_TEST", strSkinResult);
                }

                //医生分线用药
                DoctorCheckLimitDrugItem item = new DoctorCheckLimitDrugItem();

                item.DrugCode = result.ItemCode;
                item.DrugName = result.ItemName;
                item.Warn = 1;
                item.PatientInfo = this.PatientInfo;
                item.OperTypeOne = true;
                item.OperTypeTwo = true;
                if (Parameter.LIMIT_DRUG.Equals("1"))
                    Global.DoctorCheckLimitDrug(item);

                if (Parameter.LIMIT_DRUG.Equals("2"))
                    if (Global.DoctorCheckLimitDrug(item).Equals(-1))
                        return;

                //公费用药提示。
                if (Global.CheckChargeType(this.PatientInfo.ChargeType).Equals(-1))
                {
                    CheckOfficialCataLogItem offItem = new CheckOfficialCataLogItem();
                    offItem.ItemClass = result.ItemClass;
                    offItem.ItemCode = result.ItemCode;
                    offItem.ItemSpec = result.ItemSpec;
                    offItem.ItemType = "inp";
                    Global.CheckOfficialCatalog(ref offItem);
                    if (offItem.ReturnValue > 0)
                    {
                        if (Global.OfficialRemind.Equals("1"))
                        {
                            if (UIMessageBox.YesNo(this.PatientInfo.ChargeType + ";" + offItem.ItemMsg) == DialogResult.No)
                                this.gvDetail.SetRowCellValue(iFocusedRowHandle, "WRITEOFF", 1);
                        }
                        else
                            this.gvDetail.SetRowCellValue(iFocusedRowHandle, "WRITEOFF", 1);
                    }
                }

                // liujun add 2019-12-05
                gvDetail.SetRowCellValue(iFocusedRowHandle, "DRUG_NAME", result.ItemName);
                gvDetail.SetRowCellValue(iFocusedRowHandle, "DRUG_CODE", result.ItemCode);

                gvDetail.SetRowCellValue(iFocusedRowHandle, "FIRM_ID", strFirmId);//厂商标识
                gvDetail.SetRowCellValue(iFocusedRowHandle, "DRUG_SPEC", strMinSpec);//最小单位规格
                gvDetail.SetRowCellValue(iFocusedRowHandle, "PACKAGE_SPEC", strPackageSpec);//包装规格
                gvDetail.SetRowCellValue(iFocusedRowHandle, "PACKAGE_UNITS", strPackageUnits);//包装单位
                gvDetail.SetRowCellValue(iFocusedRowHandle, "AMOUNT_PER_PACKAGE", iAmountPerPackage);//包装数量


                gvDetail.SetRowCellValue(iFocusedRowHandle, "DOSAGE_UNITS", result.ItemUnit);//剂量单位

                // 检查并确保执行科室不为空，避免处方发药系统无法正常发药
                string performedDept = result.Performed_dept;
                if (string.IsNullOrEmpty(performedDept))
                {
                    // 获取当前选择的药房作为默认执行科室
                    string strDispensary = txtDispensary.EditValue?.ToString();
                    if (!string.IsNullOrEmpty(strDispensary))
                    {
                        performedDept = strDispensary;
                        result.Performed_dept = strDispensary; // 同时更新result对象
                    }
                    else
                    {
                        UIMessageBox.Warn("请先选择药房！");
                        return;
                    }
                }

                gvDetail.SetRowCellValue(iFocusedRowHandle, "DISPENSARY", performedDept.Trim());
                gvDetail.SetRowCellValue(iFocusedRowHandle, "COSTS", 0);//费用
                gvDetail.SetRowCellValue(iFocusedRowHandle, "PAYMENTS", 0);//实付费用
                gvDetail.SetRowCellValue(iFocusedRowHandle, "DOSC_PER_UNIT", result.ItemQuantity);//最小单位剂量
                gvDetail.SetRowCellValue(iFocusedRowHandle, "DOSAGE", 0);

                // 清空新录入药品的数量、剂量
                gvDetail.GetDataRow(iFocusedRowHandle)["DOSAGE_EACH"] = DBNull.Value;
                gvDetail.GetDataRow(iFocusedRowHandle)["QUANTITY"] = DBNull.Value;

                //毒理分类
                string strToxiProperty = GetToxiProperty(iFocusedRowHandle);
                gvDetail.SetRowCellValue(iFocusedRowHandle, "TOXI_PROPERTY", strToxiProperty);

                gvDetail.FocusedColumn = gridColumn15;//单次剂量
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方F9读取显示药品数据时出错!详情" + ex.ToString());
            }
        }

        /// <summary>
        /// 选择药房
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void txtDispensary_EditValueChanged(object sender, EventArgs e)
        {
            //F9 按药房过滤
            string strDispensary = txtDispensary.EditValue.ToString();
            string filterSql = "STORAGE = '" + strDispensary + "'";

            this.inputPrescText.SetInputCondition(filterSql);
        }

        /// <summary>
        /// 新方
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btNewPresc_Click(object sender, EventArgs e)
        {
            this.AddNewPresc();

            //==20200624新增，新方默认急诊西药房
            DataRow drDefaultDispensary = dtDrugStore.AsEnumerable().FirstOrDefault(r => r["STORAGE_NAME"].ToString().Contains("急诊"));
            if (drDefaultDispensary != null)
            {
                txtDispensary.EditValue = drDefaultDispensary["STORAGE_CODE"].ToString();
            }
            //try
            //{
            //    //判断gvDetial是否有修改，有修改则保存
            //    gvDetail.CloseEditor();
            //    gvDetail.UpdateCurrentRow();
            //    //if (util.DataTableHelper.GetDTChangesRowsCount(mdtSaveDetail) > 0 || gvDetail.RowCount == 0)
            //    //{
            //    //    UIMessageBox.Warn("数据已修改，必须先保存才能开新方！", "提示");
            //    //    return;
            //    //}

            //    //新增处方时刷新合理用药接口

            //    //新方
            //    //gvList
            //    gvList.AddNewRow();
            //    int iFocusedRowHandle = gvList.RowCount - 1;
            //    gvList.FocusedRowHandle = iFocusedRowHandle;

            //    DataRow drListNew = gvList.GetDataRow(iFocusedRowHandle);
            //    drListNew["PATIENT_ID"] = PatientInfo.PatientId;
            //    drListNew["NAME"] = PatientInfo.PatientName;
            //    drListNew["NAME_PHONETIC"] = PatientInfo.NamePhonetic;
            //    drListNew["IDENTITY"] = PatientInfo.Identity;
            //    drListNew["CHARGE_TYPE"] = PatientInfo.ChargeType;
            //    drListNew["UNIT_IN_CONTRACT"] = PatientInfo.UnitInContract;
            //    drListNew["BED_LABEL"] = PatientInfo.BedLabel;
            //    drListNew["PREPAYMENTS"] = PatientInfo.Prepayments;
            //    drListNew["PRESC_DATE"] = DataServerBase.GetServerTime();
            //    drListNew["PRESC_NO"] = FrmPrescDoctInputSrv.GetPrescNo();
            //    listPrescInfo.Add(new PrescInfo { PRESC_NO = drListNew["PRESC_NO"].ToString(), PRESC_DATE = drListNew["PRESC_DATE"].ToString() });
            //    drListNew["REPETITION"] = 1;
            //    drListNew["DOCTOR_USER"] = SystemParm.LoginUser.USER_NAME;
            //    drListNew["PRESCRIBED_BY"] = SystemParm.LoginUser.NAME;//开方医生姓名
            //    drListNew["ENTERED_BY"] = SystemParm.LoginUser.NAME;//录入人姓名
            //    drListNew["ORDERED_BY"] = SystemParm.LoginUser.DEPT_CODE;//开单科室代码
            //    drListNew["PRESC_TYPE"] = 0;// 0处方类型：0-西药、1-草药
            //    drListNew["PRESC_SOURCE"] = 3;// 急诊留观处方：0-门诊   1-住院   2-其它, 3-留观
            //    drListNew["PRESC_STATUS"] = 0;
            //    drListNew["DIAGNOSIS_NAME"] = PatientInfo.Diagnosis; //诊断描述    
            //}
            //catch (Exception ex)
            //{
            //    UIMessageBox.Warn(ex.ToString());
            //}
        }

        /// <summary>
        /// 加药
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btAdd_Click(object sender, EventArgs e)
        {
            try
            {
                //上面无新方 增加加药报错  YYS 2020/8/18
                if (this.gvList.FocusedRowHandle.ToInt() < -100)
                {
                    UIMessageBox.Warn("请先新增新方！");
                    return;
                }

                // 已收费处方不能添加、删除
                //gvList_FocusedRowChanged中已处理
                //liujun add 2019-12-05 FocusedRowChanged会有不触发的情况，所以要进行检查
                if (this.NotAllowModifyPresc(this.gvList.FocusedRowHandle).Equals(-1))
                {
                    UIMessageBox.Warn("处方已收费！");
                    return;
                }
                //liujun add 2019-12-05  FocusedRowChanged会有不触发的情况，所以要进行检查

                // 已保存过处方，验证西药处方不能超过5个药品
                int iListFocusedRowHandle = gvList.FocusedRowHandle;
                DataRow drList = gvList.GetDataRow(iListFocusedRowHandle);
                string strObVisitNo = drList["OB_VISIT_NO"].ToString();
                if (!string.IsNullOrEmpty(strObVisitNo))
                {
                    //不能超过5个处方  YYS 2020/8/18
                    if (miPrescMaxQuantity > 0 && gvDetail.RowCount >= miPrescMaxQuantity)
                    {
                        UIMessageBox.Warn("西药单张处方最多能开【" + miPrescMaxQuantity.ToString() + "个药品，请注意！】");
                        return;
                    }
                }

                //药局
                string strDispensary = txtDispensary.EditValue == null ? "" : txtDispensary.EditValue.ToString();
                if (string.IsNullOrEmpty(strDispensary))
                {
                    UIMessageBox.Warn("请选择药局!");
                    return;
                }

                //为处方添加新药
                AddNewDrug();
                this.gvDetail.FocusedColumn = gvDetail.Columns["DRUG_NAME"];
                SendKeys.Send("{F9}");
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方加药时出错!详情" + ex.ToString());
            }

        }

        /// <summary>
        /// 减药
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (gvList.FocusedRowHandle < 0) return;
                if (gvDetail.RowCount.Equals(0)) return;

                // 已收费处方不能添加、删除
                //gvList_FocusedRowChanged中已处理
                //liujun add 2019-12-05 FocusedRowChanged会有不触发的情况，所以要进行检查
                if (this.NotAllowModifyPresc(this.gvList.FocusedRowHandle).Equals(-1))
                {
                    UIMessageBox.Warn("处方已收费！");
                    return;
                }
                //liujun add 2019-12-05  FocusedRowChanged会有不触发的情况，所以要进行检查   

                int iDetailFocusedRowHandle = gvDetail.FocusedRowHandle;
                if (iDetailFocusedRowHandle < 0)
                {
                    UIMessageBox.Warn("请选择要删除的行...!");
                    return;
                }

                //复合处方，如果删除的是主处方则连同子处方一起删除
                DataRow drDetail = gvDetail.GetDataRow(iDetailFocusedRowHandle);
                drDetail["DELETE_FLAG"] = 1;
                if (int.Parse(drDetail["ORDER_SUB_NO"].ToString()) == 1)
                {
                    for (int i = iDetailFocusedRowHandle + 1; i < gvDetail.RowCount; i++)
                    {
                        DataRow dr = gvDetail.GetDataRow(i);
                        if (int.Parse(dr["ORDER_SUB_NO"].ToString()) == 1)
                        {
                            break;//退出循环
                        }
                        dr["DELETE_FLAG"] = 1;//打上删除标记
                    }
                }

                //删除
                for (int i = gvDetail.RowCount - 1; i >= 0; i--)
                {
                    int iDeleteFlag = 0;
                    if ((int.TryParse(gvDetail.GetRowCellValue(i, "DELETE_FLAG").ToString(), out iDeleteFlag) ? iDeleteFlag : 0) == 1)
                    {
                        gvDetail.DeleteRow(i);
                    }
                }
                this.CalcPrescTotalCost(gvList.FocusedRowHandle);
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方减药时出错!详情" + ex.ToString());
            }
        }

        /// <summary>
        /// 子处方
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btChildPresc_Click(object sender, EventArgs e)
        {
            try
            {
                //当前行是空行不进行子处方设置BTE 20220412
                DataRow drCurrent = gvDetail.GetFocusedDataRow();
                if (drCurrent == null || string.IsNullOrEmpty(drCurrent["DRUG_CODE"].ToString("")))
                {
                    return;
                }
                // 已收费处方不能添加、删除
                //gvList_FocusedRowChanged中已处理
                //liujun add 2019-12-05 FocusedRowChanged会有不触发的情况，所以要进行检查
                if (this.NotAllowModifyPresc(this.gvList.FocusedRowHandle).Equals(-1))
                {
                    UIMessageBox.Warn("处方已收费！");
                    return;
                }
                //liujun add 2019-12-05  FocusedRowChanged会有不触发的情况，所以要进行检查

                // 主处方必须取消所有子处方后再设置子处方
                int iFocusedRowHandle = gvDetail.FocusedRowHandle;
                if (iFocusedRowHandle <= 0)
                {
                    UIMessageBox.Warn("请选择要设置为子处方的记录！");
                    return;
                }
                if (iFocusedRowHandle < gvDetail.RowCount - 1)
                {
                    if (int.Parse(gvDetail.GetRowCellValue(iFocusedRowHandle + 1, "ORDER_SUB_NO").ToString()) > 1)
                    {
                        UIMessageBox.Warn("请先取消后续子处方！");
                        return;
                    }
                }

                DataRow drDetail = gvDetail.GetDataRow(iFocusedRowHandle);
                if (int.Parse(drDetail["ORDER_SUB_NO"].ToString()) > 1)
                {
                    //取消子处方后先设为null 后续保存的时候赋值
                    drDetail["ORDER_NO"] = DBNull.Value;
                    drDetail["ORDER_SUB_NO"] = 1;
                    gvDetail.UpdateCurrentRow();

                    // 刷新子处方前缀显示
                    RefreshSubOrderFlags(false, iFocusedRowHandle);
                }
                else
                {
                    // 设置子处方
                    int iOrderSubNo;
                    iOrderSubNo = int.Parse(gvDetail.GetRowCellValue(iFocusedRowHandle - 1, "ORDER_SUB_NO").ToString());
                    iOrderSubNo++;

                    if (this.gvDetail.GetRowCellValue(iFocusedRowHandle - 1, "ORDER_NO") is null)
                    {
                        drDetail["ORDER_NO"] = DBNull.Value;
                    }
                    else if (this.gvDetail.GetRowCellValue(iFocusedRowHandle - 1, "ORDER_NO").ToString().Trim().Length.Equals(0))
                    {
                        drDetail["ORDER_NO"] = DBNull.Value;
                    }
                    else
                    {
                        drDetail["ORDER_NO"] = int.Parse(this.gvDetail.GetRowCellValue(iFocusedRowHandle - 1, "ORDER_NO").ToString());
                    }

                    // 同步复合处方频次、用法、用药天数
                    drDetail["ORDER_SUB_NO"] = iOrderSubNo;
                    drDetail["ADMINISTRATION"] = gvDetail.GetRowCellValue(iFocusedRowHandle - 1, "ADMINISTRATION").ToString();//用法
                    drDetail["FREQUENCY"] = gvDetail.GetRowCellValue(iFocusedRowHandle - 1, "FREQUENCY").ToString();//频次

                    int iFreqCounter = int.TryParse(gvDetail.GetRowCellValue(iFocusedRowHandle - 1, "FREQ_COUNTER").ToString(), out iFreqCounter) ? iFreqCounter : 0;//频率次数
                    int iFreqInterval = int.TryParse(gvDetail.GetRowCellValue(iFocusedRowHandle - 1, "FREQ_INTERVAL").ToString(), out iFreqInterval) ? iFreqInterval : 0;//频率间隔
                    drDetail["FREQ_COUNTER"] = iFreqCounter;
                    drDetail["FREQ_INTERVAL"] = iFreqInterval;
                    drDetail["FREQ_INTERVAL_UNIT"] = gvDetail.GetRowCellValue(iFocusedRowHandle - 1, "FREQ_INTERVAL_UNIT");

                    gvDetail.SetRowCellValue(iFocusedRowHandle, "ABIDANCE", int.Parse(GridViewHelper.GetDataValue(gvDetail.GetRowCellValue(iFocusedRowHandle - 1, "ABIDANCE"), "int").ToString()));//用药天数
                    drDetail["PERFORM_TIMES"] = int.Parse(GridViewHelper.GetDataValue(gvDetail.GetRowCellValue(iFocusedRowHandle - 1, "PERFORM_TIMES"), "int").ToString());//本院执行次数
                    RefreshSubOrderFlags(true, iFocusedRowHandle);

                    // 重新计算当前行数量、金额
                    CalcAmountByRowAbidance(iFocusedRowHandle);

                    //同步主记录的计价和应收.
                    RefreshMasterCost(gvDetail);
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方设置子处方时出错!详情" + ex.ToString());
            }

        }
        /// <summary>
        /// 协定处方
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btBindingPresc_Click(object sender, EventArgs e)
        {
            this.BindingPrecData();
        }

        #region 保存前费用重新计算
        /// <summary>
        /// 保存前费用重新计算 - 确保剂数信息正确应用到费用计算中
        /// </summary>
        private void RecalculateCostBeforeSave()
        {
            try
            {
                string logPath = @"..\Client\LOG\exLOG\住院处方费用重新计算_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                string logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + " [INFO] 住院处方开始保存前费用重新计算\r\n";
                System.IO.File.AppendAllText(logPath, logEntry);

                // 获取当前选中的处方主记录
                if (gvList.FocusedRowHandle < 0) return;

                DataRowView drv = gvList.GetRow(gvList.FocusedRowHandle) as DataRowView;
                if (drv?.Row == null) return;

                DataRow drMaster = drv.Row;
                string prescType = drMaster["PRESC_TYPE"].ToString();
                if (!prescType.Equals("1")) return; // 只处理草药处方

                int repetition = ConvertHelper.ToInt(drMaster["REPETITION"], 1);
                if (repetition <= 0) repetition = 1;

                logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + $" [INFO] 处方类型:{prescType}, 剂数:{repetition}\r\n";
                System.IO.File.AppendAllText(logPath, logEntry);

                // 重新计算所有明细的数量和费用
                for (int i = 0; i < gvDetail.RowCount; i++)
                {
                    DataRowView detailDrv = gvDetail.GetRow(i) as DataRowView;
                    if (detailDrv?.Row == null) continue;

                    DataRow drDetail = detailDrv.Row;
                    if (drDetail.RowState == DataRowState.Deleted) continue;

                    decimal dosageEach = ConvertHelper.ToDecimal(drDetail["DOSAGE_EACH"], 0);
                    decimal dosePerUnit = ConvertHelper.ToDecimal(drDetail["DOSC_PER_UNIT"], 0);

                    if (dosageEach > 0 && dosePerUnit > 0)
                    {
                        // 重新计算数量（确保包含剂数）
                        decimal inputAmount = 0;
                        if (Parameter.HERBS_DIVIDE_DOSE_PER_UNIT == "1")
                        {
                            inputAmount = Math.Ceiling(Math.Round((dosageEach * repetition) / dosePerUnit, 2));
                        }
                        else
                        {
                            inputAmount = Math.Ceiling(Math.Round(dosageEach * repetition, 2));
                        }

                        drDetail["INPUT_AMOUNT"] = inputAmount;
                        drDetail["QUANTITY"] = inputAmount;

                        // 重新计算费用
                        ComputeDetailCost(drDetail);

                        logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") +
                                  $" [INFO] 药品:{drDetail["DRUG_NAME"]}, 单次剂量:{dosageEach}, 重新计算数量:{inputAmount}, " +
                                  $"费用:{drDetail["COSTS"]}, 应收:{drDetail["PAYMENTS"]}\r\n";
                        System.IO.File.AppendAllText(logPath, logEntry);
                    }
                }

                logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + " [INFO] 住院处方保存前费用重新计算完成\r\n";
                System.IO.File.AppendAllText(logPath, logEntry);
            }
            catch (Exception ex)
            {
                try
                {
                    string logPath = @"..\Client\LOG\exLOG\住院处方费用重新计算_" + DateTime.Now.ToString("yyyyMMdd") + ".log";
                    string logEntry = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + $" [ERROR] 住院处方保存前费用重新计算异常: {ex.Message}\r\n";
                    System.IO.File.AppendAllText(logPath, logEntry);
                }
                catch
                {
                    // 日志记录失败不影响主流程
                }
            }
        }
        #endregion 保存前费用重新计算

        /// <summary>
        /// 处方保存
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void btSave_Click(object sender, EventArgs e)
        {
            try
            {
                //判断数据有效性
                if (SaveJudgmentValidity() < 0)
                {
                    return;
                }

                // 保存前重新计算费用，确保剂数信息正确
                RecalculateCostBeforeSave();

                //Detail新加药生成OrderNo
                SetDetailOrderNo();

                // 整理处方号、分方
                DataTable dtNewPresc = new DataTable();//保存自动分方

                DetailClassity(ref dtNewPresc);

                if (/*Parameter.ISPASS.Equals("1")*/false)
                {
                    if (this.PassQueryOrCheck(33, 0).Equals(-1))
                    {
                        Utility.LogFile.WriteLogAuto("保存处方时，调用美康合理用药出错！！！", "TJHIS_ObOutp_View.txt");
                        return;
                    }
                }

                //给List的ob_visit_no赋值
                SetListObVisitNo(dtNewPresc);

                // 根据新添加处方药品，生成医嘱记录、费用
                string strOrdersWhere = "PATIENT_ID='" + PatientInfo.PatientId + "' and OB_VISIT_NO='" + PatientInfo.ObVisitNo + "' order by order_no,order_sub_no";
                DataTable dtOrders = FrmPrescDoctInputSrv.GetOrders(strOrdersWhere);

                string strBillWhere = "PATIENT_ID='" + PatientInfo.PatientId + "' and OB_VISIT_NO='" + PatientInfo.ObVisitNo + "'";
                DataTable dtObOutpBillDetail = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetObOutpBillDetailDT(strBillWhere);

                string strCostsWhere = "PATIENT_ID='" + PatientInfo.PatientId + "' and OB_VISIT_NO='" + PatientInfo.ObVisitNo + "'";
                DataTable dtObOrdersCosts = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetObOrdersCostsDT(strCostsWhere);
                if (DetailInsertOrderCost(dtOrders, dtObOutpBillDetail, dtObOrdersCosts) < 0)
                {
                    Utility.LogFile.WriteLogAuto("保存处方时，生成医嘱或计价出错!PATIENT_ID = " + PatientInfo.PatientId, "TJHIS_ObOutp_View.txt");
                    return;
                }


                ////保存
                List<DataTable> listDts = new List<DataTable>();
                //mdtList删除其它表的列
                DataTable mdtList_copy = dtList.Copy();
                mdtList_copy.Columns.Remove("BED_NO");
                mdtList_copy.Columns.Remove("PREPAYMENTS");
                mdtList_copy.Columns.Remove("DIAGNOSIS");
                mdtList_copy.Columns.Remove("BED_LABEL");
                //mdtList_copy.Columns.Remove("CHARGE_INDICATOR");
                //**********屏蔽放下边
                //listDts.Add(mdtList_copy);

                //mdtSaveDetail删除其它表的列

                //liujun add 2021-11-16 处理主键发生改变的情况
                List<DataRow> prescDetailList = this.dtSaveDetail.AsEnumerable().Where(r => r.RowState == DataRowState.Modified && r["ITEM_NO"].ToInt() != r["ITEM_NO", DataRowVersion.Original].ToInt()).ToList();
                foreach (DataRow r in prescDetailList)
                {
                    DataRow newR = this.dtSaveDetail.NewRow();
                    newR.ItemArray = r.ItemArray;
                    this.dtSaveDetail.Rows.Add(newR);
                    r.Delete();
                }
                //liujun add 2021-11-16 处理主键发生改变的情况

                DataTable mdtSaveDetail_copy = dtSaveDetail.Copy();
                mdtSaveDetail_copy.Columns.Remove("PRESC_STATUS");
                mdtSaveDetail_copy.Columns.Remove("PRESC_TYPE");
                mdtSaveDetail_copy.Columns.Remove("DISPENSARY");
                mdtSaveDetail_copy.Columns.Remove("ORDER_NO_DUP");
                mdtSaveDetail_copy.Columns.Remove("ORDER_SUB_NO_DUP");

                //修改处方保存后 减药都删除 处方也删掉  YYS 2020/8/26
                if (this.gvDetail.RowCount == 0)
                {
                    try
                    {
                        //YH20230130新疆宝石花---
                        string PrescNo = this.gvList.GetFocusedRowCellValue("PRESC_NO").ToString();
                        List<DataRow> list = mdtList_copy.AsEnumerable().Where<DataRow>(row => (!row.RowState.Equals(DataRowState.Deleted) || !row.RowState.Equals(DataRowState.Detached)) && row["PRESC_NO"].ToString().Equals(PrescNo)).ToList();
                        if (list.Count > 0)
                        {
                            list[0].Delete();
                        }

                        //List<DataRow> list = mdtList_copy.AsEnumerable().Where<DataRow>(row => !row.RowState.Equals(DataRowState.Deleted) || !row.RowState.Equals(DataRowState.Detached)).ToList();
                        //if (list.Count > 0)
                        //{
                        //    list[0].Delete();
                        //}
                    }
                    catch (Exception)
                    {
                        UIMessageBox.Err("处方中保存空处方出错!详情" + e.ToString());
                    }
                }

                ///////////////////////////////////////////////////////////
                //DataTable dt_model = FrmPrescDoctInputSrv.GetDrug_Presc_DetailList("1=0");
                //List<OB_DRUG_PRESC_DETAIL> modellist = new List<OB_DRUG_PRESC_DETAIL>();
                //Tjhis.Oboutp.Station.Comm.DataTableLIstHelp.DataTableToList(modellist, mdtSaveDetail);
                //Tjhis.Oboutp.Station.Comm.DataTableLIstHelp.ListToDataTable(modellist, dt_model);
                //listDts.Add(dt_model);
                //////listDts.Add(mdtSaveDetail);
                ///////////////////////////////////////////////////////////////////////////
                //DataTable dtSaveDetail = ((DataTable)this.gcDetail.DataSource).Copy();
                //foreach (DataRow dr in mdtSaveDetail.Rows)
                //{
                //    dtSaveDetail.Rows.Add(dr.ItemArray);
                //}
                listDts.Add(mdtSaveDetail_copy);
                ////////////////////////

                DataTable dtNewPresc_copy = dtNewPresc.Copy();
                if (dtNewPresc_copy.Rows.Count > 0)
                {
                    //dtNewPresc删除其它表的列
                    dtNewPresc_copy.Columns.Remove("BED_NO");
                    dtNewPresc_copy.Columns.Remove("PREPAYMENTS");
                    dtNewPresc_copy.Columns.Remove("DIAGNOSIS");
                    dtNewPresc_copy.Columns.Remove("BED_LABEL");
                    //dtNewPresc_copy.Columns.Remove("CHARGE_INDICATOR");

                    //**********
                    foreach (DataRow dr in dtNewPresc_copy.Rows)
                    {
                        mdtList_copy.ImportRow(dr);
                    }

                    //dtNewPresc.Rows.Add(drNewPresc);
                }

                // 修改处方类型
                /*foreach (DataRow drMdtList in mdtList_copy.Rows)
                {
                    if (drMdtList.RowState == DataRowState.Added)
                    {
                        if (Parameter.PRESC_DRUG_DISPS.Contains(drMdtList["DISPENSARY"].ToString()))
                        {
                            drMdtList["PRESC_TYPE"] = 0;
                        }
                        else if (Parameter.PRESC_CDRUG_DISPS.Contains(drMdtList["DISPENSARY"].ToString()))
                        {
                            drMdtList["PRESC_TYPE"] = 1;
                        }
                    }
                }*/

                listDts.Add(mdtList_copy);                                     //**********

                listDts.Add(dtOrders);
                listDts.Add(dtObOrdersCosts);
                listDts.Add(dtObOutpBillDetail);

                //string PrescDate = this.gvList.GetFocusedRowCellValue("PRESC_DATE").ToDateTime().ToString("yyyy-MM-dd HH:mm:ss");
                //string PrescNo = this.gvList.GetFocusedRowCellValue("PRESC_NO").ToString();

                //#region 处理艾隆数据
                //OutMedItem item = new OutMedItem();
                //item.VisitDate = this.visitDate;
                //item.VisitNo = this.visitNo;
                //item.PrescDate = PrescDate;
                //item.PrescNo = PrescNo;

                DataTable[] tables = listDts.ToArray();
                DataTable[] tableList = tables;

                //if (this.ProcessOutMedHis(item, false))
                //{
                //    tableList = new DataTable[tables.Length + 1];
                //    for (int index = 0; index < tables.Length; index++)
                //    {
                //        tableList[index] = tables[index];
                //    }

                //    tableList[tables.Length] = this.mdtOutMedHis;
                //}
                //else
                //{
                //    tableList = tables;
                //}
                //#endregion 处理艾隆数据

                //if (TJHIS.MC.Comm.Oracle.OracleSrv.UpdateDataTable(listDts.ToArray()) > 0)
                //==20200624先保存选定的处方号
                DataRow drCurrentPresc = gvList.GetFocusedDataRow();
                if (drCurrentPresc != null)
                {
                    string strPrescNO = drCurrentPresc["PRESC_NO"].ToString();
                    if (string.IsNullOrEmpty(strPrescNO))
                    {
                        msSelectedPrescNo = -1;
                    }
                    else
                    {
                        msSelectedPrescNo = int.Parse(strPrescNO);
                    }
                }
                else
                {
                    msSelectedPrescNo = -1;
                }

                //BTE 20220412 在保存前需要对dtObOrdersCosts表中的ITEM_NO重新赋值
                var collection1 = dtObOrdersCosts.AsEnumerable().Where(r => r.RowState.Equals(DataRowState.Added) && r["ITEM_NO"].ToInt(0) == 0);//筛选所有item_NO为0 的新增行
                int[] orderNos = collection1.Select(r => r["ORDER_NO"].ToInt(0)).Distinct().ToArray();
                foreach (int orderNo in orderNos)
                {
                    var orderAndSubs = collection1.Where(r => r["ORDER_NO"].ToInt(0) == orderNo).Select(r => new { orderNo = r["ORDER_NO"].ToInt(0), subNo = r["ORDER_SUB_NO"].ToInt(0) }).Distinct();
                    int MaxSubNo = orderAndSubs.Max(r => r.subNo);
                    foreach (var orderAndSub in orderAndSubs)
                    {
                        var collection2 = collection1.Where(r => r["ORDER_NO"].ToInt(0) == orderAndSub.orderNo && r["ORDER_SUB_NO"].ToInt(0) == orderAndSub.subNo).ToList();
                        for (int i = 0; i < collection2.Count; i++)
                        {
                            if (i == 0)
                                collection2[i]["ITEM_NO"] = collection2[i]["ORDER_SUB_NO"];
                            else
                                collection2[i]["ITEM_NO"] = ++MaxSubNo;
                        }
                    }
                }

                if (DataServerBase.SaveTables(tableList, Global.dbHelper) > 0)
                {
                    //this.dtSaveDetail.AcceptChanges();
                    boolChange = false;
                    UIMessageBox.Info("数据保存成功");
                    //绑定数据源
                    await BindingDataSourceAsync();
                    //20200624选中保存前选中的处方
                    //int ii= gvList.LocateByValue("PRESC_NO", msSelectedPrescNo);
                    gvList.FocusedRowHandle = gvList.LocateByValue("PRESC_NO", msSelectedPrescNo);

                    if (this.gvList.FocusedRowHandle == 0)
                    {

                        miSelectedListRowHandle = -1;
                    }
                    gvList_FocusedRowChanged(this.gvList, null);
                    //if(this.gvDetail.RowCount>0)
                    //{
                    //    this.gvDetail.FocusedRowHandle = this.gvDetail.RowCount - 1;
                    //}/
                    GlobalEvnents.Global_RefreshOrders?.Invoke(null, new Global_RefreshOrders_EventArgs("处方"));
                }
                else
                {
                    UIMessageBox.Warn("数据保存失败！");
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方保存时出错!详情" + ex.ToString());

            }

            this.gvDetail.SortInfo.Clear();
        }

        /// <summary>
        /// 药品根据天数，频次等 算出 数量
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gvDetail_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            try
            {
                int iFocusedRowHandle = gvDetail.FocusedRowHandle;
                if (iFocusedRowHandle < 0) return;

                //显示列毒理属性 未做数据改变(修改点击处方选项卡 再关闭会提示保存问题  YYS 2020/8/16)
                if (e.Column.FieldName != "TOXI_PROPERTY")
                {
                    this.boolChange = true;
                }

                DataRow drGvDetail = gvDetail.GetDataRow(iFocusedRowHandle);
                if (string.IsNullOrEmpty(drGvDetail["DRUG_CODE"].ToString())) return;

                string strFrequency;
                string abidande;
                decimal decQuantity;
                decimal decDosageEach;
                switch (e.Column.FieldName)
                {
                    case "NWARN":
                        break;

                    case "ADMINISTRATION":
                        // 同步复合处方药品用法，说明：已控制子处方不允许调整途径、频次，此次只处理修改复合处方第一个药品用法的逻辑
                        for (int rowno = iFocusedRowHandle + 1; rowno < this.gvDetail.RowCount; rowno++)
                        {
                            int strSubNo = this.gvDetail.GetRowCellValue(rowno, "ORDER_SUB_NO").ToInt(1);
                            if (strSubNo == 1) break;

                            DataRow dr = this.gvDetail.GetDataRow(rowno);
                            dr["ADMINISTRATION"] = this.gvDetail.GetRowCellValue(iFocusedRowHandle, "ADMINISTRATION").ToString().Trim();
                        }

                        break;
                    case "DOSAGE_EACH"://单次剂量
                        //触发修改数量事件
                        abidande = drGvDetail["ABIDANCE"].ToString("");
                        gvDetail_CellValueChanged(gvDetail, new CellValueChangedEventArgs(iFocusedRowHandle, gvDetail.Columns["ABIDANCE"], abidande));
                        break;
                    case "FREQUENCY"://频次
                        // 1)根据频次刷新附属信息；2)同步复合处方药品用法，说明：已控制子处方不允许调整途径、频次，此次只处理修改复合处方第一个药品用法的逻辑
                        strFrequency = drGvDetail["FREQUENCY"].ToString("");
                        DataRow drFrequency = BasicDict.PerformFreqDict.AsEnumerable().FirstOrDefault(r => r["FREQ_DESC"].ToString("").Equals(strFrequency));
                        int iDBFreqCounter = 0;
                        int iDBFreqInterval = 0;
                        string strDBFreqIntervalUnits = "";
                        if (drFrequency != null)
                        {
                            iDBFreqCounter = drFrequency["FREQ_COUNTER"].ToInt(0);
                            iDBFreqInterval = drFrequency["FREQ_INTERVAL"].ToInt(0);
                            strDBFreqIntervalUnits = drFrequency["FREQ_INTERVAL_UNITS"].ToString("");
                        }
                        drGvDetail["FREQ_COUNTER"] = iDBFreqCounter;//频率次数
                        drGvDetail["FREQ_INTERVAL"] = iDBFreqInterval;//频率间隔
                        drGvDetail["FREQ_INTERVAL_UNIT"] = strDBFreqIntervalUnits;//频率间隔单位

                        for (int i = gvDetail.FocusedRowHandle; i < gvDetail.RowCount; i++)
                        {
                            DataRow dr = gvDetail.GetDataRow(i);
                            if (dr["ORDER_SUB_NO"].ToInt(1) == 1 && i != iFocusedRowHandle) break;

                            dr["FREQUENCY"] = strFrequency;
                            dr["FREQ_COUNTER"] = iDBFreqCounter;
                            dr["FREQ_INTERVAL"] = iDBFreqInterval;
                            dr["FREQ_INTERVAL_UNIT"] = strDBFreqIntervalUnits;

                            // 根据用药天数，反算新药品数量
                            CalcAmountByRowAbidance(i);
                        }

                        //同步主记录的计价和应收.
                        RefreshMasterCost(gvDetail);
                        break;
                    case "SPLIT_FLAG":
                        //触发修改数量事件
                        decDosageEach = decimal.TryParse(drGvDetail["DOSAGE_EACH"].ToString(), out decDosageEach) ? decDosageEach : 0;
                        abidande = drGvDetail["ABIDANCE"].ToString("");
                        if (decDosageEach > 0 && !string.IsNullOrEmpty(abidande))
                        {
                            gvDetail_CellValueChanged(gvDetail, new CellValueChangedEventArgs(iFocusedRowHandle, gvDetail.Columns["QUANTITY"], abidande));
                        }
                        break;
                    case "QUANTITY"://数量
                        // 1.先计算频次数据
                        strFrequency = drGvDetail["FREQUENCY"].ToString("");
                        DataRow drFrequency1 = BasicDict.PerformFreqDict.AsEnumerable().FirstOrDefault(r => r["FREQ_DESC"].ToString("").Equals(strFrequency));
                        int iDBFreqCounter1 = 0;
                        int iDBFreqInterval1 = 0;
                        string strDBFreqIntervalUnits1 = "";
                        if (drFrequency1 != null)
                        {
                            iDBFreqCounter1 = drFrequency1["FREQ_COUNTER"].ToInt(0);
                            iDBFreqInterval1 = drFrequency1["FREQ_INTERVAL"].ToInt(0);
                            strDBFreqIntervalUnits1 = drFrequency1["FREQ_INTERVAL_UNITS"].ToString("");
                        }
                        drGvDetail["FREQ_COUNTER"] = iDBFreqCounter1;//频率次数
                        drGvDetail["FREQ_INTERVAL"] = iDBFreqInterval1;//频率间隔
                        drGvDetail["FREQ_INTERVAL_UNIT"] = strDBFreqIntervalUnits1;//频率间隔单位

                        // 2.再算用药天数
                        decQuantity = decimal.TryParse(this.gvDetail.GetRowCellValue(iFocusedRowHandle, "QUANTITY").ToString(), out decQuantity) ? decQuantity : 0;
                        if (decQuantity <= 0) return;

                        //计算价格
                        ComputeDetailCost(drGvDetail);

                        //计算执行次数、天数、医保天数
                        decimal decPerformTimes = 0;//执行次数
                        decimal decAbidance = 0;//用药天数
                        decimal decInsurAbidance = 0;//医保用药天数
                        CalcInsurAbidance(drGvDetail, ref decPerformTimes, ref decAbidance, ref decInsurAbidance);
                        //如果天数>0  执行次数=0 则设成1天  YYS  2021.3.30
                        if (decAbidance > 0 && decPerformTimes <= 0) decPerformTimes = 1;

                        drGvDetail["INSUR_ABIDANCE"] = decInsurAbidance;
                        if (drGvDetail["ORDER_SUB_NO"].ToInt(1) == 1)
                        {
                            drGvDetail["PERFORM_TIMES"] = decPerformTimes;
                            drGvDetail["ABIDANCE"] = decAbidance;

                            for (int i = gvDetail.FocusedRowHandle + 1; i < gvDetail.RowCount; i++)
                            {
                                DataRow dr = gvDetail.GetDataRow(i);
                                if (dr["ORDER_SUB_NO"].ToInt(1) == 1 && i != iFocusedRowHandle) break;

                                dr["ABIDANCE"] = decAbidance;

                                // 根据用药天数，反算新药品数量
                                CalcAmountByRowAbidance(i);
                            }
                        }

                        //同步主记录的计价和应收.
                        RefreshMasterCost(gvDetail);
                        break;
                    case "ABIDANCE"://天数
                        // 1.先计算频次数据
                        strFrequency = drGvDetail["FREQUENCY"].ToString("");
                        if (string.IsNullOrEmpty(strFrequency))
                        {
                            break;
                        }
                        DataRow drFrequency2 = BasicDict.PerformFreqDict.Select("FREQ_DESC = '" + strFrequency + "'")[0];
                        int iDBFreqCounter2 = 0;
                        int iDBFreqInterval2 = 0;
                        string strDBFreqIntervalUnits2 = "";
                        if (drFrequency2 != null)
                        {
                            iDBFreqCounter2 = drFrequency2["FREQ_COUNTER"].ToInt(0);
                            iDBFreqInterval2 = drFrequency2["FREQ_INTERVAL"].ToInt(0);
                            strDBFreqIntervalUnits2 = drFrequency2["FREQ_INTERVAL_UNITS"].ToString("");
                        }
                        drGvDetail["FREQ_COUNTER"] = iDBFreqCounter2;//频率次数
                        drGvDetail["FREQ_INTERVAL"] = iDBFreqInterval2;//频率间隔
                        drGvDetail["FREQ_INTERVAL_UNIT"] = strDBFreqIntervalUnits2;//频率间隔单位

                        // 2.再计算天数
                        int iAbidance = 0;
                        iAbidance = int.TryParse(drGvDetail["ABIDANCE"].ToString(), out iAbidance) ? iAbidance : 0;
                        if (iAbidance <= 0) return;

                        CalcAmountByRowAbidance(iFocusedRowHandle);

                        for (int i = gvDetail.FocusedRowHandle; i < gvDetail.RowCount; i++)
                        {
                            DataRow dr = gvDetail.GetDataRow(i);
                            if (dr["ORDER_SUB_NO"].ToInt(1) == 1 && i != iFocusedRowHandle) break;

                            dr["ABIDANCE"] = iAbidance;

                            // 根据用药天数，反算新药品数量
                            CalcAmountByRowAbidance(i);
                        }

                        //同步主记录的计价和应收.
                        RefreshMasterCost(gvDetail);
                        break;
                }

                //Begin----保存gvDetail对应的datatable时增加下面代码，否则会出现datatable中有值，但数据库中未保存上---
                gvDetail.CloseEditor();
                gvDetail.UpdateCurrentRow();
                //End----------------------------------------------------------------------------------------------- 
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("根据天数，频次算具体数量时出错!详情" + ex.ToString());
            }
        }

        /// <summary>
        ///  根据指定行、指定列动态计算药品数量
        /// </summary>
        private void CalcAmountByRowAbidance(int rowHandle)
        {
            // 取对应DataRow
            DataRow dr = gvDetail.GetDataRow(rowHandle);
            if (dr == null) return;

            // 获取药品用药天数
            int iAbidance = 0;
            iAbidance = int.TryParse(dr["ABIDANCE"].ToString(), out iAbidance) ? iAbidance : 0;
            if (iAbidance <= 0) return;

            // 根据天数计算数量
            string strFrequency = dr["FREQUENCY"].ToString("");
            if (string.IsNullOrEmpty(strFrequency)) return;

            int iFreqCounter = int.TryParse(dr["FREQ_COUNTER"].ToString(), out iFreqCounter) ? iFreqCounter : 0;
            int iFreqInterval = int.TryParse(dr["FREQ_INTERVAL"].ToString(), out iFreqInterval) ? iFreqInterval : 0;
            string strFreqIntervalUnit = dr["FREQ_INTERVAL_UNIT"].ToString();

            // 计算执行次数
            decimal decPerFormtimes = 0;
            if (!string.IsNullOrEmpty(strFreqIntervalUnit))
            {
                if (iFreqCounter == 0 || iFreqInterval == 0)
                {
                    decPerFormtimes = 1;
                }
                else
                {
                    switch (strFreqIntervalUnit)
                    {
                        case "周":
                            decPerFormtimes = (iAbidance * iFreqCounter) / (iFreqInterval * 7);
                            break;
                        case "日":
                            decPerFormtimes = (iAbidance * iFreqCounter) / (iFreqInterval);
                            break;
                        case "小时":
                            decPerFormtimes = (iAbidance * iFreqCounter * 24) / (iFreqInterval);
                            break;
                        case "月":
                            decPerFormtimes = (iAbidance * iFreqCounter) / (30 * iFreqInterval);
                            break;
                    }
                    // 考虑到隔日、间隔用药的情况，需要将频次系数重新整理：小于1时取1，否则向下取整
                    if (decPerFormtimes < 1)
                    {
                        decPerFormtimes = 1;
                    }
                    else
                    {
                        decPerFormtimes = Math.Floor(Math.Round(decPerFormtimes, 4, MidpointRounding.AwayFromZero));
                    }
                }
            }

            // 若当前执行次数为0，则动态设置成1
            if (decPerFormtimes == 0) decPerFormtimes = 1;

            // 转换药品拆包消耗量作为单次实际使用量
            decimal decDosageEach = CalcSplitDosageEach(dr);//单次剂量
            decimal decDoscPerUnit = decimal.Parse(dr["DOSC_PER_UNIT"].ToString()); //最小单位剂量
            decimal decAmountPerPackage = decimal.Parse(dr["AMOUNT_PER_PACKAGE"].ToString()); //包装数量

            //增加 round函数，避免ceiling函数预小数点后很多0的情况会进1的问题
            decimal decQuantity = Math.Ceiling(Math.Round((decPerFormtimes * decDosageEach) / (decDoscPerUnit * decAmountPerPackage), 4, MidpointRounding.AwayFromZero));
            dr["QUANTITY"] = decQuantity;//不触发数量的修改事件

            //计算价格
            ComputeDetailCost(dr);

            //计算执行次数、天数、医保天数
            decimal decPerformTimes = 0;//执行次数
            decimal decAbidance = 0;//用药天数
            decimal decInsurAbidance = 0;//医保用药天数
            CalcInsurAbidance(dr, ref decPerformTimes, ref decAbidance, ref decInsurAbidance);
            //如果天数>0  执行次数=0 则设成1天  YYS  2021.3.30
            if (decAbidance > 0 && decPerformTimes <= 0)
                decPerformTimes = 1;

            dr["PERFORM_TIMES"] = decPerFormtimes;
            dr["ABIDANCE"] = iAbidance;
            dr["INSUR_ABIDANCE"] = decInsurAbidance;

            gvDetail.UpdateCurrentRow();//刷新明细金额合计功能
        }

        /// <summary>
        /// CheckEdit拆包标志选中和取消选中
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void repositoryItemCheckEdit1_QueryCheckStateByValue(object sender, DevExpress.XtraEditors.Controls.QueryCheckStateByValueEventArgs e)
        {
            string val = "";
            if (e.Value != null)
            {
                val = e.Value.ToString().Trim();
            }
            else
            {
                val = "0";//默认为不选   
            }

            if (val == "1")
            {
                val = "1";
            }
            else
            {
                val = "0";
            }

            switch (val)
            {
                //case "True":
                //case "Yes":
                case "1":
                    e.CheckState = CheckState.Checked;
                    break;
                //case "False":
                //case "No":
                case "0":
                    e.CheckState = CheckState.Unchecked;
                    break;
                default:
                    e.CheckState = CheckState.Unchecked;
                    break;
            }
            e.Handled = true;
        }
        private List<PrescInfo> listPrescInfo = new List<PrescInfo>();

        /// <summary>
        /// CA 签名打印
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btPrint_Click(object sender, EventArgs e)//打印
        {
            try
            {
                //用于测试
                //listPrescInfo.Add(new PrescInfo { PRESC_DATE = "2019-09-25 10:20:21", PRESC_NO = "473" });
                /////////////////
                int iListHandle = gvList.FocusedRowHandle;
                if (iListHandle >= 0)
                {
                    listPrescInfo.Clear();
                    PrescInfo prescInfo = new PrescInfo();
                    prescInfo.PRESC_DATE = ConvertHelper.ToDateTime(gvList.GetRowCellValue(iListHandle, "PRESC_DATE"));
                    prescInfo.PRESC_NO = gvList.GetRowCellValue(iListHandle, "PRESC_NO").ToInt();
                    listPrescInfo.Add(prescInfo);
                }
                foreach (PrescInfo pi in listPrescInfo)
                {
                    DataTable dt = new PrescPrintSvr().GetPrintData(pi.PRESC_DATE, pi.PRESC_NO);
                    setSubOrder(dt);
                    //读取app.config的配置路径 YYS 2020/9/2
                    String strFilePath = Global.HWPostilPath;
                    strFilePath = strFilePath + pi.PRESC_NO.ToString() + "_" + pi.PRESC_DATE.ToString("yyyyMMddHHmmss") + "_" + DataServerBase.GetServerTime().ToString("yyyyMMddHHmmss") + ".pdf";

                    String strToxiProperty = "";
                    if (dt.Rows.Count > 0)
                    {
                        String strWhere = "drug_code='" + dt.Rows[0]["DRUG_CODE"].ToString() + "' and drug_spec='" + dt.Rows[0]["DRUG_SPEC"].ToString() + "'";
                        strToxiProperty = Tjhis.Oboutp.Station.PrescSvr.FrmPrescDoctInputSrv.GetToxiProperty(strWhere).ToString();
                    }
                    Report.ReportHandle.LoadTempPreView("留观处方笺", dt, "", strToxiProperty);


                    //Report.ReportHandle.LoadTempPreView("留观处方笺", dt, strFilePath, strToxiProperty);
                    ////ca电子签章
                    //HWPostil.HWPostil hWPostil = new HWPostil.HWPostil();
                    //hWPostil.FilePath = strFilePath;//处方号+处方日期+打印时间
                    //hWPostil.AfterSave_Event += (x, y) =>
                    //{
                    //    // 读取PDF文件
                    //    byte[] strs = File.ReadAllBytes(strFilePath);

                    //    // 处理完，清理生成文件
                    //    Comm.FileHelper.DeleteFile(strFilePath);

                    //    // 推送平台消息
                    //    string result = UploadFile("4", dt.Rows[0], strs, 1, "A");
                    //    if (string.IsNullOrEmpty(result))
                    //    {
                    //        MessageBox.Show("签名提交成功");
                    //        hWPostil.Close();
                    //    }
                    //    else
                    //        MessageBox.Show("签名失败，请联系信息科支持！错误信息：" + result);
                    //};
                    //hWPostil.SearchText = "医师:";
                    //hWPostil.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方电子签章时出错!详情" + ex.ToString());
            }

        }

        /// <summary>  
        /// 指定Post地址使用Get 方式获取全部字符串  
        /// </summary>  
        /// <param name="url">请求后台地址</param>  
        /// <param name="content">Post提交数据内容(utf-8编码的)</param>  
        /// <returns></returns>  
        private string Post(string url, string content)
        {
            try
            {
                string result = "";
                HttpWebRequest req = (HttpWebRequest)WebRequest.Create(url);
                req.Method = "POST";
                req.ContentType = "application/x-www-form-urlencoded";
                #region 添加Post 参数
                byte[] data = Encoding.UTF8.GetBytes(content);
                req.ContentLength = data.Length;
                using (Stream reqStream = req.GetRequestStream())
                {
                    reqStream.Write(data, 0, data.Length);
                    reqStream.Close();
                }
                #endregion
                HttpWebResponse resp = (HttpWebResponse)req.GetResponse();
                Stream stream = resp.GetResponseStream();
                //获取响应内容  
                using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                {
                    result = reader.ReadToEnd();
                }
                return result;
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("Post请求地址" + url + " 出错!详情" + ex.ToString());
                //File.WriteAllText(filepath + "ex.txt", ex.ToString());
                Utility.LogFile.WriteLogAuto("Post请求" + url + ",content内容" + content + " 出错!详情" + ex.ToString(), "TJHIS_ObOutp_View.txt");
                return string.Empty;
            }
        }

        /// <summary>
        /// 推送平台数据拼装
        /// </summary>
        /// <param name="source"></param>
        /// <param name="dr"></param>
        /// <param name="strs"></param>
        /// <param name="endPageNumber"></param>
        /// <param name="state">A为增加 D为删除</param>
        /// <returns></returns>
        private string UploadFile(string source, DataRow dr, byte[] strs, int endPageNumber, string state)
        {
            try
            {
                string applyNo = dr["PATIENT_ID"].ToString() + "_" + dr["OB_VISIT_NO"].ToString() + "_" + dr["PRESC_NO"].ToString();
                StringBuilder sb = new StringBuilder();
                sb.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                sb.AppendLine("<root>");
                sb.AppendLine("<AuthHeader>");
                sb.AppendLine("<msgType>TJ508</msgType>");
                sb.AppendLine("<msgId>" + Guid.NewGuid().ToString() + "</msgId>");
                sb.AppendLine("<createTime>" + new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString("yyyyMMddHHmmss") + "</createTime>");
                sb.AppendLine("<sourceId>1.3.6.1.4.1.1000000.2016.100</sourceId>");   // ??
                sb.AppendLine("<targetId>1.3.6.1.4.1.1000000.2016.xxx</targetId>");   // ??
                sb.AppendLine("<sysPassword></sysPassword>");
                sb.AppendLine("</AuthHeader>");
                sb.AppendLine("<ControlActProcess> ");
                sb.AppendLine("<PatientId>" + dr["PATIENT_ID"].ToString() + "</PatientId>");
                if (source == "2")
                    sb.AppendLine("<VisitId>" + dr["VISIT_ID"].ToString() + "</VisitId>");
                else
                    sb.AppendLine("<VisitId>" + 0 + "</VisitId>");
                sb.AppendLine("<PatientSource>" + source + "</PatientSource>");
                sb.AppendLine("<Name>" + dr["NAME"].ToString() + "</Name>");
                sb.AppendLine("<ApplyNo>" + applyNo + "</ApplyNo>");
                sb.AppendLine("<ReportNo>" + applyNo + "</ReportNo>");
                sb.AppendLine("<ReportTitle>留观处方笺</ReportTitle>");
                sb.AppendLine("<ReportClass>A</ReportClass>");
                sb.AppendLine("<ReportSubClass>A_LGCF</ReportSubClass>");
                sb.AppendLine("<ReportDept>" + SystemParm.LoginUser.DEPT_CODE + "</ReportDept>");
                sb.AppendLine("<ReportDate>" + new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString("yyyy-MM-dd HH:mm:ss") + "</ReportDate>");
                if (source == "1")
                    //留观不走这个 所以没有这个visit_date
                    sb.AppendLine("<ReportActiveDate>" + DateTime.Parse(dr["visit_date"].ToString().Split(' ')[0] + " 00:00:00").ToString("yyyy-MM-dd HH:mm:ss") + "</ReportActiveDate>");
                else
                    //留观走这个
                    sb.AppendLine("<ReportActiveDate>" + new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString("yyyy-MM-dd HH:mm:ss") + "</ReportActiveDate>");
                sb.AppendLine("<ReportStatus>" + state + "</ReportStatus>");
                sb.AppendLine("<PageOrention>V</PageOrention>");
                sb.AppendLine("<PageSize>A4</PageSize>");
                sb.AppendLine("<PageCount>" + endPageNumber.ToString() + "</PageCount>");
                sb.AppendLine("<Notes></Notes>");
                sb.AppendLine("<FileType>pdf</FileType>");
                sb.AppendLine("<FileBase64>" + Convert.ToBase64String(strs) + "</FileBase64>");
                sb.AppendLine("</ControlActProcess>");
                sb.AppendLine("</root>");

                string result = Post(postUrl, sb.ToString());
                Utility.LogFile.WriteLogAuto("PRESC_NO:" + dr["PRESC_NO"].ToString() + ",ReportStatus:" + state + "向平台发送时返回的结果：result：" + result, "TJHIS_ObOutp_View.txt");
                if (result.Contains("处理成功") == false)
                {
                    return result;
                }
                else
                {
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.Err("处方电子签章推送平台数据拼装出错!详情" + ex.ToString());
                Utility.LogFile.WriteLogAuto("PRESC_NO:" + dr["PRESC_NO"].ToString() + ",ReportStatus:" + state + "向平台发送时数据时出错!!!", "TJHIS_ObOutp_View.txt");
                return string.Empty;
            }

        }

        /// <summary>
        /// 复制处方
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btCopyPresc_Click(object sender, EventArgs e)
        {
            this.CopyPrescData();
        }

        /// <summary>
        /// 只要点击detail中的下拉框 就改为保存状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gvDetail_ShownEditor(object sender, EventArgs e)
        {
            //this.boolChange = true;
        }

        /// <summary>
        /// 毁方
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btDelPresc_Click(object sender, EventArgs e)
        {
            this.DestroyPresc();
        }

        private void gvList_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            //Begin----保存gvList对应的datatable时增加下面代码，否则会出现datatable中有值，但数据库中未保存上---
            gvList.CloseEditor();
            gvList.UpdateCurrentRow();
            //End-----------------------------------------------------------------------------------------------
        }

        private void gvDetail_ShowingEditor(object sender, CancelEventArgs e)
        {
            // 子处方不允许调整频次、途径
            if (gvDetail.FocusedColumn.FieldName.ToString("").Equals("ADMINISTRATION") || gvDetail.FocusedColumn.FieldName.ToString("").Equals("FREQUENCY") || gvDetail.FocusedColumn.FieldName.ToString("").Equals("ABIDANCE"))
            {
                if (gvDetail.GetRowCellValue(gvDetail.FocusedRowHandle, "ORDER_SUB_NO").ToInt(1) > 1)
                    e.Cancel = true;
            }
        }

        private void repositoryItemPopupContainerEdit1_KeyDown(object sender, KeyEventArgs e)
        {
            PopupContainerEdit inputEdit = sender as PopupContainerEdit;
            if (e.KeyCode.Equals(Keys.F9))
            {
                if (!inputEdit.IsPopupOpen)
                    inputEdit.ShowPopup();
            }
        }

        /// <summary>
        /// 刷新按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btRefresh_Click(object sender, EventArgs e)
        {
            if (boolChange)
            {
                if (UIMessageBox.YesNo("当前处方修改未保存，是否进行保存？").Equals(DialogResult.Yes))
                {
                    this.btSave_Click(this.btSave, null);
                }
            }

            int handle = this.gvList.FocusedRowHandle;

            this.InitData();
            this.boolChange = false;

            if (this.dtList.Rows.Count.Equals(0))
            {
                this.btNewPresc_Click(this.btNewPresc, null);
            }
            else
            {
                miSelectedListRowHandle = -1;
                this.gvList_FocusedRowChanged(this.gvList, new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs(-1, 0));
            }
        }

        /// <summary>
        /// 非数据绑定的列触发事件。 以便于自行处理该列的显示数据与数据源
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gvDetail_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            #region  加药保存，然后再减药再加药保存报错
            //if (e.Column.FieldName == "IMG" && e.IsGetData)
            //{
            //    if (e.ListSourceRowIndex < this.mdtSaveDetail.Rows.Count)
            //    {
            //        string index = this.mdtSaveDetail.Rows[e.ListSourceRowIndex]["NWARN"].ToString("");
            //        if (!Global.StringIsNull(index))
            //            e.Value = imageList[index];
            //        else
            //            e.Value = imageList["6"];
            //    }
            //    else
            //        e.Value = imageList["6"];
            //}
            //else
            //    e.Value = imageList["6"];
            #endregion 加药保存，然后再减药再加药保存报错
            if (e.Column.FieldName == "IMG" && e.IsGetData)
            {
                int handle = this.gvDetail.GetRowHandle(e.ListSourceRowIndex);//GetRowHandle()是针对datatable的数据行(datarow)获得指定的数据行在gridView中的handle
                //MessageBox.Show("gvDetail_CustomUnboundColumnData" + handle);
                if (handle >= 0)
                {
                    string index = this.gvDetail.GetRowCellValue(handle, "NWARN").ToString("");
                    if (!Global.StringIsNull(index))
                        switch (index)
                        {
                            case "1":
                            case "2":
                            case "3":
                            case "4":
                            case "5":
                                e.Value = imageList[index];
                                break;
                            default:
                                e.Value = imageList["6"];
                                break;
                        }
                    else
                        e.Value = imageList["6"];

                }
                else
                    e.Value = imageList["6"];
            }
            else
                e.Value = imageList["6"];
        }

        /// <summary>
        /// 药品Grid右键 显示药品说明书
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gvDetail_MouseDown(object sender, MouseEventArgs e)
        {
            if (!e.Button.Equals(MouseButtons.Right)) return;
            // 非单击有效行时返回
            GridHitInfo hitInfo = (gvDetail.CalcHitInfo((e as MouseEventArgs).Location));
            if (!(hitInfo != null && (hitInfo.InRow || hitInfo.InRowCell) && hitInfo.RowHandle >= 0)) return;
            if (/*Parameter.ISPASS.Equals("1")*/false && hitInfo.Column.FieldName.Equals("DRUG_NAME"))
            {
                string drugCode = this.gvDetail.GetDataRow(hitInfo.RowHandle)["DRUG_CODE"].ToString();
                if (!Global.StringIsNull(drugCode))
                {
                    this.ClickedAndCheck(hitInfo.RowHandle);
                    Tjhis.Oboutp.Station.Comm.ReadCard.CardReadAPI.MDC_CloseDrugHint();

                    this.PassQueryOrCheck(110, hitInfo.RowHandle);
                    gcDetail.ContextMenuStrip = this.medicomMenu;
                    medicomMenu.Show(MousePosition.X, MousePosition.Y);
                }
            }
        }

        /// <summary>
        /// 药品单击事件 (执行gvDetail_MouseDown就会再执行gvDetail_Click了)
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gvDetail_Click(object sender, EventArgs e)
        {
            if (/*Parameter.ISPASS.Equals("1")*/false)
                Tjhis.Oboutp.Station.Comm.ReadCard.CardReadAPI.MDC_CloseDrugHint();

            // 非单击有效行时返回
            GridHitInfo hitInfo = (gvDetail.CalcHitInfo((e as MouseEventArgs).Location));
            if (!(hitInfo != null && (hitInfo.InRow || hitInfo.InRowCell) && hitInfo.RowHandle >= 0)) return;

            if (hitInfo.Column == null) return;

            if (/*Parameter.ISPASS.Equals("1")*/false)
            {
                if (hitInfo.Column.FieldName == "PACKAGE_SPEC" || hitInfo.Column.FieldName == "FIRM_ID")
                {
                    this.ClickedAndCheck(hitInfo.RowHandle);
                }
                else if (hitInfo.Column.FieldName.Equals("NWARN"))
                {
                    if (this.gvDetail.GetDataRow(hitInfo.RowHandle)["NWARN"].ToInt(0) > 1)
                        this.PassQueryOrCheck(6, hitInfo.RowHandle);
                }
                else
                {
                    Tjhis.Oboutp.Station.Comm.ReadCard.CardReadAPI.MDC_CloseDrugHint();
                }
            }
        }

        private void drugExplain_Click(object sender, EventArgs e)
        {
            Tjhis.Oboutp.Station.Comm.ReadCard.CardReadAPI.MDC_DoRefDrug(11);
            this.gcDetail.ContextMenuStrip = null;
        }
        /// <summary>
        /// 收费按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButtonCharge_Click(object sender, EventArgs e)
        {
            //TJHIS.ObDoctor.Charge.frmOBCharge frmOBCharge = new TJHIS.ObDoctor.Charge.frmOBCharge(PatientInfo.PatientId, PatientInfo.ObVisitNo);
            //frmOBCharge.ShowDialog();
        }



        private void gcDetail_ProcessGridKey(object sender, KeyEventArgs e)
        {
            if (e.KeyData == Keys.Enter)
            {
                if (this.gvDetail.FocusedColumn.FieldName.Equals("FREQ_DETAIL"))
                {
                    if (this.gvDetail.FocusedRowHandle == this.gvDetail.RowCount - 1)
                    {
                        if (btAdd.Enabled)
                            btAdd_Click(null, null);
                    }
                    else
                    {
                        this.gvDetail.MoveNext();
                        this.gvDetail.FocusedColumn = this.gvDetail.Columns["DRUG_NAME"];
                    }
                }
                else
                    SendKeys.Send("{Tab}");
            }
        }

        #endregion 事件

        private void gvList_ShowingEditor(object sender, CancelEventArgs e)
        {
            try
            {
                DataRow dr = gvList.GetFocusedDataRow();
                if (dr == null) return;
                if (!dr.RowState.Equals(DataRowState.Added))
                {
                    e.Cancel = true;
                }
                //if (dr["ORDER_STATUS"].ToString("") != "5")
                //{
                //    e.Cancel = true;
                //}
                //else
                //{
                //    if (gvList.FocusedColumn.FieldName == "FREQUENCY" && dr["REPEAT_INDICATOR"].ToString("") == "临")
                //    {
                //        e.Cancel = true;
                //    }
                //}
            }
            catch (Exception ex)
            {
                UIMessageBox.Warn(ex.ToString());
            }
        }


        private void repositoryItemDateEdit1_EditValueChanged(object sender, EventArgs e)
        {
            DateEdit de = (DateEdit)sender;
            DateTime dePRESC_DATE = de.EditValue.ToString().ToDateTime();
            foreach (DataRow dr in dtSaveDetail.Rows)
            {
                if (dr.RowState.Equals(DataRowState.Deleted))
                {
                    continue;
                }
                dr["PRESC_DATE"] = dePRESC_DATE;
            }
        }

        private void btClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
    public class PrescInfo
    {
        public int PRESC_NO { get; set; }
        public DateTime PRESC_DATE { get; set; }
    }
}