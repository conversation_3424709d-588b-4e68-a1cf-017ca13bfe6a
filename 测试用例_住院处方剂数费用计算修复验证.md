# 住院处方剂数费用计算BUG修复验证

## 问题描述
住院处方中药剂数的乘法算法问题：开4剂药，保存后只能收到1剂的钱。

## 修复内容

### 1. 住院医生站 (Tjhis_Doctor_Station)
**文件**: `TjhisPlatSource\Tjhis_Doctor_Station\Business\Presc\frmPrescMain.cs`

**修改内容**:
- 在 `DetailComputeItemMoney` 方法中添加详细的调试日志
- 新增 `RecalculateCostBeforeSave` 方法，在保存前重新计算费用
- 在 `PrescSave` 方法中调用费用重新计算

### 2. 住院急诊站 (Tjhis_ObOutp_Station)  
**文件**: `TjhisPlatSource\Tjhis_ObOutp_Station\Pres\FrmPrescDoctInput.cs`

**修改内容**:
- 新增 `RecalculateCostBeforeSave` 方法，确保剂数信息正确应用
- 在 `btSave_Click` 方法中调用费用重新计算

## 测试步骤

### 测试前准备
1. 确保日志目录存在：`..\Client\LOG\exLOG\`
2. 准备测试数据：选择一个草药处方进行测试

### 测试用例1：新建草药处方
1. 打开住院医生站
2. 新建草药处方
3. 添加药品：如"麦冬 15g"
4. 设置剂数：4剂
5. 保存处方
6. 检查费用计算：
   - 预期：总数量 = 15g × 4剂 = 60g
   - 预期：总费用 = 60g × 单价
   - 实际：查看界面显示和数据库保存的费用是否一致

### 测试用例2：修改现有处方剂数
1. 打开已保存的草药处方
2. 修改剂数：从2剂改为5剂
3. 保存处方
4. 检查费用是否按新剂数重新计算

### 测试用例3：验证日志记录
1. 执行上述测试后，检查日志文件：
   - `住院处方费用计算_YYYYMMDD.log`
   - `住院处方费用重新计算_YYYYMMDD.log`
2. 验证日志中记录的数量和费用计算过程

## 验证要点

### 1. 数量计算验证
```
输入数量 = Math.Ceiling(Math.Round((单次剂量 × 剂数) / 最小单位剂量, 2))
```

### 2. 费用计算验证
```
费用 = 单价 × 输入数量
应收费用 = 应收单价 × 输入数量
```

### 3. 日志验证
检查日志中是否包含：
- 药品名称
- 单次剂量
- 剂数
- 重新计算的数量
- 计算的费用

## 预期结果
1. 开4剂药，收费应该是4剂的费用
2. 修改剂数后，费用能正确重新计算
3. 界面显示的费用与数据库保存的费用一致
4. 日志文件正确记录计算过程

## 回滚方案
如果修复出现问题，可以：
1. 注释掉 `RecalculateCostBeforeSave()` 调用
2. 恢复原始的费用计算逻辑
3. 删除添加的日志记录代码

## 注意事项
1. 此修复只影响草药处方（PRESC_TYPE = "1"）
2. 西药处方不受影响
3. 日志文件按日期分割，便于查看和管理
4. 异常情况下日志记录失败不会影响主流程
