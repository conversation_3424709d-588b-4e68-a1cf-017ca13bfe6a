﻿using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Text;
using Tjhis.Outpdoct.Station.Dal;
using Tjhis.Outpdoct.Station.Interface;
using Tjhis.Outpdoct.Station.Model;

namespace Tjhis.Outpdoct.Station.Business
{
    /// <summary>
    /// 正常664版本数据
    /// </summary>
    public class OrderDal : IOrderVersion
    {
        private OracleDataHelper db = new OracleDataHelper();

        public int GetMaxItemNo(string serialNO, string OrderClass = "")
        {
            return 0;
        }

        public List<OUTP_ORDERS_COSTS_STANDARD> GetOrderCostsList(string patientId, string clinicNo, DateTime visitDate, int visitNo)
        {
            string sql = @"SELECT CLINIC_NO,
                                  ORDER_NO,
                                  ORDER_SUB_NO,
                                  PATIENT_ID,
                                  ORDER_CLASS,
                                  ITEM_NO,
                                  SERIAL_NO,
                                  OUTP_SERIAL_NO,
                                  VISIT_DATE,
                                  VISIT_NO,
                                  OB_VISIT_ID,
                                  ITEM_CLASS,
                                  ITEM_NAME,
                                  ITEM_CODE,
                                  ITEM_SPEC,
                                  UNITS,
                                  REPETITION,
                                  AMOUNT,
                                  ORDERED_BY_DEPT,
                                  ORDERED_BY_DOCTOR,
                                  PERFORMED_BY,
                                  CLASS_ON_RCPT,
                                  COSTS,
                                  CHARGES,
                                  DIAG_DESC,
                                  RCPT_NO,
                                  CHARGE_INDICATOR,
                                  CLASS_ON_RECKONING,
                                  SUBJ_CODE,
                                  PRICE_QUOTIETY,
                                  ITEM_PRICE,
                                  BILL_DATE,
                                  SKINTEST,
                                  PRESC_PSNO,
                                  INSURANCE_FLAG,
                                  INSURANCE_CONSTRAINED_LEVEL,
                                  OPER_DATE,
                                  OPER_ID,
                                  PACKAGE_NO,
                                  INSUR_SH,
                                  TJ_LSH,
                                  YPXZBS,
                                  SKIN_SAVE,
                                  SKIN_START,
                                  SKIN_BATH,
                                  SKIN_OPER_NO,
                                  SKIN_FLAG,
                                  HIS_UNIT_CODE,
                                  INSUR_ADULT,
                                  CLWZM,
                                  CLXZBS,
                                  TRADE_PRICE,
                                  BATCH_CODE,
                                  BATCH_NO,
                                  GUID,
                                  ORDERED_NURSE,
                                  RECIPETYPE
                                  FROM OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD";
            string sqlWhere = " WHERE CLINIC_NO=:CLINIC_NO";
            List<DbParameter> dbParameters = new List<DbParameter>
            {
                db.CreateDbParameter(":CLINIC_NO",clinicNo)
            };
            return db.GetModels<OUTP_ORDERS_COSTS_STANDARD>(string.Concat(sql, sqlWhere), dbParameters);
        }

        public List<OUTP_ORDERS_STANDARD> GetOrdersList(string patientId, string clinicNo, DateTime visitDate, int visitNo)
        {
            string sql = @"SELECT PATIENT_ID,
                                  CLINIC_NO,
                                  ORDER_NO,
                                  ORDER_SUB_NO,
                                  ITEM_NO,
                                  VISIT_DATE,
                                  VISIT_NO,
                                  OB_VISIT_ID,
                                  OUTPDOCT.OUTP_ORDERS_STANDARD.SERIAL_NO,
                                  OUTP_SERIAL_NO,
                                  OUTPDOCT.OUTP_ORDERS_STANDARD.ORDER_CODE,
                                  ORDER_TEXT,
                                  ORDER_CLASS,
                                  AMOUNT,
                                  FREQUENCY,
                                  ADMINISTRATION,
                                  DOSAGE_UNITS,
                                  DOSAGE,
                                  REPETITION,
                                  UNITS,
                                  FIRM_ID,
                                  ITEM_SPEC,
                                  COSTS,
                                  CHARGES,
                                  ORDERED_BY,
                                  DOCTOR,
                                  ORDER_DATE,
                                  DOCTOR_NO,
                                  PERFORMED_BY,
                                  DIAGNOSIS_DESC,
                                  PRESC_PSNO,
                                  APPOINT_NO,
                                  RCPT_NO,
                                  PRESC_ATTR,
                                  OPSP_DISE_CODE,
                                  SPLIT_FLAG,
                                  SKIN_RESULT,
                                  FREQ_DETAIL,
                                  PERFORM_TIMES,
                                  ABIDANCE,
                                  CHARGE_INDICATOR,
                                  NURSE,
                                  BATCH_NO,
                                  USAGE_DESC,
                                  CLINIC_SPECIFIC,
                                  DECOCTION,
                                  COUNT_PER_REPETITION,
                                  EXECUTE_NURSE,
                                  EXECUTE_TIME,
                                  ORDERED_NURSE,
                                  CHECK_FLAG,
                                  TREAT_ITEM,
                                  SKIN_FLAG,
                                  SIGNATURE_NO,
                                  GETDRUG_FLAG,
                                  DOSE_PER_UNIT,
                                  BJCA_CN,
                                  BJCA_VALUE,
                                  BJCA_TIME,
                                  OUTPDOCT.OUTP_ORDERS_STANDARD.HIS_UNIT_CODE,
                                  TJ_LSH,
                                  TRADE_PRICE,
                                  BATCH_CODE,
                                  GUID,
                                  ITEM_PRICE,
                                  ORDERED_NURSE_NO,
                                  EXECUTE_STATUS,
                                  RECIPETYPE,
                                  PRINT_STATUS,
                                  ONLINE_PRESC_NO,
                                  DEPT_DICT.DEPT_NAME AS PERFORMED_BY_NAME,HERBAL_RULES_OF_TREATMENT,HERBAL_EXTRACT_JUICE_QUANTITY,HERBAL_DOSAGE,CPRESC_NAME,INSUR_CODE,INSUR_NAME,REASON_FOR_MEDICATION,DRUG_SPEC,PRESC_COMM_TABOO,SPECIAL_REQUEST
                                  FROM OUTPDOCT.OUTP_ORDERS_STANDARD LEFT JOIN DEPT_DICT ON OUTPDOCT.OUTP_ORDERS_STANDARD.PERFORMED_BY=DEPT_DICT.DEPT_CODE";
            string sqlWhere = " WHERE CLINIC_NO=:CLINIC_NO ORDER BY ORDER_NO,ORDER_SUB_NO";
            List<DbParameter> dbParameters = new List<DbParameter>
            {
                db.CreateDbParameter(":CLINIC_NO",clinicNo)
            };

            return db.GetModels<OUTP_ORDERS_STANDARD>(string.Concat(sql, sqlWhere), dbParameters);
        }

        public void SetDeleteOrderCostsSql(ref List<string> sqlLst, ref List<DbParameter[]> dbParametersLst, OUTP_ORDERS_COSTS_STANDARD costs)
        {
            string sql = " delete from  OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD  where his_unit_code =:his_unit_code and  CLINIC_NO =:CLINIC_NO and ORDER_NO=:OrderNo and ORDER_SUB_NO =:OrderSubNo";
            DbParameter[] parameters = new DbParameter[]
            {
                db.CreateDbParameter(":his_unit_code",SystemParm.HisUnitCode),
                db.CreateDbParameter(":CLINIC_NO",costs.CLINIC_NO),
                db.CreateDbParameter(":OrderNo",costs.ORDER_NO),
                db.CreateDbParameter(":OrderSubNo",costs.ORDER_SUB_NO)
            };
            sqlLst.Add(sql);
            dbParametersLst.Add(parameters);
            sql = " delete from  OUTPDOCT.IND_OUTP_ORDERS_COSTS  where his_unit_code =:his_unit_code and  CLINIC_NO =:CLINIC_NO and ORDER_NO=:OrderNo and ORDER_SUB_NO =:OrderSubNo";
            parameters = new DbParameter[]
            {
                db.CreateDbParameter(":his_unit_code",SystemParm.HisUnitCode),
                db.CreateDbParameter(":CLINIC_NO",costs.CLINIC_NO),
                db.CreateDbParameter(":OrderNo",costs.ORDER_NO),
                db.CreateDbParameter(":OrderSubNo",costs.ORDER_SUB_NO)
            };
            sqlLst.Add(sql);
            dbParametersLst.Add(parameters);
        }

        public void SetDeleteOrderOneCostSql(ref List<string> sqlLst, ref List<DbParameter[]> dbParametersLst, OUTP_ORDERS_COSTS_STANDARD costs,decimal? charges)
        {
            string sql = " delete from  OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD  where his_unit_code =:his_unit_code and  CLINIC_NO =:CLINIC_NO and ORDER_NO=:OrderNo and ORDER_SUB_NO =:OrderSubNo and ITEM_NO=:ItemNo";
            DbParameter[] parameters = new DbParameter[]
            {
                db.CreateDbParameter(":his_unit_code",SystemParm.HisUnitCode),
                db.CreateDbParameter(":CLINIC_NO",costs.CLINIC_NO),
                db.CreateDbParameter(":OrderNo",costs.ORDER_NO),
                db.CreateDbParameter(":OrderSubNo",costs.ORDER_SUB_NO),
                db.CreateDbParameter(":ItemNo",costs.ITEM_NO)
            };
            sqlLst.Add(sql);
            dbParametersLst.Add(parameters);
            sql = " delete from  OUTPDOCT.IND_OUTP_ORDERS_COSTS  where his_unit_code =:his_unit_code and  CLINIC_NO =:CLINIC_NO and ORDER_NO=:OrderNo and ORDER_SUB_NO =:OrderSubNo and ITEM_NO=:ItemNo";
            parameters = new DbParameter[]
            {
                db.CreateDbParameter(":his_unit_code",SystemParm.HisUnitCode),
                db.CreateDbParameter(":CLINIC_NO",costs.CLINIC_NO),
                db.CreateDbParameter(":OrderNo",costs.ORDER_NO),
                db.CreateDbParameter(":OrderSubNo",costs.ORDER_SUB_NO),
                db.CreateDbParameter(":ItemNo",costs.ITEM_NO)
            };
            sqlLst.Add(sql);
            dbParametersLst.Add(parameters);
            //sql = string.Format("update OUTP_ORDERS_STANDARD set COSTS={0},CHARGES={0} where his_unit_code =:his_unit_code and  CLINIC_NO =:CLINIC_NO and ORDER_NO=:OrderNo and ORDER_SUB_NO =:OrderSubNo", charges);

            //2025-8-26 数据一致性最好：直接从明细表汇总，避免内存计算误差
            sql = @"UPDATE OUTP_ORDERS_STANDARD os
                        SET (COSTS, CHARGES) = (
                            SELECT NVL(SUM(COSTS), 0), NVL(SUM(CHARGES), 0)
                            FROM OUTP_ORDERS_COSTS_STANDARD
                            WHERE CLINIC_NO = os.CLINIC_NO
                              AND ORDER_NO = os.ORDER_NO
                              AND ORDER_SUB_NO = os.ORDER_SUB_NO
                        )
                        WHERE his_unit_code = :his_unit_code
                          AND CLINIC_NO = :CLINIC_NO
                          AND ORDER_NO = :OrderNo
                          AND ORDER_SUB_NO = :OrderSubNo";

            parameters = new DbParameter[]
            {
                db.CreateDbParameter(":his_unit_code",SystemParm.HisUnitCode),
                db.CreateDbParameter(":CLINIC_NO",costs.CLINIC_NO),
                db.CreateDbParameter(":OrderNo",costs.ORDER_NO),
                db.CreateDbParameter(":OrderSubNo",costs.ORDER_SUB_NO)
            };
            sqlLst.Add(sql);
            dbParametersLst.Add(parameters);
        }

        public void SetDeleteOrderSql(ref List<string> sqlLst, ref List<DbParameter[]> dbParametersLst, OUTP_ORDERS_STANDARD order)
        {
            string sql = "DELETE FROM  OUTPDOCT.OUTP_ORDERS_STANDARD  WHERE his_unit_code = :HisUnitCode and  CLINIC_NO = :ClinicNo and ORDER_NO =:OrderNo and ORDER_SUB_NO =:OrderSubNo";
            DbParameter[] parameters = new DbParameter[]
            {
                db.CreateDbParameter(":HisUnitCode",SystemParm.HisUnitCode),
                db.CreateDbParameter(":ClinicNo",order.CLINIC_NO),
                db.CreateDbParameter(":OrderNo",order.ORDER_NO),
                db.CreateDbParameter(":OrderSubNo",order.ORDER_SUB_NO)
            };
            sqlLst.Add(sql);
            dbParametersLst.Add(parameters);
        }

        public void SetInsertOrderCostsSql(ref List<string> sqlLst, ref List<DbParameter[]> dbParametersLst, OUTP_ORDERS_COSTS_STANDARD costs)
        {
            string sqlor = "Insert Into OUTP_ORDERS_COSTS_STANDARD(PATIENT_ID  , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO, ";
            sqlor += "ITEM_NO , ITEM_CLASS, ITEM_NAME , ITEM_CODE , ITEM_SPEC , UNITS, REPETITION , AMOUNT,";
            sqlor += "CLASS_ON_RCPT, COSTS , CHARGES ,RCPT_NO ,CHARGE_INDICATOR ,  CLASS_ON_RECKONING ,SUBJ_CODE ,";
            sqlor += "PRICE_QUOTIETY ,ITEM_PRICE  , INSURANCE_FLAG , ";
            sqlor += "CLXZBS  ,CLWZM ,HIS_UNIT_CODE, OUTP_SERIAL_NO,ORDER_CLASS,PERFORMED_BY, SERIAL_NO, ";
            sqlor += "  VISIT_DATE , VISIT_NO,YPXZBS,ORDERED_BY_DEPT,ORDERED_BY_DOCTOR ";
            sqlor += " , TRADE_PRICE	,BATCH_CODE	,BATCH_NO	,GUID ,RECIPETYPE,INSUR_ADULT"; //20220402 
            sqlor += " )";
            sqlor += " Values (";
            sqlor += ":PATIENT_ID  , :CLINIC_NO     , :ORDER_NO    ,:ORDER_SUB_NO, ";
            sqlor += ":ITEM_NO , :ITEM_CLASS, :ITEM_NAME , :ITEM_CODE , :ITEM_SPEC , :UNITS, :REPETITION , :AMOUNT,";
            sqlor += ":CLASS_ON_RCPT, :COSTS , :CHARGES ,:RCPT_NO ,:CHARGE_INDICATOR ,  :CLASS_ON_RECKONING ,:SUBJ_CODE ,";
            sqlor += ":PRICE_QUOTIETY ,:ITEM_PRICE  , :INSURANCE_FLAG , ";
            sqlor += ":CLXZBS  ,:CLWZM ,:HIS_UNIT_CODE, :OUTP_SERIAL_NO,:ORDER_CLASS,:PERFORMED_BY, :SERIAL_NO, ";
            sqlor += "  :VISIT_DATE , :VISIT_NO,:YPXZBS,:ORDERED_BY_DEPT,:ORDERED_BY_DOCTOR ";
            sqlor += " , :TRADE_PRICE	,:BATCH_CODE	,:BATCH_NO	,:GUID ,:RECIPETYPE,:INSUR_ADULT"; //20220402 
            sqlor += " )";
            DbParameter[] dbs = new DbParameter[]
            {
                db.CreateDbParameter(":PATIENT_ID", typeof(string),            costs.PATIENT_ID),
                db.CreateDbParameter(":CLINIC_NO",  typeof(string), costs.CLINIC_NO),
                db.CreateDbParameter(":ORDER_NO",       typeof(string),        costs.ORDER_NO),
                db.CreateDbParameter(":ORDER_SUB_NO",    typeof(string),       costs.ORDER_SUB_NO),
                db.CreateDbParameter(":ITEM_NO",    typeof(string),            costs.ITEM_NO),
                db.CreateDbParameter(":ITEM_CLASS",  typeof(string),           costs.ITEM_CLASS),
                db.CreateDbParameter(":ITEM_NAME",    typeof(string),          costs.ITEM_NAME),
                db.CreateDbParameter(":ITEM_CODE",   typeof(string),           costs.ITEM_CODE),
                db.CreateDbParameter(":ITEM_SPEC",    typeof(string),          costs.ITEM_SPEC),
                db.CreateDbParameter(":UNITS",      typeof(string),            costs.UNITS),
                db.CreateDbParameter(":REPETITION",  typeof(int),           costs.REPETITION),
                db.CreateDbParameter(":AMOUNT",      typeof(decimal),           costs.AMOUNT),
                db.CreateDbParameter(":CLASS_ON_RCPT",    typeof(string),      costs.CLASS_ON_RCPT),
                db.CreateDbParameter(":COSTS",      typeof(decimal),            costs.COSTS),
                db.CreateDbParameter(":CHARGES",    typeof(decimal),            costs.CHARGES),
                db.CreateDbParameter(":RCPT_NO",      typeof(string),          costs.RCPT_NO),
                db.CreateDbParameter(":CHARGE_INDICATOR",    typeof(int),   costs.CHARGE_INDICATOR),
                db.CreateDbParameter(":CLASS_ON_RECKONING",  typeof(string),   costs.CLASS_ON_RECKONING),
                db.CreateDbParameter(":SUBJ_CODE",     typeof(string),         costs.SUBJ_CODE),
                db.CreateDbParameter(":PRICE_QUOTIETY",   typeof(decimal),      costs.PRICE_QUOTIETY),
                db.CreateDbParameter(":ITEM_PRICE",      typeof(decimal),       costs.ITEM_PRICE),
                db.CreateDbParameter(":INSURANCE_FLAG",   typeof(string),      costs.INSURANCE_FLAG),
                db.CreateDbParameter(":CLXZBS",     typeof(string),            costs.CLXZBS),
                db.CreateDbParameter(":CLWZM",       typeof(string),           costs.CLWZM),
                db.CreateDbParameter(":HIS_UNIT_CODE",    typeof(string),      costs.HIS_UNIT_CODE),
                db.CreateDbParameter(":OUTP_SERIAL_NO",   typeof(string),      costs.OUTP_SERIAL_NO),
                db.CreateDbParameter(":ORDER_CLASS",     typeof(string),       costs.ORDER_CLASS),
                db.CreateDbParameter(":PERFORMED_BY",   typeof(string),        costs.PERFORMED_BY),
                db.CreateDbParameter(":SERIAL_NO",         typeof(string),     costs.SERIAL_NO),
                db.CreateDbParameter(":VISIT_DATE",   typeof(DateTime),          costs.VISIT_DATE),
                db.CreateDbParameter(":VISIT_NO",     typeof(decimal),          costs.VISIT_NO),
                db.CreateDbParameter(":YPXZBS",        typeof(string),         costs.YPXZBS),
                db.CreateDbParameter(":ORDERED_BY_DEPT",   typeof(string),     costs.ORDERED_BY_DEPT),
                db.CreateDbParameter(":ORDERED_BY_DOCTOR",  typeof(string),    costs.ORDERED_BY_DOCTOR),
                db.CreateDbParameter(":TRADE_PRICE",  typeof(decimal),          costs.TRADE_PRICE),
                db.CreateDbParameter(":BATCH_CODE",    typeof(string),         costs.BATCH_CODE),
                db.CreateDbParameter(":BATCH_NO",      typeof(string),         costs.BATCH_NO),
                db.CreateDbParameter(":GUID",           typeof(string),        costs.GUID),
                db.CreateDbParameter(":RECIPETYPE",  typeof(string),             costs.RECIPETYPE),
                db.CreateDbParameter(":INSUR_ADULT",  typeof(string),             costs.INSUR_ADULT)
            };
            dbParametersLst.Add(dbs);
            sqlLst.Add(sqlor);

            string sqlor1 = "Insert Into IND_OUTP_ORDERS_COSTS(PATIENT_ID  , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO, ";
            sqlor1 += "ITEM_NO , ITEM_CLASS, ITEM_NAME , ITEM_CODE , ITEM_SPEC , UNITS, REPETITION , AMOUNT,";
            sqlor1 += "CLASS_ON_RCPT, COSTS , CHARGES ,RCPT_NO ,CHARGE_INDICATOR ,  CLASS_ON_RECKONING ,SUBJ_CODE ,";
            sqlor1 += "PRICE_QUOTIETY ,ITEM_PRICE  , INSURANCE_FLAG , ";
            sqlor1 += "CLXZBS  ,CLWZM ,HIS_UNIT_CODE, OUTP_SERIAL_NO,ORDER_CLASS,VISIT_DATE,VISIT_NO ,PERFORMED_BY,YPXZBS ,";
            sqlor1 += " TRADE_PRICE,BATCH_CODE,GUID,BATCH_NO,RECIPETYPE,INSUR_ADULT) ";
            sqlor1 += " Values (";
            sqlor1 += ":PATIENT_ID  , :CLINIC_NO     , :ORDER_NO    ,:ORDER_SUB_NO, ";
            sqlor1 += ":ITEM_NO,:ITEM_CLASS,:ITEM_NAME,:ITEM_CODE,:ITEM_SPEC,:UNITS,:REPETITION,:AMOUNT,";
            sqlor1 += ":CLASS_ON_RCPT,:COSTS,:CHARGES,:RCPT_NO,:CHARGE_INDICATOR,:CLASS_ON_RECKONING,:SUBJ_CODE,";
            sqlor1 += ":PRICE_QUOTIETY,:ITEM_PRICE,:INSURANCE_FLAG,";
            sqlor1 += ":CLXZBS,:CLWZM,:HIS_UNIT_CODE,:OUTP_SERIAL_NO,:ORDER_CLASS,:VISIT_DATE,:VISIT_NO,:PERFORMED_BY,:YPXZBS,";
            sqlor1 += ":TRADE_PRICE,:BATCH_CODE,:GUID,:BATCH_NO,:RECIPETYPE,:INSUR_ADULT";
            sqlor1 += ")";
            dbs = new DbParameter[]
            {
                db.CreateDbParameter(":PATIENT_ID", typeof(string),            costs.PATIENT_ID),
                db.CreateDbParameter(":CLINIC_NO",  typeof(string), costs.CLINIC_NO),
                db.CreateDbParameter(":ORDER_NO",       typeof(string),        costs.ORDER_NO),
                db.CreateDbParameter(":ORDER_SUB_NO",    typeof(string),       costs.ORDER_SUB_NO),

                db.CreateDbParameter(":ITEM_NO",typeof(string),costs.ITEM_NO),
                db.CreateDbParameter(":ITEM_CLASS",typeof(string),costs.ITEM_CLASS),
                db.CreateDbParameter(":ITEM_NAME",typeof(string),costs.ITEM_NAME),
                db.CreateDbParameter(":ITEM_CODE",typeof(string),costs.ITEM_CODE),
                db.CreateDbParameter(":ITEM_SPEC",typeof(string),costs.ITEM_SPEC),
                db.CreateDbParameter(":UNITS",typeof(string),costs.UNITS),
                db.CreateDbParameter(":REPETITION",typeof(int),costs.REPETITION),
                db.CreateDbParameter(":AMOUNT",typeof(decimal),costs.AMOUNT),
                db.CreateDbParameter(":CLASS_ON_RCPT",typeof(string),costs.CLASS_ON_RCPT),
                db.CreateDbParameter(":COSTS",typeof(decimal),costs.COSTS),
                db.CreateDbParameter(":CHARGES",typeof(decimal),costs.CHARGES),
                db.CreateDbParameter(":RCPT_NO",typeof(string),costs.RCPT_NO),
                db.CreateDbParameter(":CHARGE_INDICATOR",typeof(int),costs.CHARGE_INDICATOR),
                db.CreateDbParameter(":CLASS_ON_RECKONING",typeof(string),costs.CLASS_ON_RECKONING),
                db.CreateDbParameter(":SUBJ_CODE",typeof(string),costs.SUBJ_CODE),
                db.CreateDbParameter(":PRICE_QUOTIETY",typeof(decimal),costs.PRICE_QUOTIETY),
                db.CreateDbParameter(":ITEM_PRICE",typeof(decimal),costs.ITEM_PRICE),
                db.CreateDbParameter(":INSURANCE_FLAG",typeof(string),costs.INSURANCE_FLAG),
                db.CreateDbParameter(":CLXZBS",typeof(string),costs.CLXZBS),
                db.CreateDbParameter(":CLWZM",typeof(string),costs.CLWZM),
                db.CreateDbParameter(":HIS_UNIT_CODE",typeof(string),costs.HIS_UNIT_CODE),
                db.CreateDbParameter(":OUTP_SERIAL_NO",typeof(string),costs.OUTP_SERIAL_NO),
                db.CreateDbParameter(":ORDER_CLASS",typeof(string),costs.ORDER_CLASS),
                db.CreateDbParameter(":VISIT_DATE",typeof(DateTime),costs.VISIT_DATE),
                db.CreateDbParameter(":VISIT_NO",typeof(int),costs.VISIT_NO),
                db.CreateDbParameter(":PERFORMED_BY",typeof(string),costs.PERFORMED_BY),
                db.CreateDbParameter(":YPXZBS",typeof(string),costs.YPXZBS),
                db.CreateDbParameter(":TRADE_PRICE",typeof(decimal),costs.TRADE_PRICE),
                db.CreateDbParameter(":BATCH_CODE",typeof(string),costs.BATCH_CODE),
                db.CreateDbParameter(":GUID",typeof(string),costs.GUID),
                db.CreateDbParameter(":BATCH_NO",typeof(string),costs.BATCH_NO),
                db.CreateDbParameter(":RECIPETYPE",typeof(string),costs.RECIPETYPE),
                db.CreateDbParameter(":INSUR_ADULT",typeof(string),costs.INSUR_ADULT),
            };
            dbParametersLst.Add(dbs);
            sqlLst.Add(sqlor1);
        }

        public void SetInsertOrderSql(ref List<string> sqlLst, ref List<DbParameter[]> dbParametersLst, OUTP_ORDERS_STANDARD order)
        {
            string sqlor = "Insert Into OUTP_ORDERS_STANDARD(PATIENT_ID  , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO, ";
            sqlor += "ORDERED_BY  ,DOCTOR   ,ORDER_DATE  ,DOCTOR_NO , NURSE  , SIGNATURE_NO , USAGE_DESC  , DIAGNOSIS_DESC ,";
            sqlor += "HIS_UNIT_CODE ,OUTP_SERIAL_NO  , APPOINT_NO , ORDER_CLASS, ORDER_TEXT , ORDER_CODE ,DOSAGE ,DOSAGE_UNITS ,";
            sqlor += "ADMINISTRATION , FREQUENCY ,Skin_Flag, SKIN_RESULT, PRESC_PSNO  , AMOUNT   , PERFORMED_BY  ,COSTS   ,CHARGES,";
            sqlor += " CHARGE_INDICATOR,SPLIT_FLAG,ITEM_SPEC,FIRM_ID,REPETITION,PRESC_ATTR,OPSP_DISE_CODE,UNITS,ABIDANCE,PERFORM_TIMES,";
            sqlor += " FREQ_DETAIL,BATCH_NO,DOSE_PER_UNIT,SERIAL_NO,";
            sqlor += "  TRADE_PRICE ,BATCH_CODE	,GUID	,ITEM_PRICE  , ";
            sqlor += "BJCA_CN,BJCA_VALUE,BJCA_TIME ,DECOCTION, COUNT_PER_REPETITION,VISIT_DATE ,VISIT_NO,RECIPETYPE, ";
            sqlor += "HERBAL_EXTRACT_JUICE_QUANTITY,HERBAL_DOSAGE,HERBAL_RULES_OF_TREATMENT,CPRESC_NAME,INSUR_CODE,INSUR_NAME,REASON_FOR_MEDICATION,DRUG_SPEC";
            sqlor += " ,PRESC_COMM_TABOO,SPECIAL_REQUEST ) ";
            sqlor += " Values (";
            sqlor += ":PATIENT_ID  , :CLINIC_NO, :ORDER_NO,:ORDER_SUB_NO,";
            sqlor += ":ORDERED_BY  ,:DOCTOR   ,:ORDER_DATE  ,:DOCTOR_NO , :NURSE  , :SIGNATURE_NO , :USAGE_DESC  , :DIAGNOSIS_DESC ,";
            sqlor += ":HIS_UNIT_CODE ,:OUTP_SERIAL_NO  , :APPOINT_NO , :ORDER_CLASS, :ORDER_TEXT , :ORDER_CODE ,:DOSAGE ,:DOSAGE_UNITS ,";
            sqlor += ":ADMINISTRATION , :FREQUENCY ,:SKIN_FLAG, :SKIN_RESULT, :PRESC_PSNO  , :AMOUNT   , :PERFORMED_BY  ,:COSTS   ,:CHARGES,";
            sqlor += " :CHARGE_INDICATOR,:SPLIT_FLAG,:ITEM_SPEC,:FIRM_ID,:REPETITION,:PRESC_ATTR, :OPSP_DISE_CODE,:UNITS,:ABIDANCE,:PERFORM_TIMES,";
            sqlor += " :FREQ_DETAIL,:BATCH_NO,:DOSE_PER_UNIT,:SERIAL_NO,";
            sqlor += "  :TRADE_PRICE ,:BATCH_CODE	,:GUID	,:ITEM_PRICE  , ";
            sqlor += ":BJCA_CN,:BJCA_VALUE,:BJCA_TIME ,:DECOCTION, :COUNT_PER_REPETITION,:VISIT_DATE ,:VISIT_NO,:RECIPETYPE, ";
            sqlor += ":HERBAL_EXTRACT_JUICE_QUANTITY,:HERBAL_DOSAGE,:HERBAL_RULES_OF_TREATMENT,:CPRESC_NAME,:INSUR_CODE,:INSUR_NAME,:REASON_FOR_MEDICATION,:DRUG_SPEC,:PRESC_COMM_TABOO,:SPECIAL_REQUEST)";
            sqlLst.Add(sqlor);
            DbParameter[] dbs = new DbParameter[]
            {
                this.db.CreateDbParameter(":PATIENT_ID",typeof(string),order.PATIENT_ID),
                this.db.CreateDbParameter(":CLINIC_NO",typeof(string),order.CLINIC_NO),
                this.db.CreateDbParameter(":ORDER_NO",typeof(decimal), order.ORDER_NO),
                this.db.CreateDbParameter(":ORDER_SUB_NO",typeof(decimal), order.ORDER_SUB_NO),
                this.db.CreateDbParameter(":ORDERED_BY",typeof(string), order.ORDERED_BY),
                this.db.CreateDbParameter(":DOCTOR",typeof(string), order.DOCTOR),
                this.db.CreateDbParameter(":ORDER_DATE",typeof(DateTime), order.ORDER_DATE),
                this.db.CreateDbParameter(":DOCTOR_NO",typeof(string), order.DOCTOR_NO),
                this.db.CreateDbParameter(":NURSE",typeof(string), order.NURSE),
                this.db.CreateDbParameter(":SIGNATURE_NO",typeof(decimal), order.SIGNATURE_NO),
                this.db.CreateDbParameter(":USAGE_DESC",typeof(string), order.USAGE_DESC),
                this.db.CreateDbParameter(":DIAGNOSIS_DESC",typeof(string), order.DIAGNOSIS_DESC),
                this.db.CreateDbParameter(":HIS_UNIT_CODE",typeof(string), order.HIS_UNIT_CODE),
                this.db.CreateDbParameter(":OUTP_SERIAL_NO", typeof(string),order.OUTP_SERIAL_NO),
                this.db.CreateDbParameter(":APPOINT_NO",typeof(string), order.APPOINT_NO),
                this.db.CreateDbParameter(":ORDER_CLASS", typeof(string),order.ORDER_CLASS),
                this.db.CreateDbParameter(":ORDER_TEXT", typeof(string),order.ORDER_TEXT),
                this.db.CreateDbParameter(":ORDER_CODE",typeof(string), order.ORDER_CODE),
                this.db.CreateDbParameter(":DOSAGE",typeof(decimal), order.DOSAGE),
                this.db.CreateDbParameter(":DOSAGE_UNITS",typeof(string), order.DOSAGE_UNITS),
                this.db.CreateDbParameter(":ADMINISTRATION",typeof(string), order.ADMINISTRATION),
                this.db.CreateDbParameter(":FREQUENCY",typeof(string), order.FREQUENCY),
                this.db.CreateDbParameter(":SKIN_FLAG",typeof(int), order.SKIN_FLAG),
                this.db.CreateDbParameter(":SKIN_RESULT",typeof(int), order.SKIN_RESULT),
                this.db.CreateDbParameter(":PRESC_PSNO",typeof(int), order.PRESC_PSNO),
                this.db.CreateDbParameter(":AMOUNT",typeof(decimal), order.AMOUNT),
                this.db.CreateDbParameter(":PERFORMED_BY",typeof(string), order.PERFORMED_BY),
                this.db.CreateDbParameter(":COSTS",typeof(decimal), order.COSTS),
                this.db.CreateDbParameter(":CHARGES",typeof(decimal), order.CHARGES),
                this.db.CreateDbParameter(":CHARGE_INDICATOR",typeof(int), order.CHARGE_INDICATOR),
                this.db.CreateDbParameter(":SPLIT_FLAG",typeof(int), order.SPLIT_FLAG),
                this.db.CreateDbParameter(":ITEM_SPEC",typeof(string), order.ITEM_SPEC),
                this.db.CreateDbParameter(":FIRM_ID",typeof(string), order.FIRM_ID),
                this.db.CreateDbParameter(":REPETITION",typeof(int), order.REPETITION),
                this.db.CreateDbParameter(":PRESC_ATTR",typeof(string), order.PRESC_ATTR),
                this.db.CreateDbParameter(":OPSP_DISE_CODE",typeof(string), order.OPSP_DISE_CODE),
                this.db.CreateDbParameter(":UNITS",typeof(string), order.UNITS),
                this.db.CreateDbParameter(":ABIDANCE",typeof(decimal), order.ABIDANCE),
                this.db.CreateDbParameter(":PERFORM_TIMES",typeof(decimal), order.PERFORM_TIMES),
                this.db.CreateDbParameter(":FREQ_DETAIL",typeof(string), order.FREQ_DETAIL),
                this.db.CreateDbParameter(":BATCH_NO",typeof(string), order.BATCH_NO),
                this.db.CreateDbParameter(":DOSE_PER_UNIT",typeof(decimal), order.DOSE_PER_UNIT),
                this.db.CreateDbParameter(":SERIAL_NO",typeof(string), order.SERIAL_NO),
                this.db.CreateDbParameter(":TRADE_PRICE",typeof(decimal), order.TRADE_PRICE),
                this.db.CreateDbParameter(":BATCH_CODE",typeof(string), order.BATCH_CODE),
                this.db.CreateDbParameter(":GUID",typeof(string), order.GUID),
                this.db.CreateDbParameter(":ITEM_PRICE",typeof(decimal), order.ITEM_PRICE),
                this.db.CreateDbParameter(":BJCA_CN",typeof(string), order.BJCA_CN),
                this.db.CreateDbParameter(":BJCA_VALUE",typeof(string), order.BJCA_VALUE),
                this.db.CreateDbParameter(":BJCA_TIME",typeof(string), order.BJCA_TIME),
                this.db.CreateDbParameter(":DECOCTION",typeof(string), order.DECOCTION),
                this.db.CreateDbParameter(":COUNT_PER_REPETITION",typeof(int), order.COUNT_PER_REPETITION),
                this.db.CreateDbParameter(":VISIT_DATE",typeof(DateTime), order.VISIT_DATE),
                this.db.CreateDbParameter(":VISIT_NO",typeof(int), order.VISIT_NO),
                this.db.CreateDbParameter(":RECIPETYPE",typeof(string), order.RECIPETYPE),
                this.db.CreateDbParameter(":HERBAL_EXTRACT_JUICE_QUANTITY",typeof(string), order.HERBAL_EXTRACT_JUICE_QUANTITY),
                this.db.CreateDbParameter(":HERBAL_DOSAGE",typeof(string), order.HERBAL_DOSAGE),
                this.db.CreateDbParameter(":HERBAL_RULES_OF_TREATMENT",typeof(string), order.HERBAL_RULES_OF_TREATMENT),
                this.db.CreateDbParameter(":CPRESC_NAME",typeof(string), order.CPRESC_NAME),
                this.db.CreateDbParameter(":INSUR_CODE",typeof(string), order.INSUR_CODE),
                this.db.CreateDbParameter(":INSUR_NAME",typeof(string), order.INSUR_NAME),
                this.db.CreateDbParameter(":REASON_FOR_MEDICATION",typeof(string), order.REASON_FOR_MEDICATION),
                this.db.CreateDbParameter(":DRUG_SPEC",typeof(string), order.DRUG_SPEC),
                this.db.CreateDbParameter(":PRESC_COMM_TABOO",typeof(string), order.PRESC_COMM_TABOO),
                this.db.CreateDbParameter(":SPECIAL_REQUEST",typeof(string), order.SPECIAL_REQUEST),
            };
            dbParametersLst.Add(dbs);
        }

        public void SetInsertOutpOrderSql(ref List<string> sqlLst, ref List<DbParameter[]> dbParametersLst, OUTP_ORDERS_STANDARD order)
        {

        }

        public void SetUpdateOrderCostsSql(ref List<string> sqlLst, ref List<DbParameter[]> dbParametersLst, OUTP_ORDERS_COSTS_STANDARD costs)
        {
            string sqlor = string.Format("Update OUTP_ORDERS_COSTS_STANDARD Set " +
                            "ITEM_CLASS=:ITEM_CLASS, ITEM_NAME=:ITEM_NAME, ITEM_CODE=:ITEM_CODE, ITEM_SPEC=:ITEM_SPEC, UNITS=:UNITS, REPETITION=:REPETITION, AMOUNT=:AMOUNT, " +
                            "CLASS_ON_RCPT=:CLASS_ON_RCPT, COSTS=:COSTS, CHARGES=:CHARGES, RCPT_NO=:RCPT_NO, CHARGE_INDICATOR=:CHARGE_INDICATOR, CLASS_ON_RECKONING=:CLASS_ON_RECKONING, SUBJ_CODE=:SUBJ_CODE," +
                            "PRICE_QUOTIETY=:PRICE_QUOTIETY, ITEM_PRICE=:ITEM_PRICE, INSURANCE_FLAG=:INSURANCE_FLAG, " +
                            "CLXZBS=:CLXZBS, CLWZM=:CLWZM, HIS_UNIT_CODE=:HIS_UNIT_CODE, OUTP_SERIAL_NO=:OUTP_SERIAL_NO, ORDER_CLASS=:ORDER_CLASS, PERFORMED_BY=:PERFORMED_BY, SERIAL_NO=:SERIAL_NO, " +
                            "VISIT_DATE={0}, VISIT_NO=:VISIT_NO, YPXZBS=:YPXZBS, ORDERED_BY_DEPT=:ORDERED_BY_DEPT, ORDERED_BY_DOCTOR=:ORDERED_BY_DOCTOR, " +
                            "TRADE_PRICE=:TRADE_PRICE, BATCH_CODE=:BATCH_CODE, BATCH_NO=:BATCH_NO, GUID=:GUID, RECIPETYPE=:RECIPETYPE, INSUR_ADULT=:INSUR_ADULT " +
                            "Where CLINIC_NO=:CLINIC_NO AND ORDER_NO=:ORDER_NO AND ORDER_SUB_NO=:ORDER_SUB_NO AND ITEM_NO=:ITEM_NO", "TO_DATE('" + costs.VISIT_DATE.ToString() + "', 'yyyy/MM/dd HH24:mi:ss')");
            DbParameter[] dbs = new DbParameter[]
            {
                db.CreateDbParameter(":ITEM_CLASS",  typeof(string),           costs.ITEM_CLASS),
                db.CreateDbParameter(":ITEM_NAME",    typeof(string),          costs.ITEM_NAME),
                db.CreateDbParameter(":ITEM_CODE",   typeof(string),           costs.ITEM_CODE),
                db.CreateDbParameter(":ITEM_SPEC",    typeof(string),          costs.ITEM_SPEC),
                db.CreateDbParameter(":UNITS",      typeof(string),            costs.UNITS),
                db.CreateDbParameter(":REPETITION",  typeof(int),           costs.REPETITION),
                db.CreateDbParameter(":AMOUNT",      typeof(decimal),           costs.AMOUNT),
                db.CreateDbParameter(":CLASS_ON_RCPT",    typeof(string),      costs.CLASS_ON_RCPT),
                db.CreateDbParameter(":COSTS",      typeof(decimal),            costs.COSTS),
                db.CreateDbParameter(":CHARGES",    typeof(decimal),            costs.CHARGES),
                db.CreateDbParameter(":RCPT_NO",      typeof(string),          costs.RCPT_NO),
                db.CreateDbParameter(":CHARGE_INDICATOR",    typeof(int),   costs.CHARGE_INDICATOR),
                db.CreateDbParameter(":CLASS_ON_RECKONING",  typeof(string),   costs.CLASS_ON_RECKONING),
                db.CreateDbParameter(":SUBJ_CODE",     typeof(string),         costs.SUBJ_CODE),
                db.CreateDbParameter(":PRICE_QUOTIETY",   typeof(decimal),      costs.PRICE_QUOTIETY),
                db.CreateDbParameter(":ITEM_PRICE",      typeof(decimal),       costs.ITEM_PRICE),
                db.CreateDbParameter(":INSURANCE_FLAG",   typeof(string),      costs.INSURANCE_FLAG),
                db.CreateDbParameter(":CLXZBS",     typeof(string),            costs.CLXZBS),
                db.CreateDbParameter(":CLWZM",       typeof(string),           costs.CLWZM),
                db.CreateDbParameter(":HIS_UNIT_CODE",    typeof(string),      costs.HIS_UNIT_CODE),
                db.CreateDbParameter(":OUTP_SERIAL_NO",   typeof(string),      costs.OUTP_SERIAL_NO),
                db.CreateDbParameter(":ORDER_CLASS",     typeof(string),       costs.ORDER_CLASS),
                db.CreateDbParameter(":PERFORMED_BY",   typeof(string),        costs.PERFORMED_BY),
                db.CreateDbParameter(":SERIAL_NO",         typeof(string),     costs.SERIAL_NO),
                db.CreateDbParameter(":VISIT_NO",     typeof(decimal),          costs.VISIT_NO),
                db.CreateDbParameter(":YPXZBS",        typeof(string),         costs.YPXZBS),
                db.CreateDbParameter(":ORDERED_BY_DEPT",   typeof(string),     costs.ORDERED_BY_DEPT),
                db.CreateDbParameter(":ORDERED_BY_DOCTOR",  typeof(string),    costs.ORDERED_BY_DOCTOR),
                db.CreateDbParameter(":TRADE_PRICE",  typeof(decimal),          costs.TRADE_PRICE),
                db.CreateDbParameter(":BATCH_CODE",    typeof(string),         costs.BATCH_CODE),
                db.CreateDbParameter(":BATCH_NO",      typeof(string),         costs.BATCH_NO),
                db.CreateDbParameter(":GUID",           typeof(string),        costs.GUID),
                db.CreateDbParameter(":RECIPETYPE",  typeof(string),             costs.RECIPETYPE),
                db.CreateDbParameter(":INSUR_ADULT",  typeof(string),             costs.INSUR_ADULT),
                db.CreateDbParameter(":CLINIC_NO",  typeof(string), costs.CLINIC_NO),
                db.CreateDbParameter(":ORDER_NO",       typeof(string),        costs.ORDER_NO),
                db.CreateDbParameter(":ORDER_SUB_NO",    typeof(string),       costs.ORDER_SUB_NO),
                db.CreateDbParameter(":ITEM_NO",    typeof(string),            costs.ITEM_NO),
            };
            dbParametersLst.Add(dbs);
            sqlLst.Add(sqlor);

            string sqlor1 = string.Format("Update IND_OUTP_ORDERS_COSTS Set " +
                                                "ITEM_CLASS={0},        ITEM_NAME={1},          ITEM_CODE={2},              ITEM_SPEC={3},      UNITS={4}, " + 
                                                "REPETITION={5},        AMOUNT={6},             CLASS_ON_RCPT={7},          COSTS={8},          CHARGES={9}, " +
                                                "RCPT_NO={10},          CHARGE_INDICATOR={11},  CLASS_ON_RECKONING={12},    SUBJ_CODE={13},     PRICE_QUOTIETY={14}, " +
                                                "ITEM_PRICE={15},       INSURANCE_FLAG={16},    CLXZBS={17},                CLWZM={18},         HIS_UNIT_CODE={19}, " +
                                                "OUTP_SERIAL_NO={20},   ORDER_CLASS={21},       VISIT_DATE={22},            VISIT_NO={23},      PERFORMED_BY={24}, " +
                                                "YPXZBS={25},           TRADE_PRICE={26},       BATCH_CODE={27},            GUID={28},          BATCH_NO={29}, " +
                                                "RECIPETYPE={30},       INSUR_ADULT={31} " + 
                                                "Where CLINIC_NO='{32}' AND ORDER_NO={33} AND ORDER_SUB_NO={34} AND ITEM_NO={35}",
                                                costs.ITEM_CLASS == null ? "NULL" : "'" + costs.ITEM_CLASS + "'",               costs.ITEM_NAME == null ? "NULL" : "'" + costs.ITEM_NAME + "'",                 costs.ITEM_CODE == null ? "NULL" : "'" + costs.ITEM_CODE + "'",                                 costs.ITEM_SPEC == null ? "NULL" : "'" + costs.ITEM_SPEC + "'",             costs.UNITS == null ? "NULL" : "'" + costs.UNITS + "'",
                                                costs.REPETITION == null ? "NULL" : costs.REPETITION.ToString(),                costs.AMOUNT == null ? "NULL" : costs.AMOUNT.ToString(),                        costs.CLASS_ON_RCPT == null ? "NULL" : "'" + costs.CLASS_ON_RCPT + "'",                         costs.COSTS == null ? "NULL" : costs.COSTS.ToString(),                      costs.CHARGES == null ? "NULL" : costs.CHARGES.ToString(),
                                                costs.RCPT_NO == null ? "NULL" : "'" + costs.RCPT_NO + "'",                     costs.CHARGE_INDICATOR == null ? "NULL" : costs.CHARGE_INDICATOR.ToString(),    costs.CLASS_ON_RECKONING == null ? "NULL" : "'" + costs.CLASS_ON_RECKONING.ToString() + "'",    costs.SUBJ_CODE == null ? "NULL" : "'" + costs.SUBJ_CODE.ToString() + "'",  costs.PRICE_QUOTIETY == null ? "NULL" : costs.PRICE_QUOTIETY.ToString(),
                                                costs.ITEM_PRICE == null ? "NULL" : costs.ITEM_PRICE.ToString(),                costs.INSURANCE_FLAG == null ? "NULL" : "'" + costs.INSURANCE_FLAG + "'",       costs.CLXZBS == null ? "NULL" : "'" + costs.CLXZBS + "'",                                       costs.CLWZM == null ? "NULL" : "'" + costs.CLWZM.ToString() + "'",          costs.HIS_UNIT_CODE == null ? "NULL" : "'" + costs.HIS_UNIT_CODE + "'",
                                                costs.OUTP_SERIAL_NO == null ? "NULL" : "'" + costs.OUTP_SERIAL_NO + "'",       costs.ORDER_CLASS == null ? "NULL" : "'" + costs.ORDER_CLASS + "'",             "TO_DATE('" + costs.VISIT_DATE.ToString() + "', 'yyyy/MM/dd HH24:mi:ss')",                      costs.VISIT_NO == null ? "NULL" : costs.VISIT_NO.ToString(),                costs.PERFORMED_BY == null ? "NULL" : "'" + costs.PERFORMED_BY + "'",
                                                costs.YPXZBS == null ? "NULL" : "'" + costs.YPXZBS.ToString() + "'",            costs.TRADE_PRICE == null ? "NULL" : costs.TRADE_PRICE.ToString(),              costs.BATCH_CODE == null ? "NULL" : "'" + costs.BATCH_CODE + "'",                               costs.GUID == null ? "NULL" : "'" + costs.GUID + "'",                       costs.BATCH_NO == null ? "NULL" : "'" + costs.BATCH_NO + "'",
                                                costs.RECIPETYPE == null ? "NULL" : "'" + costs.RECIPETYPE + "'",               costs.INSUR_ADULT == null ? "NULL" : "'" + costs.INSUR_ADULT + "'",
                                                costs.CLINIC_NO, costs.ORDER_NO.ToString(), costs.ORDER_SUB_NO.ToString(), costs.ITEM_NO.ToString());
            sqlLst.Add(sqlor1);

            dbParametersLst.Add(new DbParameter[] { });
        }

        public void SetUpdateOrderSql(ref List<string> sqlLst, ref List<DbParameter[]> dbParametersLst, OUTP_ORDERS_STANDARD order)
        {
            string sqlor = string.Format("Update OUTP_ORDERS_STANDARD Set  " +
                                                "SIGNATURE_NO={0}, USAGE_DESC={1}, DIAGNOSIS_DESC={2}, ORDER_CLASS={3}, ORDER_TEXT={4}, " +
                                                "ORDER_CODE={5}, DOSAGE={6}, DOSAGE_UNITS={7}, ADMINISTRATION={8}, FREQUENCY={9}, " +
                                                "SKIN_FLAG={10}, PRESC_PSNO={11}, AMOUNT={12}, COSTS={13}, CHARGES={14}, " +
                                                "SPLIT_FLAG={15}, ITEM_SPEC={16}, FIRM_ID={17}, REPETITION={18}, PRESC_ATTR={19}, " +
                                                "UNITS={20}, ABIDANCE={21}, PERFORM_TIMES={22}, FREQ_DETAIL={23}, BATCH_NO={24}, " +
                                                "DOSE_PER_UNIT={25}, TRADE_PRICE={26}, BATCH_CODE={27}, GUID={28}, ITEM_PRICE={29}, " +
                                                "BJCA_CN={30}, BJCA_VALUE={31}, BJCA_TIME={32}, DECOCTION={33}, COUNT_PER_REPETITION={34}, " +
                                                "VISIT_NO={35}, VISIT_DATE={36}, RECIPETYPE={37}, HERBAL_EXTRACT_JUICE_QUANTITY={38}, HERBAL_DOSAGE={39}," +
                                                "HERBAL_RULES_OF_TREATMENT={40}, CPRESC_NAME={41}, SPECIAL_REQUEST={42} " +
                                                "Where CLINIC_NO='{43}' AND ORDER_NO={44} AND ORDER_SUB_NO={45}",
                                                order.SIGNATURE_NO==null ? "NULL" : order.SIGNATURE_NO.ToString(),                              order.USAGE_DESC==null ? "NULL" : "'" + order.USAGE_DESC + "'",             order.DIAGNOSIS_DESC==null ? "NULL" : "'" + order.DIAGNOSIS_DESC + "'", order.ORDER_CLASS==null ? "NULL" : "'" + order.ORDER_CLASS + "'",                                       order.ORDER_TEXT==null ? "NULL" : "'" + order.ORDER_TEXT + "'",
                                                order.ORDER_CODE==null ? "NULL" : "'" + order.ORDER_CODE + "'",                                 order.DOSAGE==null ? "NULL" : order.DOSAGE.ToString(),                      order.DOSAGE_UNITS==null ? "NULL" : "'" + order.DOSAGE_UNITS + "'",     order.ADMINISTRATION==null ? "NULL" : "'" + order.ADMINISTRATION + "'",                                 order.FREQUENCY==null ? "NULL" : "'" + order.FREQUENCY + "'",
                                                order.SKIN_FLAG==null ? "NULL" : order.SKIN_FLAG.ToString(),                                    order.PRESC_PSNO==null ? "NULL" : order.PRESC_PSNO.ToString(),              order.AMOUNT==null ? "NULL" : order.AMOUNT.ToString(),                  order.COSTS==null ? "NULL" : order.COSTS.ToString(),                                                    order.CHARGES==null ? "NULL" : order.CHARGES.ToString(),
                                                order.SPLIT_FLAG==null ? "NULL" : order.SPLIT_FLAG.ToString(),                                  order.ITEM_SPEC==null ? "NULL" : "'" + order.ITEM_SPEC + "'",               order.FIRM_ID==null ? "NULL" : "'" + order.FIRM_ID + "'",               order.REPETITION==null ? "NULL" : order.REPETITION.ToString(),                                          order.PRESC_ATTR==null ? "NULL" : "'" + order.PRESC_ATTR + "'",
                                                order.UNITS==null ? "NULL" : "'" + order.UNITS + "'",                                           order.ABIDANCE==null ? "NULL" : order.ABIDANCE.ToString(),                  order.PERFORM_TIMES==null ? "NULL" : order.PERFORM_TIMES.ToString(),    order.FREQ_DETAIL==null ? "NULL" : "'" + order.FREQ_DETAIL + "'",                                       order.BATCH_NO==null ? "NULL" : "'" + order.BATCH_NO + "'",
                                                order.DOSE_PER_UNIT==null ? "NULL" : order.DOSE_PER_UNIT.ToString(),                            order.TRADE_PRICE==null ? "NULL" : order.TRADE_PRICE.ToString(),            order.BATCH_CODE==null ? "NULL" : "'" + order.BATCH_CODE + "'",         order.GUID==null ? "NULL" : "'" + order.GUID + "'",                                                     order.ITEM_PRICE==null ? "NULL" : order.ITEM_PRICE.ToString(),
                                                order.BJCA_CN==null ? "NULL" : "'" + order.BJCA_CN + "'",                                       order.BJCA_VALUE==null ? "NULL" : "'" + order.BJCA_VALUE + "'",             order.BJCA_TIME==null ? "NULL" : "'" + order.BJCA_TIME + "'",           order.DECOCTION==null ? "NULL" : "'" + order.DECOCTION + "'",                                           order.COUNT_PER_REPETITION==null ? "NULL" : order.COUNT_PER_REPETITION.ToString(),
                                                order.VISIT_NO==null ? "NULL" : order.VISIT_NO.ToString(),                                      "TO_DATE('" + order.VISIT_DATE.ToString() + "', 'yyyy/MM/dd HH24:mi:ss')",  order.RECIPETYPE==null ? "NULL" : "'" + order.RECIPETYPE + "'",         order.HERBAL_EXTRACT_JUICE_QUANTITY==null ? "NULL" : order.HERBAL_EXTRACT_JUICE_QUANTITY.ToString(),    order.HERBAL_DOSAGE==null ? "NULL" : order.HERBAL_DOSAGE.ToString(),
                                                order.HERBAL_RULES_OF_TREATMENT==null ? "NULL" : "'" + order.HERBAL_RULES_OF_TREATMENT + "'",   order.CPRESC_NAME==null ? "NULL" : "'" + order.CPRESC_NAME + "'",           order.SPECIAL_REQUEST==null ? "NULL" : "'" + order.SPECIAL_REQUEST + "'",
                                                order.CLINIC_NO,                                                                                order.ORDER_NO,                                                             order.ORDER_SUB_NO);
            sqlLst.Add(sqlor);

            dbParametersLst.Add(new DbParameter[]{});
        }

        public int ValidateOrderChargeIndicator(OUTP_ORDERS_STANDARD order)
        {
            string sql = "SELECT nvl(CHARGE_INDICATOR,0) from OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD where HIS_UNIT_CODE =:HIS_UNIT_CODE  and  CLINIC_NO =:CLINIC_NO and ORDER_NO = :ORDER_NO and ORDER_SUB_NO =:ORDER_SUB_NO";
            List<DbParameter> paraList = new List<DbParameter>()
            {
                db.CreateDbParameter(":HIS_UNIT_CODE",SystemParm.HisUnitCode),
                db.CreateDbParameter(":CLINIC_NO",order.CLINIC_NO),
                db.CreateDbParameter(":ORDER_NO",order.ORDER_NO),
                db.CreateDbParameter(":ORDER_SUB_NO",order.ORDER_SUB_NO),
            };
            object o = db.GetSingleValue(sql, paraList);
            if (o != null)
            {
                return Convert.ToInt32(o);
            }
            return 0;
        }
    }
}
