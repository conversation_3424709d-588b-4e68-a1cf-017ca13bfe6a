using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;

namespace Tjhis.Presdisp.Station.Common
{
    /// <summary>
    /// 库存操作验证和日志记录工具
    /// 创建日期：2025-01-17
    /// 目的：提供统一的库存操作验证和日志记录功能
    /// </summary>
    public static class StockOperationValidator
    {
        private static readonly string LogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "Client", "LOG", "exLOG");
        
        /// <summary>
        /// 验证库存扣减操作是否安全
        /// </summary>
        /// <param name="drugCode">药品代码</param>
        /// <param name="drugName">药品名称</param>
        /// <param name="storage">药房代码</param>
        /// <param name="batchNo">批号</param>
        /// <param name="requiredQuantity">需要扣减的数量</param>
        /// <param name="currentQuantity">当前库存数量</param>
        /// <returns>验证结果</returns>
        public static StockOperationResult ValidateStockDeduction(string drugCode, string drugName, 
            string storage, string batchNo, decimal requiredQuantity, decimal currentQuantity)
        {
            try
            {
                WriteOperationLog($"[库存扣减验证] 开始验证 - 药品：{drugName}({drugCode})，批号：{batchNo}，当前库存：{currentQuantity}，需要扣减：{requiredQuantity}");

                if (requiredQuantity <= 0)
                {
                    WriteOperationLog($"[库存扣减验证] 验证失败 - 扣减数量必须大于0，实际值：{requiredQuantity}");
                    return new StockOperationResult
                    {
                        IsValid = false,
                        ErrorMessage = "扣减数量必须大于0",
                        OperationType = "库存扣减验证"
                    };
                }

                if (currentQuantity < requiredQuantity)
                {
                    WriteOperationLog($"[库存扣减验证] 验证失败 - 库存不足，当前库存：{currentQuantity}，需要扣减：{requiredQuantity}");
                    return new StockOperationResult
                    {
                        IsValid = false,
                        ErrorMessage = $"库存不足！当前库存：{currentQuantity}，需要扣减：{requiredQuantity}",
                        OperationType = "库存扣减验证"
                    };
                }

                decimal remainingStock = currentQuantity - requiredQuantity;
                WriteOperationLog($"[库存扣减验证] 验证通过 - 扣减后剩余库存：{remainingStock}");

                return new StockOperationResult
                {
                    IsValid = true,
                    ErrorMessage = "验证通过",
                    OperationType = "库存扣减验证",
                    RemainingStock = remainingStock
                };
            }
            catch (Exception ex)
            {
                WriteOperationLog($"[库存扣减验证] 验证异常 - {ex.Message}");
                return new StockOperationResult
                {
                    IsValid = false,
                    ErrorMessage = $"验证过程发生异常：{ex.Message}",
                    OperationType = "库存扣减验证"
                };
            }
        }

        /// <summary>
        /// 记录库存操作结果
        /// </summary>
        /// <param name="operationType">操作类型</param>
        /// <param name="drugCode">药品代码</param>
        /// <param name="drugName">药品名称</param>
        /// <param name="storage">药房代码</param>
        /// <param name="batchNo">批号</param>
        /// <param name="quantity">操作数量</param>
        /// <param name="oldQuantity">操作前库存</param>
        /// <param name="newQuantity">操作后库存</param>
        /// <param name="isSuccess">操作是否成功</param>
        /// <param name="errorMessage">错误信息</param>
        public static void LogStockOperation(string operationType, string drugCode, string drugName,
            string storage, string batchNo, decimal quantity, decimal oldQuantity, decimal newQuantity,
            bool isSuccess, string errorMessage = "")
        {
            try
            {
                string status = isSuccess ? "成功" : "失败";
                string logMessage = $"[{operationType}] {status} - " +
                    $"药品：{drugName}({drugCode})，" +
                    $"药房：{storage}，" +
                    $"批号：{batchNo}，" +
                    $"操作数量：{quantity}，" +
                    $"操作前库存：{oldQuantity}，" +
                    $"操作后库存：{newQuantity}";

                if (!isSuccess && !string.IsNullOrEmpty(errorMessage))
                {
                    logMessage += $"，错误信息：{errorMessage}";
                }

                WriteOperationLog(logMessage);

                // 如果操作失败或导致负库存，记录到专门的错误日志
                if (!isSuccess || newQuantity < 0)
                {
                    WriteErrorLog($"[库存异常] {logMessage}");
                }
            }
            catch (Exception ex)
            {
                WriteOperationLog($"[日志记录异常] 记录库存操作日志时发生异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 写入操作日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private static void WriteOperationLog(string message)
        {
            try
            {
                string fileName = $"库存操作日志_{DateTime.Now:yyyyMMdd}.log";
                string fullPath = Path.Combine(LogPath, fileName);
                
                // 确保日志目录存在
                Directory.CreateDirectory(LogPath);
                
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}{Environment.NewLine}";
                File.AppendAllText(fullPath, logEntry, Encoding.UTF8);
            }
            catch
            {
                // 日志写入失败不应该影响主要业务流程
            }
        }

        /// <summary>
        /// 写入错误日志
        /// </summary>
        /// <param name="message">错误消息</param>
        private static void WriteErrorLog(string message)
        {
            try
            {
                string fileName = $"库存异常日志_{DateTime.Now:yyyyMMdd}.log";
                string fullPath = Path.Combine(LogPath, fileName);
                
                // 确保日志目录存在
                Directory.CreateDirectory(LogPath);
                
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}{Environment.NewLine}";
                File.AppendAllText(fullPath, logEntry, Encoding.UTF8);
            }
            catch
            {
                // 日志写入失败不应该影响主要业务流程
            }
        }

        /// <summary>
        /// 检查数据库操作结果
        /// </summary>
        /// <param name="affectedRows">受影响的行数</param>
        /// <param name="operationType">操作类型</param>
        /// <param name="drugName">药品名称</param>
        /// <param name="expectedRows">期望的受影响行数</param>
        /// <returns>检查结果</returns>
        public static StockOperationResult CheckDatabaseOperationResult(int affectedRows, string operationType, 
            string drugName, int expectedRows = 1)
        {
            try
            {
                if (affectedRows == expectedRows)
                {
                    WriteOperationLog($"[数据库操作检查] {operationType}成功 - 药品：{drugName}，受影响行数：{affectedRows}");
                    return new StockOperationResult
                    {
                        IsValid = true,
                        ErrorMessage = "操作成功",
                        OperationType = operationType
                    };
                }
                else if (affectedRows == 0)
                {
                    string errorMsg = $"{operationType}失败 - 可能是库存不足或记录不存在";
                    WriteOperationLog($"[数据库操作检查] {errorMsg} - 药品：{drugName}，受影响行数：{affectedRows}");
                    return new StockOperationResult
                    {
                        IsValid = false,
                        ErrorMessage = errorMsg,
                        OperationType = operationType
                    };
                }
                else
                {
                    string errorMsg = $"{operationType}异常 - 受影响行数不符合预期";
                    WriteOperationLog($"[数据库操作检查] {errorMsg} - 药品：{drugName}，受影响行数：{affectedRows}，期望行数：{expectedRows}");
                    return new StockOperationResult
                    {
                        IsValid = false,
                        ErrorMessage = errorMsg,
                        OperationType = operationType
                    };
                }
            }
            catch (Exception ex)
            {
                WriteOperationLog($"[数据库操作检查] 检查过程发生异常 - {ex.Message}");
                return new StockOperationResult
                {
                    IsValid = false,
                    ErrorMessage = $"检查过程发生异常：{ex.Message}",
                    OperationType = operationType
                };
            }
        }
    }

    /// <summary>
    /// 库存操作结果
    /// </summary>
    public class StockOperationResult
    {
        /// <summary>
        /// 操作是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public string OperationType { get; set; }

        /// <summary>
        /// 剩余库存
        /// </summary>
        public decimal RemainingStock { get; set; }
    }
}
