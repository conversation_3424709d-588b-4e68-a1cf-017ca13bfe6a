﻿using NM_Service.NMService;
using System;
using System.Data;
using System.IO;
using System.Runtime.ExceptionServices;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace TjhisInterfaceInsurance
{
    public class ClassInsurPublic
    {
        #region 老代码

        public static DataTable GetTjInsuranceDict(string interfacecode, string insur_type)
        {
            DataTable dt_data = null;
            string sql = "select * from insurance.tj_insurance_dict where interfacecode='" + interfacecode + "' and insur_type='" + insur_type + "' ";
            dt_data = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];


            return dt_data;
        }

        /// <summary>
        /// 将json转换为DataTable
        /// </summary>
        /// <param name="strJson">得到的json</param>
        /// <returns></returns>
        public static DataTable JsonToDT(string strJson)
        {
            //转换json格式
            strJson = strJson.Replace(",\"", "*\"").Replace("\":", "\"#").ToString();
            //取出表名   
            var rg = new Regex(@"(?<={)[^:]+(?=:\[)", RegexOptions.IgnoreCase);
            string strName = rg.Match(strJson).Value;
            DataTable tb = null;
            //去除表名   
            strJson = strJson.Substring(strJson.IndexOf("[") + 1);
            strJson = strJson.Substring(0, strJson.IndexOf("]"));

            //获取数据   
            rg = new Regex(@"(?<={)[^}]+(?=})");
            MatchCollection mc = rg.Matches(strJson);
            for (int i = 0; i < mc.Count; i++)
            {
                string strRow = mc[i].Value;
                string[] strRows = strRow.Split('*');

                //创建表   
                if (tb == null)
                {
                    tb = new DataTable();
                    tb.TableName = strName;
                    foreach (string str in strRows)
                    {
                        var dc = new DataColumn();
                        string[] strCell = str.Split('#');

                        if (strCell[0].Substring(0, 1) == "\"")
                        {
                            int a = strCell[0].Length;
                            dc.ColumnName = strCell[0].Substring(1, a - 2);
                        }
                        else
                        {
                            dc.ColumnName = strCell[0];
                        }
                        tb.Columns.Add(dc);
                    }
                    tb.AcceptChanges();
                }

                //增加内容   
                DataRow dr = tb.NewRow();
                for (int r = 0; r < strRows.Length; r++)
                {
                    dr[r] = strRows[r].Split('#')[1].Trim().Replace("，", ",").Replace("：", ":").Replace("\"", "");
                }
                tb.Rows.Add(dr);
                tb.AcceptChanges();
            }

            return tb;
        }

        #region 根据各明细的item_code 取价表的material_code
        public static bool GetPriceListMaterialcode(string itemclass, string itemcode, string itemspec, string tunits, ref string tmaterialcode)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("select ITEM_UNIT_CODE from current_price_list a ");
            sb.Append(" where a.item_class = '" + itemclass + "' and a.item_code = '" + itemcode + "' ");
            sb.Append("and a.item_spec = '" + itemspec + "' and a.units = '" + tunits + "'");
            ServerPublicClient service = new ServerPublicClient();
            DataTable dt = service.GetDataBySql(sb.ToString()).Tables[0];
            if (dt.Rows.Count > 0)
            {
                tmaterialcode = dt.Rows[0][0].ToString();
                if (string.IsNullOrEmpty(tmaterialcode)) tmaterialcode = itemcode;
                dt.Dispose();
                return true;
            }

            return false;
        }
        #endregion

        #endregion
        

        private static Action<string> _logAction = WriteLog; // 默认日志方法
        //将日志记录方法托管出去，让日志部分跟灵活
        public static void Configure(Action<string> logAction)
        {
            _logAction = logAction ?? WriteLog; // 确保至少有一个默认的日志方法
        }
        /// <summary>       
        /// 默认写日志
        /// </summary>        
        public static void WriteLog(string str)
        {
            try
            {
                DateTime d = DateTime.Now;
                string path = AppDomain.CurrentDomain.BaseDirectory + "PTYBLOG/";
                string fileName = d.ToString("yyyy-MM-dd-HH") + ".txt";

                if (!Directory.Exists(path))
                    Directory.CreateDirectory(path);

                using (StreamWriter file = new StreamWriter(path + fileName, true))
                {
                    if (string.IsNullOrEmpty(str))
                        file.WriteLine("");
                    else
                    {
                        file.WriteLine(d.ToString("MM-dd HH:mm:ss") + " ==>> " + str);                        
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("writeLog方法异常" + e.Message);

            }

        }

        #region 动态库封装
        private static object lockObj = new object();
        /// <summary>
        ///  1.接口初始化 
        /// </summary>
        /// <returns>返回值 0 成功  -1 失败</returns>
        [DllImport(@"SiInterface_hsf.dll", EntryPoint = "INIT", SetLastError = false, CharSet = CharSet.Auto, ExactSpelling = false,
            CallingConvention = CallingConvention.StdCall)]
        public static extern int INIT(IntPtr OutputData);
        /// <summary>
        /// 2.业务处理
        /// </summary>
        /// <param name="inputData">入参：交易类别代码+'|'+交易特定输入数据</param>
        /// <param name="outputData">出参：业务执行代码+'|'+业务数据（或者出错信息）</param>
        /// <returns>返回值 0 成功 返回 <0 失败
        /// 错误，包括系统级别错误(网络、主机、数据库)和业务级别错 误，
        /// 系统级别错误由动态库将错误信息写入输出参数，业务级别错误由后台通过输出参数提示错误信息。其中：
        /// -1－系统级别错误， HIS 系统提示错误信息后，需要进行冲正等后续 业务操作处理；
        /// -2－业务处理错误， HIS 系统直接将输出参数的错误信息提示给操作员即可
        /// </returns>       
        /// <summary>
        /// 交易函数
        /// </summary>
        [DllImport(@"SiInterface_hsf.dll", EntryPoint = "BUSINESS_HANDLE", SetLastError = false, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern int BUSINESS_HANDLE([MarshalAs(UnmanagedType.LPArray)] byte[] InputData, [MarshalAs(UnmanagedType.LPArray)] byte[] OutputData);

       [HandleProcessCorruptedStateExceptions]
        [System.Security.SecurityCritical]
        public static int Init(ref StringBuilder outputData)
        {
            lock (lockObj)
            {
                outputData = new StringBuilder();
                //分配了50,000字节的非托管内存，用于存储一些错误信息。
                IntPtr errinfo = Marshal.AllocHGlobal(50000);
                int ret = INIT(errinfo);
                //将分配的非托管内存内容转换为一个ANSI字符串，Marshal.PtrToStringAnsi函数用于将指向字符数组的指针转换为.NET中的字符串。
                var strOut = Marshal.PtrToStringAnsi(errinfo);
                outputData.Append(strOut);
                //释放之前分配的非托管内存，以避免内存泄漏。
                Marshal.FreeHGlobal(errinfo);
                return ret;
            }
        }
        [HandleProcessCorruptedStateExceptions]
        [System.Security.SecurityCritical]
        public static int BusinessHandle(string inputData, ref StringBuilder outputData)
        {
            lock (lockObj)
            {
                try
                {
                    outputData.Clear();
                    byte[] outStr = new byte[8000000];

                    int ret = ClassInsurPublic.BUSINESS_HANDLE(Encoding.Default.GetBytes(inputData), outStr);
                    var strOut = Encoding.Default.GetString(outStr).Trim().Replace("\0", "");
                    outputData.Append(strOut);
                    return ret;
                }
                catch (Exception ex)
                {
                    string str = ex.Message;
                    return -1;
                }
                
            }
        }


        #endregion
        


    }
}
